# نظام الحفظ الديناميكي الذكي
## Dynamic Smart Saving System

### 🎯 **الهدف من النظام**
تطوير نظام حفظ ديناميكي وعملي مرتبط بحالة الشحنة، بحيث يتم تحديد الحقول والأقسام المطلوبة تلقائياً حسب حالة الشحنة الحالية.

### 🚀 **المزايا الرئيسية**

#### **📊 حفظ ذكي حسب الحالة:**
- **تحت الطلب**: يكفي ملء القسم الأساسي فقط
- **في الانتظار**: القسم الأساسي + تاريخ الشحن
- **مؤكدة**: الأساسي + الأصناف + تاريخ الوصول المتوقع
- **تم الشحن**: الأساسي + الأصناف + الشحن + الموانئ
- **في الطريق**: معظم الأقسام للتتبع الكامل
- **تم التسليم**: جميع الأقسام مكتملة

#### **🎨 واجهة تفاعلية:**
- **أزرار التنقل الذكية**: تظهر بالأخضر للأقسام المطلوبة
- **مؤشر الأقسام**: يعرض عدد الأقسام المطلوبة في شريط الحالة
- **رسائل نجاح مخصصة**: تعرض الأقسام المحفوظة
- **زر معلومات الحالة**: لعرض تفاصيل المتطلبات

### 🔧 **التحديثات المنفذة**

#### **1. نظام التحقق الديناميكي**

##### **دالة تحديد الحقول المطلوبة:**
```python
def get_required_fields_by_status(self, status):
    """تحديد الحقول المطلوبة حسب حالة الشحنة"""
    basic_required = {
        'shipment_number': 'رقم الشحنة',
        'supplier_id': 'المورد',
        'status': 'حالة الشحنة'
    }
    
    status_requirements = {
        'تحت الطلب': {**basic_required},
        'في الانتظار': {
            **basic_required,
            'shipment_date': 'تاريخ الشحن'
        },
        'مؤكدة': {
            **basic_required,
            'shipment_date': 'تاريخ الشحن',
            'expected_arrival_date': 'تاريخ الوصول المتوقع'
        }
        # ... باقي الحالات
    }
```

##### **دالة تحديد الأقسام المطلوبة:**
```python
def get_required_sections_by_status(self, status):
    """تحديد الأقسام المطلوبة حسب حالة الشحنة"""
    section_requirements = {
        'تحت الطلب': [0],  # فقط القسم الأساسي
        'في الانتظار': [0],  # فقط القسم الأساسي
        'مؤكدة': [0, 1],  # الأساسي + الأصناف
        'تم الشحن': [0, 1, 2],  # الأساسي + الأصناف + الشحن
        # ... باقي الحالات
    }
```

#### **2. إدارة رؤية الأقسام**

##### **تحديث رؤية الأقسام:**
```python
def update_sections_visibility(self):
    """تحديث رؤية الأقسام بناءً على حالة الشحنة"""
    current_status = self.form_vars.get('status').get().strip()
    required_sections = self.get_required_sections_by_status(current_status)
    
    # إخفاء/إظهار الأقسام حسب المتطلبات
    for i, section in enumerate(self.sections):
        if i in required_sections:
            section.pack(fill='both', expand=True)
        else:
            section.pack_forget()
```

##### **تحديث أزرار التنقل:**
```python
def update_navigation_buttons(self):
    """تحديث أزرار التنقل بناءً على الأقسام المطلوبة"""
    for i, button in enumerate(self.nav_buttons):
        if i in required_sections:
            button.configure(state='normal', bg=COLORS['secondary'])
            # إضافة علامة ✓ للأقسام المطلوبة
        else:
            button.configure(state='disabled', bg=COLORS['disabled'])
```

#### **3. التحقق الذكي من البيانات**

##### **التحقق المخصص:**
```python
def validate_form(self):
    """التحقق من صحة النموذج بناءً على حالة الشحنة"""
    current_status = self.form_vars.get('status').get().strip()
    required_fields = self.get_required_fields_by_status(current_status)
    
    # التحقق فقط من الحقول المطلوبة للحالة الحالية
    for field, label in required_fields.items():
        value = self.form_vars.get(field).get().strip()
        if not value:
            errors.append(f"حقل {label} مطلوب لحالة '{current_status}'")
```

#### **4. واجهة المستخدم المحسنة**

##### **زر معلومات الحالة:**
```python
# زر معلومات الحالة
status_info_btn = create_simple_rtl_button(
    actions_frame,
    text="ℹ️ معلومات الحالة",
    button_type="info",
    command=self.show_status_info
)
```

##### **مؤشر الأقسام في شريط الحالة:**
```python
# مؤشر الأقسام المطلوبة
self.sections_indicator = create_simple_rtl_label(
    status_frame,
    text="",
    font=('Segoe UI', 10, 'normal'),
    bg=COLORS['light'],
    fg=COLORS['info']
)
```

### 📊 **جدول متطلبات الحالات**

| الحالة | الأقسام المطلوبة | الحقول الإضافية المطلوبة |
|--------|------------------|---------------------------|
| **تحت الطلب** | الأساسي فقط | رقم الشحنة، المورد، الحالة |
| **في الانتظار** | الأساسي فقط | + تاريخ الشحن |
| **مؤكدة** | الأساسي + الأصناف | + تاريخ الوصول المتوقع |
| **تم الشحن** | الأساسي + الأصناف + الشحن | + الموانئ + شركة الشحن |
| **في الطريق** | 5 أقسام | + رقم الحاوية + رقم التتبع |
| **وصلت** | 6 أقسام | + تاريخ الوصول الفعلي |
| **في الجمارك** | 7 أقسام | + بوليصة الشحن |
| **تم التسليم** | جميع الأقسام | + القيمة الإجمالية |

### 🎮 **التفاعل مع النظام**

#### **🔄 تغيير الحالة:**
1. **اختيار حالة جديدة** من القائمة المنسدلة
2. **تحديث تلقائي** للأقسام المطلوبة
3. **تحديث أزرار التنقل** بالألوان المناسبة
4. **تحديث مؤشر الأقسام** في شريط الحالة

#### **💾 عملية الحفظ:**
1. **التحقق الذكي** من الحقول المطلوبة فقط
2. **حفظ البيانات** المتاحة
3. **رسالة نجاح مخصصة** تعرض الأقسام المحفوظة
4. **إمكانية إضافة المزيد** لاحقاً

#### **ℹ️ معلومات الحالة:**
- **زر معلومات الحالة**: يعرض تفاصيل المتطلبات
- **اختصار F2**: للوصول السريع للمعلومات
- **مؤشر بصري**: في شريط الحالة

### 🎯 **فوائد النظام الجديد**

#### **📈 للمستخدمين:**
- ✅ **سهولة الاستخدام**: لا حاجة لملء جميع الحقول
- ✅ **وضوح المتطلبات**: معرفة ما هو مطلوب بالضبط
- ✅ **مرونة في العمل**: حفظ تدريجي حسب تقدم الشحنة
- ✅ **توفير الوقت**: التركيز على المهم فقط

#### **💼 للإدارة:**
- ✅ **بيانات دقيقة**: حسب مرحلة الشحنة
- ✅ **تتبع أفضل**: لحالة كل شحنة
- ✅ **كفاءة أعلى**: في إدخال البيانات
- ✅ **مرونة في العمليات**: تدريجية ومنظمة

#### **🔧 للنظام:**
- ✅ **أداء محسن**: عدم معالجة بيانات غير ضرورية
- ✅ **تناسق البيانات**: حسب مرحلة الشحنة
- ✅ **سهولة الصيانة**: قواعد واضحة ومنظمة
- ✅ **قابلية التوسع**: إضافة حالات جديدة بسهولة

### ⌨️ **اختصارات لوحة المفاتيح الجديدة**

| الاختصار | الوظيفة |
|----------|---------|
| **F2** | عرض معلومات الحالة والأقسام المطلوبة |
| **Ctrl+S** | حفظ الشحنة (مع التحقق الذكي) |
| **F1** | المساعدة (محدثة بالمعلومات الجديدة) |

### 🔮 **مثال عملي**

#### **سيناريو: شحنة جديدة "تحت الطلب"**

1. **إنشاء شحنة جديدة**
2. **اختيار حالة "تحت الطلب"**
3. **ملء البيانات الأساسية فقط:**
   - رقم الشحنة
   - اختيار المورد
   - تأكيد الحالة
4. **الحفظ بنجاح** ✅
5. **رسالة تأكيد:** "تم حفظ الشحنة بحالة 'تحت الطلب' - الأقسام المحفوظة: المعلومات الأساسية"

#### **تطوير الشحنة لاحقاً:**
1. **تغيير الحالة إلى "مؤكدة"**
2. **ظهور قسم الأصناف** تلقائياً
3. **إضافة الأصناف المطلوبة**
4. **حفظ محدث** مع الأقسام الجديدة

### 🎉 **النتيجة النهائية**

نظام حفظ ديناميكي وذكي يوفر:
- **مرونة كاملة** في إدخال البيانات
- **وضوح تام** للمتطلبات
- **كفاءة عالية** في العمل
- **تجربة مستخدم ممتازة**

**🚀 الآن يمكن للمستخدمين:**
- حفظ شحنة "تحت الطلب" بالبيانات الأساسية فقط
- تطوير البيانات تدريجياً مع تقدم الشحنة
- معرفة المتطلبات بوضوح في كل مرحلة
- العمل بكفاءة أكبر ووقت أقل
