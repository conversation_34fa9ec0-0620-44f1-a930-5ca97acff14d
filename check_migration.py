#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من نجاح عملية النقل
Check migration success
"""

import sqlite3

def check_migration():
    """التحقق من نجاح عملية النقل"""
    
    print("🔍 التحقق من نجاح عملية النقل...")
    
    # الاتصال بقاعدة البيانات الجديدة
    conn = sqlite3.connect('database/shipment_system.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # عد الشحنات
        cursor.execute("SELECT COUNT(*) as count FROM advanced_shipments")
        count = cursor.fetchone()['count']
        print(f"📊 عدد الشحنات في قاعدة البيانات الجديدة: {count}")
        
        # عرض أول 3 شحنات
        cursor.execute("SELECT shipment_number, customer_name, supplier_name FROM advanced_shipments LIMIT 3")
        shipments = cursor.fetchall()
        
        print("\n📦 أول 3 شحنات:")
        for shipment in shipments:
            print(f"  - {shipment['shipment_number']}: {shipment['customer_name']} ({shipment['supplier_name']})")
        
        print("\n✅ تم التحقق بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    check_migration()
