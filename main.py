#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام متابعة شحنات الموردين
Supplier Shipment Tracking System

الملف الرئيسي لتشغيل البرنامج
Main application entry point
"""

import os
import sys
import logging
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# إعداد نظام السجلات
from config.config import LOGGING_CONFIG, get_logs_path

def setup_logging():
    """إعداد نظام السجلات"""
    try:
        logs_path = get_logs_path()
        log_file = os.path.join(logs_path, 'system.log')
        
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG['level']),
            format=LOGGING_CONFIG['format'],
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logging.info("تم تشغيل النظام بنجاح")
        
    except Exception as e:
        print(f"خطأ في إعداد نظام السجلات: {e}")

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_modules = [
        'tkinter',
        'sqlite3',
        'datetime',
        'hashlib',
        'os',
        'sys'
    ]
    
    optional_modules = [
        'pandas',
        'openpyxl',
        'reportlab',
        'PIL'
    ]
    
    missing_required = []
    missing_optional = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_required.append(module)
    
    for module in optional_modules:
        try:
            __import__(module)
        except ImportError:
            missing_optional.append(module)
    
    if missing_required:
        error_msg = f"المكتبات التالية مطلوبة ولكنها غير مثبتة:\n{', '.join(missing_required)}"
        messagebox.showerror("خطأ", error_msg)
        return False
    
    if missing_optional:
        warning_msg = f"المكتبات التالية اختيارية ولكنها غير مثبتة (قد تؤثر على بعض الوظائف):\n{', '.join(missing_optional)}"
        messagebox.showwarning("تحذير", warning_msg)
    
    return True

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        from database.database_manager import db_manager
        logging.info("تم تهيئة قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        error_msg = f"خطأ في تهيئة قاعدة البيانات: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("خطأ", error_msg)
        return False

def create_backup_on_startup():
    """إنشاء نسخة احتياطية عند بدء التشغيل"""
    try:
        from config.config import BACKUP_CONFIG
        if BACKUP_CONFIG['backup_on_startup']:
            # سيتم تنفيذ النسخ الاحتياطي لاحقاً
            logging.info("تم إنشاء نسخة احتياطية عند بدء التشغيل")
    except Exception as e:
        logging.warning(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

def show_splash_screen():
    """عرض شاشة البداية"""
    splash = tk.Tk()
    splash.title("نظام متابعة الشحنات")
    splash.geometry("400x300")
    splash.configure(bg='#2E86AB')
    splash.resizable(False, False)
    
    # توسيط النافذة
    splash.update_idletasks()
    width = splash.winfo_width()
    height = splash.winfo_height()
    x = (splash.winfo_screenwidth() // 2) - (width // 2)
    y = (splash.winfo_screenheight() // 2) - (height // 2)
    splash.geometry(f'{width}x{height}+{x}+{y}')
    
    # محتوى شاشة البداية
    title_label = tk.Label(
        splash,
        text="نظام متابعة شحنات الموردين",
        font=('Tahoma', 16, 'bold'),
        bg='#2E86AB',
        fg='white'
    )
    title_label.pack(pady=50)
    
    subtitle_label = tk.Label(
        splash,
        text="Supplier Shipment Tracking System",
        font=('Arial', 12),
        bg='#2E86AB',
        fg='white'
    )
    subtitle_label.pack(pady=10)
    
    version_label = tk.Label(
        splash,
        text="الإصدار 1.0.0",
        font=('Tahoma', 10),
        bg='#2E86AB',
        fg='white'
    )
    version_label.pack(pady=20)
    
    loading_label = tk.Label(
        splash,
        text="جاري التحميل...",
        font=('Tahoma', 10),
        bg='#2E86AB',
        fg='white'
    )
    loading_label.pack(pady=30)
    
    # إغلاق شاشة البداية بعد 3 ثوانٍ
    splash.after(3000, splash.destroy)
    splash.mainloop()

def run_login_window():
    """تشغيل نافذة تسجيل الدخول المتقدمة"""
    try:
        from src.simple_advanced_login import show_simple_advanced_login
        return show_simple_advanced_login()

    except Exception as e:
        error_msg = f"خطأ في تشغيل نافذة تسجيل الدخول: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("خطأ", error_msg)
        return False

def run_main_application():
    """تشغيل التطبيق الرئيسي"""
    try:
        from src.main_window import MainWindow
        main_window = MainWindow()
        main_window.run()
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق الرئيسي: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("خطأ", error_msg)

def cleanup_on_exit():
    """تنظيف الموارد عند الخروج"""
    try:
        from config.config import BACKUP_CONFIG
        if BACKUP_CONFIG['backup_on_exit']:
            # سيتم تنفيذ النسخ الاحتياطي لاحقاً
            logging.info("تم إنشاء نسخة احتياطية عند الخروج")
        
        logging.info("تم إغلاق النظام بنجاح")
        
    except Exception as e:
        logging.warning(f"خطأ في تنظيف الموارد: {str(e)}")

def main():
    """الدالة الرئيسية"""
    try:
        # إعداد نظام السجلات
        setup_logging()
        
        # التحقق من المكتبات المطلوبة
        if not check_dependencies():
            return
        
        # تهيئة قاعدة البيانات
        if not initialize_database():
            return
        
        # إنشاء نسخة احتياطية عند بدء التشغيل
        create_backup_on_startup()
        
        # عرض شاشة البداية
        show_splash_screen()
        
        # تشغيل نافذة تسجيل الدخول
        if run_login_window():
            # تشغيل التطبيق الرئيسي
            run_main_application()
        
        # تنظيف الموارد عند الخروج
        cleanup_on_exit()
        
    except KeyboardInterrupt:
        logging.info("تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        error_msg = f"خطأ عام في البرنامج: {str(e)}"
        logging.critical(error_msg)
        messagebox.showerror("خطأ خطير", error_msg)
    finally:
        # التأكد من إغلاق جميع النوافذ
        try:
            import tkinter as tk
            root = tk._default_root
            if root:
                root.quit()
        except:
            pass

if __name__ == "__main__":
    main()
