#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لدالة إضافة المرفقات
Simple Test for Add Attachment Function
"""

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_function():
    """اختبار الدالة"""
    try:
        print("🧪 اختبار دالة إضافة المرفقات...")
        
        # استيراد الكلاس
        from src.fullscreen_shipment_form import FullscreenShipmentForm
        
        # فحص الدوال الموجودة
        methods = [method for method in dir(FullscreenShipmentForm) if 'attachment' in method.lower()]
        print(f"📋 الدوال المتعلقة بالمرفقات: {methods}")
        
        # فحص دالة add_attachment
        if hasattr(FullscreenShipmentForm, 'add_attachment'):
            method = getattr(FullscreenShipmentForm, 'add_attachment')
            import inspect
            sig = inspect.signature(method)
            print(f"🔍 توقيع دالة add_attachment: {sig}")
            print(f"📊 عدد المعاملات: {len(sig.parameters)}")
            for name, param in sig.parameters.items():
                print(f"  - {name}: {param}")
        
        print("✅ الاختبار مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_function()
