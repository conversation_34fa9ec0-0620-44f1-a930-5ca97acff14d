#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار البحث في الأصناف مع دعم RTL كامل
Item Search Dialog with Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS
from src.simple_rtl_components import *

class ItemSearchDialog:
    """نافذة حوار البحث في الأصناف"""
    
    def __init__(self, parent, items_data, main_form):
        self.parent = parent
        self.items_data = items_data or []
        self.main_form = main_form
        self.filtered_items = []
        self.selected_item = None
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات البحث
        self.search_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_items()
        
        # توسيط النافذة
        self.center_window()
        
        # تركيز على حقل البحث
        self.focus_search_field()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🔍 البحث في الأصناف - F9")
        self.root.geometry("800x600")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        
        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.close_dialog)
        
        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Return>', lambda e: self.select_item())
        self.root.bind('<Escape>', lambda e: self.close_dialog())
        self.root.bind('<F9>', lambda e: self.close_dialog())
        self.root.bind('<Double-Button-1>', lambda e: self.select_item())
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = create_simple_rtl_label(
            main_frame,
            text="🔍 البحث في الأصناف والمنتجات",
            font=('Segoe UI', 20, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e', pady=(0, 20))
        
        # منطقة البحث
        search_frame = create_simple_rtl_frame(main_frame)
        search_frame.pack(fill='x', pady=(0, 20))
        
        # الصف الأول - البحث العام
        search_row1 = create_simple_rtl_frame(search_frame)
        search_row1.pack(fill='x', pady=(0, 15))
        
        # حقل البحث العام
        search_label = create_simple_rtl_label(
            search_row1,
            text="🔍 البحث العام:",
            font=('Segoe UI', 14, 'bold')
        )
        search_label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars['general'] = tk.StringVar()
        self.general_search_entry = create_simple_rtl_entry(
            search_row1,
            textvariable=self.search_vars['general'],
            placeholder="ابحث في كود الصنف، اسم الصنف، أو الوصف..."
        )
        self.general_search_entry.configure(font=('Segoe UI', 12, 'normal'))
        self.general_search_entry.pack(fill='x', ipady=10)
        
        # ربط البحث الفوري
        self.search_vars['general'].trace('w', self.on_search_change)
        
        # الصف الثاني - بحث متقدم
        search_row2 = create_simple_rtl_frame(search_frame)
        search_row2.pack(fill='x', pady=(15, 15))
        
        # كود الصنف
        self.create_search_field(search_row2, "🔢 كود الصنف:", 'item_code', width_ratio=1/3)
        
        # اسم الصنف
        self.create_search_field(search_row2, "📦 اسم الصنف:", 'item_name', width_ratio=1/3)
        
        # بلد المنشأ
        self.create_search_combobox(search_row2, "🌍 بلد المنشأ:", 'origin_country', 
                                   values=['', 'السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان', 
                                          'الصين', 'الهند', 'تركيا', 'ألمانيا', 'أمريكا', 'اليابان'], 
                                   width_ratio=1/3)
        
        # أزرار البحث
        buttons_row = create_simple_rtl_frame(search_frame)
        buttons_row.pack(fill='x', pady=(15, 0))
        
        # زر البحث
        search_btn = create_simple_rtl_button(
            buttons_row,
            text="🔍 بحث",
            button_type="primary",
            command=self.perform_search
        )
        search_btn.pack(side='right', padx=5)
        
        # زر مسح
        clear_btn = create_simple_rtl_button(
            buttons_row,
            text="🗑️ مسح",
            button_type="secondary",
            command=self.clear_search
        )
        clear_btn.pack(side='right', padx=5)
        
        # عداد النتائج
        self.results_label = create_simple_rtl_label(
            buttons_row,
            text="",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        self.results_label.pack(side='left', padx=10)
        
        # جدول النتائج
        results_frame = create_simple_rtl_frame(main_frame)
        results_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # عنوان النتائج
        results_title = create_simple_rtl_label(
            results_frame,
            text="📊 نتائج البحث:",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        results_title.pack(anchor='e', pady=(0, 10))
        
        # إنشاء جدول النتائج
        table_frame = create_simple_rtl_frame(results_frame)
        table_frame.pack(fill='both', expand=True)
        
        # تعريف أعمدة الجدول
        columns = ['item_code', 'item_name', 'description', 'quantity', 'unit', 'unit_price', 'total_price']
        column_names = ['كود الصنف', 'اسم الصنف', 'الوصف', 'الكمية', 'الوحدة', 'سعر الوحدة', 'الإجمالي']
        
        # إنشاء Treeview
        self.results_tree = create_simple_rtl_treeview(
            table_frame,
            columns=columns,
            show='tree headings',
            height=15
        )
        
        # تكوين الأعمدة
        for i, (col_id, col_name) in enumerate(zip(columns, column_names)):
            self.results_tree.heading(col_id, text=col_name, anchor='e')
            width = 120 if i < 3 else 80
            self.results_tree.column(col_id, width=width, anchor='e', minwidth=50)
        
        # العمود الرئيسي
        self.results_tree.column('#0', width=30, minwidth=30, anchor='e')
        self.results_tree.heading('#0', text='#', anchor='e')
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.on_result_double_click)
        
        # أزرار التحكم
        control_frame = create_simple_rtl_frame(main_frame)
        control_frame.pack(fill='x', pady=(20, 0))
        
        # زر الاختيار
        select_btn = create_simple_rtl_button(
            control_frame,
            text="✅ اختيار الصنف",
            button_type="success",
            command=self.select_item
        )
        select_btn.pack(side='right', padx=5)
        
        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            control_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_dialog
        )
        close_btn.pack(side='right', padx=5)
        
        # معلومات المساعدة
        help_label = create_simple_rtl_label(
            control_frame,
            text="💡 نصيحة: اضغط F9 للبحث، Enter للاختيار، Esc للإغلاق",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        help_label.pack(side='left', padx=10)
        
    def create_search_field(self, parent, label_text, var_name, width_ratio=1/3):
        """إنشاء حقل بحث"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)
        
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.search_vars[var_name]
        )
        entry.pack(fill='x', ipady=8)
        
        # ربط البحث الفوري
        self.search_vars[var_name].trace('w', self.on_search_change)
        
        return entry
    
    def create_search_combobox(self, parent, label_text, var_name, values=[], width_ratio=1/3):
        """إنشاء حقل بحث بقائمة منسدلة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)
        
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars[var_name] = tk.StringVar()
        combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.search_vars[var_name]
        )
        combo['values'] = values
        combo.pack(fill='x', ipady=8)
        
        # ربط البحث الفوري
        self.search_vars[var_name].trace('w', self.on_search_change)
        
        return combo
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        # تأخير قصير لتجنب البحث المفرط
        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)
        self._search_timer = self.root.after(300, self.perform_search)
    
    def perform_search(self):
        """تنفيذ البحث"""
        try:
            # جمع معايير البحث
            general_search = self.search_vars['general'].get().strip().lower()
            item_code_search = self.search_vars.get('item_code', tk.StringVar()).get().strip().lower()
            item_name_search = self.search_vars.get('item_name', tk.StringVar()).get().strip().lower()
            origin_country_search = self.search_vars.get('origin_country', tk.StringVar()).get().strip()
            
            # تصفية الأصناف
            self.filtered_items = []
            
            for item in self.items_data:
                # البحث العام
                if general_search:
                    item_text = f"{item.get('item_code', '')} {item.get('item_name', '')} {item.get('description', '')}".lower()
                    if general_search not in item_text:
                        continue
                
                # البحث في كود الصنف
                if item_code_search:
                    if item_code_search not in item.get('item_code', '').lower():
                        continue
                
                # البحث في اسم الصنف
                if item_name_search:
                    if item_name_search not in item.get('item_name', '').lower():
                        continue
                
                # البحث في بلد المنشأ
                if origin_country_search:
                    if origin_country_search != item.get('origin_country', ''):
                        continue
                
                # إضافة الصنف للنتائج
                self.filtered_items.append(item)
            
            # تحديث الجدول
            self.update_results_tree()
            
            # تحديث عداد النتائج
            self.update_results_count()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
    
    def clear_search(self):
        """مسح البحث"""
        for var in self.search_vars.values():
            var.set('')
        self.perform_search()
        self.focus_search_field()
    
    def load_items(self):
        """تحميل جميع الأصناف"""
        self.filtered_items = self.items_data.copy()
        self.update_results_tree()
        self.update_results_count()
    
    def update_results_tree(self):
        """تحديث جدول النتائج"""
        # مسح البيانات الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # إضافة النتائج
        for i, item_data in enumerate(self.filtered_items):
            self.add_result_to_tree(item_data, i + 1)
    
    def add_result_to_tree(self, item_data, index):
        """إضافة نتيجة للجدول"""
        try:
            values = [
                item_data.get('item_code', ''),
                item_data.get('item_name', ''),
                item_data.get('description', ''),
                str(item_data.get('quantity', 0)),
                item_data.get('unit', ''),
                f"{float(item_data.get('unit_price', 0)):,.2f}",
                f"{float(item_data.get('total_price', 0)):,.2f}"
            ]
            
            # تحديد لون الصف
            tags = ['odd_row' if index % 2 == 1 else 'even_row']
            
            # إدراج العنصر
            item = self.results_tree.insert(
                '', 'end',
                text=str(index),
                values=values,
                tags=tags
            )
            
            # حفظ بيانات الصنف
            self.results_tree.set(item, '#0', f"{index} (ID: {item_data.get('id', '')})")
            
        except Exception as e:
            print(f"خطأ في إضافة نتيجة للجدول: {e}")
    
    def update_results_count(self):
        """تحديث عداد النتائج"""
        total_items = len(self.items_data)
        filtered_items = len(self.filtered_items)
        
        if filtered_items == total_items:
            self.results_label.configure(text=f"📊 إجمالي الأصناف: {total_items}")
        else:
            self.results_label.configure(text=f"📊 النتائج: {filtered_items} من {total_items}")
    
    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            item_text = self.results_tree.item(item, 'text')
            
            # البحث عن الصنف
            if '(ID:' in item_text:
                try:
                    item_id = item_text.split('(ID:')[1].split(')')[0].strip()
                    
                    for item_data in self.filtered_items:
                        if str(item_data.get('id', '')) == str(item_id):
                            self.selected_item = item_data
                            break
                except:
                    self.selected_item = None
    
    def on_result_double_click(self, event):
        """عند النقر المزدوج على نتيجة"""
        self.select_item()
    
    def select_item(self):
        """اختيار الصنف"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف من النتائج")
            return
        
        self.close_dialog()
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.root.destroy()
    
    def focus_search_field(self):
        """تركيز على حقل البحث"""
        self.general_search_entry.focus_set()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    
    # بيانات تجريبية
    test_items = [
        {'id': '1', 'item_code': 'ITM001', 'item_name': 'منتج تجريبي 1', 'description': 'وصف المنتج الأول'},
        {'id': '2', 'item_code': 'ITM002', 'item_name': 'منتج تجريبي 2', 'description': 'وصف المنتج الثاني'}
    ]
    
    dialog = ItemSearchDialog(root, test_items, None)
    root.mainloop()
