#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الروابط التشعبية للمستندات
Test Document Hyperlinks
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_document_links():
    """اختبار الروابط التشعبية"""
    
    print("🧪 اختبار الروابط التشعبية للمستندات...")
    
    try:
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار الروابط التشعبية")
        root.geometry("600x400")
        root.configure(bg='#f8fafc')
        
        # تهيئة مدير المصادقة
        from src.auth_manager import auth_manager
        auth_manager.current_user = {
            'username': 'test_user',
            'role': 'admin',
            'permissions': ['all']
        }
        
        # فحص الدوال الموجودة
        from src.fullscreen_shipment_form import FullscreenShipmentForm
        
        methods = [method for method in dir(FullscreenShipmentForm) if 'link' in method.lower()]
        print(f"📋 الدوال المتعلقة بالروابط: {methods}")
        
        # فحص دالة create_document_link_field
        if hasattr(FullscreenShipmentForm, 'create_document_link_field'):
            print("✅ دالة create_document_link_field موجودة")
        else:
            print("❌ دالة create_document_link_field غير موجودة")
        
        # فحص دالة add_document_link
        if hasattr(FullscreenShipmentForm, 'add_document_link'):
            print("✅ دالة add_document_link موجودة")
        else:
            print("❌ دالة add_document_link غير موجودة")
        
        # فحص دالة open_document_link
        if hasattr(FullscreenShipmentForm, 'open_document_link'):
            print("✅ دالة open_document_link موجودة")
        else:
            print("❌ دالة open_document_link غير موجودة")
        
        # إنشاء كلاس اختبار
        class TestLinkForm:
            def __init__(self):
                self.document_links = {}
                self.form_vars = {}
                print("🔗 تم إنشاء كلاس اختبار الروابط")
            
            def add_document_link(self, var_name, document_name):
                """دالة اختبار إضافة رابط"""
                print(f"🔗 تم استدعاء add_document_link مع: var_name='{var_name}', document_name='{document_name}'")
                
                # محاكاة إدخال رابط
                test_url = "https://example.com/document.pdf"
                self.document_links[var_name] = {
                    'url': test_url,
                    'description': f'رابط اختبار لـ {document_name}',
                    'added_date': '2025-06-30 18:15:00'
                }
                
                messagebox.showinfo("تم الحفظ", f"تم حفظ رابط اختبار لـ {document_name}:\n{test_url}")
            
            def open_document_link(self, var_name, document_name):
                """دالة اختبار فتح رابط"""
                print(f"🔗 تم استدعاء open_document_link مع: var_name='{var_name}', document_name='{document_name}'")
                
                if var_name in self.document_links:
                    url = self.document_links[var_name]['url']
                    messagebox.showinfo("فتح الرابط", f"سيتم فتح الرابط:\n{url}")
                else:
                    messagebox.showinfo("لا يوجد رابط", f"لا يوجد رابط محفوظ لـ {document_name}")
        
        # إنشاء كلاس الاختبار
        test_form = TestLinkForm()
        
        # إنشاء واجهة الاختبار
        title_label = tk.Label(
            root,
            text="🔗 اختبار الروابط التشعبية للمستندات",
            font=('Arial', 16, 'bold'),
            bg='#f8fafc',
            fg='#1e40af'
        )
        title_label.pack(pady=20)
        
        # زر اختبار إضافة رابط
        add_link_btn = tk.Button(
            root,
            text="📝 اختبار إضافة رابط",
            font=('Arial', 12),
            bg='#2563eb',
            fg='white',
            padx=20,
            pady=10,
            command=lambda: test_form.add_document_link('test_document', 'مستند اختبار')
        )
        add_link_btn.pack(pady=10)
        
        # زر اختبار فتح رابط
        open_link_btn = tk.Button(
            root,
            text="🔗 اختبار فتح رابط",
            font=('Arial', 12),
            bg='#059669',
            fg='white',
            padx=20,
            pady=10,
            command=lambda: test_form.open_document_link('test_document', 'مستند اختبار')
        )
        open_link_btn.pack(pady=10)
        
        # تعليمات
        instructions = tk.Label(
            root,
            text="""📋 تعليمات الاختبار:
1. انقر على 'اختبار إضافة رابط' لمحاكاة إضافة رابط
2. انقر على 'اختبار فتح رابط' لمحاكاة فتح الرابط
3. راقب الرسائل في الكونسول""",
            font=('Arial', 10),
            bg='#f8fafc',
            fg='#374151',
            justify='right'
        )
        instructions.pack(pady=20)
        
        print("✅ تم إنشاء نافذة اختبار الروابط")
        print("💡 استخدم الأزرار لاختبار الوظائف")
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_document_links()
