#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تسجيل الدخول المتقدمة
Test Advanced Login Window
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.advanced_login_window import AdvancedLoginWindow, show_advanced_login

def test_login_window():
    """اختبار نافذة تسجيل الدخول"""
    print("🧪 بدء اختبار نافذة تسجيل الدخول المتقدمة...")
    
    try:
        # اختبار إنشاء النافذة
        print("✅ إنشاء نافذة تسجيل الدخول...")
        login_window = AdvancedLoginWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📋 الميزات المتاحة:")
        print("   • تصميم RTL متقدم")
        print("   • شريط عنوان مخصص")
        print("   • تأثيرات بصرية وحركية")
        print("   • نظام أمان متقدم")
        print("   • رسائل خطأ ونجاح")
        print("   • حوارات مخصصة")
        print("   • دعم اختصارات لوحة المفاتيح")
        
        print("\n🎮 التحكم في النافذة:")
        print("   • Enter: تسجيل الدخول")
        print("   • Escape: إغلاق النافذة")
        print("   • يمكن سحب النافذة من شريط العنوان")
        
        print("\n🔑 بيانات الدخول الافتراضية:")
        print("   • اسم المستخدم: admin")
        print("   • كلمة المرور: admin123")
        
        print("\n🚀 تشغيل النافذة...")
        result = login_window.run()
        
        if result:
            print("✅ تم تسجيل الدخول بنجاح!")
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_login_function():
    """اختبار وظيفة تسجيل الدخول المباشرة"""
    print("\n🧪 اختبار وظيفة تسجيل الدخول المباشرة...")
    
    try:
        result = show_advanced_login()
        
        if result:
            print("✅ تم تسجيل الدخول بنجاح!")
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def demo_features():
    """عرض توضيحي للميزات"""
    print("\n🎨 عرض توضيحي لميزات نافذة تسجيل الدخول المتقدمة:")
    print("=" * 60)
    
    features = [
        "🎨 تصميم RTL متقدم مع دعم كامل للعربية",
        "🖼️ شريط عنوان مخصص قابل للسحب",
        "🔐 نظام أمان متقدم مع قفل الحساب",
        "✨ تأثيرات بصرية (fade in/out, shake)",
        "🎭 رسوم متحركة للشعار وشريط التقدم",
        "👁️ إظهار/إخفاء كلمة المرور",
        "💾 خيار تذكر بيانات الدخول",
        "❓ نظام مساعدة وحوارات مخصصة",
        "🔄 استعادة كلمة المرور",
        "📱 تصميم متجاوب وحديث",
        "⌨️ دعم اختصارات لوحة المفاتيح",
        "🌈 نظام ألوان متناسق",
        "🔤 خطوط عربية محسنة",
        "📊 شريط تقدم متحرك",
        "🔔 رسائل حالة ملونة",
        "🎯 تركيز تلقائي على الحقول",
        "🛡️ حماية من المحاولات المتكررة",
        "📧 روابط الدعم الفني",
        "📅 معلومات النظام والإصدار",
        "🎪 تأثيرات تفاعلية متقدمة"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i:2d}. {feature}")
    
    print("\n" + "=" * 60)
    print("💡 نصائح الاستخدام:")
    print("   • جرب الضغط على زر العين لإظهار كلمة المرور")
    print("   • اضغط على 'نسيت كلمة المرور؟' لرؤية معلومات الاستعادة")
    print("   • انقر على 'مساعدة' لرؤية التعليمات الكاملة")
    print("   • جرب إدخال بيانات خاطئة لرؤية نظام الأمان")
    print("   • اسحب النافذة من شريط العنوان لتحريكها")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نافذة تسجيل الدخول المتقدمة")
    print("=" * 50)
    
    # عرض الميزات
    demo_features()
    
    # اختيار نوع الاختبار
    print("\n📋 اختر نوع الاختبار:")
    print("1. اختبار النافذة الكاملة")
    print("2. اختبار الوظيفة المباشرة")
    print("3. عرض الميزات فقط")
    print("0. خروج")
    
    try:
        choice = input("\nأدخل اختيارك (1-3): ").strip()
        
        if choice == "1":
            success = test_login_window()
            print(f"\n📊 نتيجة الاختبار: {'نجح' if success else 'فشل'}")
            
        elif choice == "2":
            success = test_login_function()
            print(f"\n📊 نتيجة الاختبار: {'نجح' if success else 'فشل'}")
            
        elif choice == "3":
            print("\n✅ تم عرض الميزات بنجاح!")
            
        elif choice == "0":
            print("\n👋 تم الخروج من الاختبار")
            
        else:
            print("\n❌ اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    main()
