# 🔍 البحث F9 في نافذة إضافة الصنف

## 📋 نظرة عامة

تم تفعيل **وظيفة البحث المتقدم F9** في حقل "كود الصنف" داخل نافذة إضافة صنف جديد، مما يتيح للمستخدم البحث في الأصناف المحفوظة مسبقاً في قاعدة البيانات واختيار كود صنف موجود مع إمكانية ملء البيانات تلقائياً.

## ✨ الميزات الرئيسية

### 🔍 **البحث في الأصناف المحفوظة**

#### **مصدر البيانات:**
- **قاعدة البيانات**: البحث في جدول `shipment_items`
- **أكواد فريدة**: عرض الأكواد الفريدة فقط (DISTINCT)
- **بيانات شاملة**: كود، اسم، وحدة، سعر، بلد المنشأ
- **ترتيب ذكي**: ترتيب حسب كود الصنف

#### **استعلام قاعدة البيانات:**
```sql
SELECT DISTINCT item_code, item_name, unit, unit_price, origin_country, description
FROM shipment_items 
WHERE item_code IS NOT NULL AND item_code != ''
ORDER BY item_code
```

### 🎯 **واجهة البحث المتقدمة**

#### **نافذة البحث (700x500):**
- **عنوان واضح**: "🔍 البحث في أكواد الأصناف المحفوظة"
- **وصف مفيد**: "ابحث في الأصناف المحفوظة مسبقاً واختر كود الصنف المطلوب"
- **تصميم RTL**: محاذاة يمين طبيعية للعربية
- **نافذة modal**: تركيز حصري على البحث

#### **حقل البحث الذكي:**
- **بحث موحد**: في كود الصنف واسم الصنف معاً
- **بحث فوري**: النتائج تظهر أثناء الكتابة (300ms تأخير)
- **نص توضيحي**: "ابحث في كود الصنف أو اسم الصنف..."
- **بحث غير حساس**: لحالة الأحرف

### 📊 **جدول النتائج المتقدم**

#### **5 أعمدة شاملة:**
1. **🔢 كود الصنف**: رقم التعريف الفريد
2. **📦 اسم الصنف**: الاسم التجاري للمنتج
3. **📏 الوحدة**: وحدة القياس المحفوظة
4. **💰 سعر الوحدة**: السعر المحفوظ مسبقاً
5. **🌍 بلد المنشأ**: البلد المصنع أو المنتج

#### **مميزات الجدول:**
- **أشرطة تمرير**: عمودية وأفقية للتنقل السهل
- **صفوف متناوبة**: ألوان متناوبة للوضوح
- **تحديد مفرد**: اختيار صنف واحد فقط
- **نقر مزدوج**: للاختيار السريع

### 📈 **عداد النتائج الديناميكي**

#### **عرض ذكي:**
- **عند عدم البحث**: "📊 إجمالي الأكواد: X"
- **عند البحث**: "📊 النتائج: Y من X"
- **تحديث فوري**: مع كل تغيير في البحث

#### **مثال:**
```
قبل البحث: "📊 إجمالي الأكواد: 150"
بعد البحث: "📊 النتائج: 12 من 150"
```

### 🔄 **الملء التلقائي للبيانات**

#### **عند اختيار صنف:**
1. **كود الصنف**: يتم ملؤه تلقائياً
2. **اسم الصنف**: إذا كان متوفراً
3. **الوصف**: إذا كان متوفراً
4. **الوحدة**: إذا كانت متوفرة
5. **سعر الوحدة**: إذا كان متوفراً
6. **بلد المنشأ**: إذا كان متوفراً

#### **التركيز التلقائي:**
- بعد الملء، ينتقل التركيز لحقل "الكمية"
- يوفر تدفق عمل سلس للمستخدم

## 🛠️ **التقنيات المستخدمة**

### 🎨 **نافذة حوار متخصصة**

#### **ItemCodeSearchDialog Class:**
```python
class ItemCodeSearchDialog:
    """نافذة حوار البحث في أكواد الأصناف"""
    - اتصال مباشر بقاعدة البيانات
    - بحث فوري مع تأخير ذكي
    - ملء تلقائي للبيانات
    - تصميم RTL متقدم
```

#### **مكونات الواجهة:**
- **منطقة البحث**: حقل بحث موحد مع زر مسح
- **جدول النتائج**: عرض الأصناف مع تمرير
- **شريط التحكم**: أزرار الاختيار والإغلاق
- **شريط المساعدة**: نصائح الاستخدام

### 🔗 **التكامل مع نافذة الصنف**

#### **ربط F9:**
```python
# ربط F9 على مستوى النافذة
self.root.bind('<F9>', self.show_item_code_search)

# ربط F9 بحقل كود الصنف
self.item_code_entry.bind('<F9>', self.show_item_code_search)
self.item_code_entry.bind('<KeyPress-F9>', self.show_item_code_search)
```

#### **وظيفة البحث:**
```python
def show_item_code_search(self, event=None):
    """إظهار نافذة البحث عند F9"""
    # 1. إنشاء نافذة البحث
    # 2. انتظار النتيجة
    # 3. ملء البيانات تلقائياً
    # 4. التركيز على الحقل التالي
```

### 🗄️ **استعلام قاعدة البيانات المحسن**

#### **استعلام الأكواد الفريدة:**
```python
def load_items_from_database(self):
    """تحميل الأصناف من قاعدة البيانات"""
    query = """
        SELECT DISTINCT item_code, item_name, unit, unit_price, 
               origin_country, description
        FROM shipment_items 
        WHERE item_code IS NOT NULL AND item_code != ''
        ORDER BY item_code
    """
```

#### **مميزات الاستعلام:**
- **DISTINCT**: لتجنب التكرار
- **WHERE**: لتصفية الأكواد الفارغة
- **ORDER BY**: للترتيب المنطقي

## ⌨️ **اختصارات لوحة المفاتيح**

### **في نافذة إضافة الصنف:**
- **F9**: فتح نافذة البحث في أكواد الأصناف
- **Enter**: حفظ الصنف
- **Esc**: إلغاء وإغلاق النافذة

### **في نافذة البحث:**
- **Enter**: اختيار الصنف المحدد
- **Esc**: إغلاق نافذة البحث
- **F9**: إغلاق نافذة البحث
- **Double-Click**: اختيار سريع للصنف

### **التنقل:**
- **Tab**: التنقل بين الحقول
- **↑/↓**: التنقل في نتائج الجدول
- **Ctrl+A**: تحديد الكل في حقل البحث

## 🚀 **كيفية الاستخدام**

### **1. فتح نافذة إضافة صنف:**
```
1. انتقل لقسم الأصناف (القسم 2)
2. انقر "➕ إضافة صنف"
3. ستفتح نافذة إضافة صنف جديد
```

### **2. تفعيل البحث F9:**
```
طرق تفعيل البحث:
✅ اضغط F9 في أي مكان في النافذة
✅ اضغط F9 أثناء التركيز على حقل كود الصنف
✅ انقر على التلميح في أسفل النافذة
```

### **3. البحث في الأكواد:**
```
1. اكتب في حقل البحث:
   - جزء من كود الصنف
   - جزء من اسم الصنف
   - أو كليهما

2. النتائج تظهر فورياً أثناء الكتابة
3. استخدم "🗑️ مسح البحث" لمسح النص
```

### **4. اختيار الصنف:**
```
طرق الاختيار:
✅ انقر على الصنف ثم "✅ اختيار الكود"
✅ انقر مرتين على الصنف
✅ انقر على الصنف ثم اضغط Enter
```

### **5. الملء التلقائي:**
```
بعد الاختيار:
1. سيتم ملء كود الصنف تلقائياً
2. سيتم ملء البيانات المتوفرة (اسم، وحدة، سعر، إلخ)
3. سينتقل التركيز لحقل الكمية
4. أكمل باقي البيانات المطلوبة
```

## 🎯 **حالات الاستخدام**

### **🔍 سيناريوهات الاستخدام الشائعة:**

#### **1. إضافة صنف موجود مسبقاً:**
```
المشكلة: تريد إضافة صنف تم شحنه من قبل
الحل: 
1. اضغط F9 في حقل كود الصنف
2. ابحث عن الصنف
3. اختر الصنف المطلوب
4. ستمتلئ البيانات تلقائياً
5. أدخل الكمية الجديدة فقط
```

#### **2. البحث بالكود الجزئي:**
```
المشكلة: تتذكر جزء من كود الصنف فقط
الحل:
1. اضغط F9
2. اكتب الجزء المتذكر (مثل "ITM")
3. ستظهر جميع الأكواد التي تحتوي على "ITM"
4. اختر الكود المطلوب
```

#### **3. البحث بالاسم:**
```
المشكلة: تتذكر اسم المنتج وليس الكود
الحل:
1. اضغط F9
2. اكتب جزء من اسم المنتج
3. ستظهر جميع المنتجات المطابقة
4. اختر المنتج المطلوب
```

#### **4. توفير الوقت في الإدخال:**
```
الفائدة: بدلاً من إدخال جميع البيانات يدوياً
الحل:
1. استخدم F9 لاختيار صنف موجود
2. ستمتلئ معظم البيانات تلقائياً
3. عدّل فقط ما تحتاج تعديله (مثل الكمية)
```

### **⚡ نصائح للاستخدام الأمثل:**

#### **1. البحث السريع:**
- اكتب أول حروف من كود الصنف
- أو اكتب كلمة مفتاحية من اسم المنتج
- النتائج ستظهر فورياً

#### **2. الاستفادة من الملء التلقائي:**
- اختر أصناف محفوظة مسبقاً لتوفير الوقت
- تحقق من البيانات المملوءة تلقائياً
- عدّل فقط ما يحتاج تعديل

#### **3. إدارة الأكواد:**
- استخدم أكواد منطقية ومنظمة
- احفظ الأصناف الشائعة لسهولة البحث
- استخدم أسماء واضحة للمنتجات

## 🎉 **النتيجة النهائية**

**✅ تم تفعيل البحث F9 في نافذة إضافة الصنف بنجاح!**

### **🌟 الإنجازات المحققة:**
- 🔍 **بحث متقدم** في الأصناف المحفوظة مسبقاً
- 🗄️ **اتصال مباشر** بقاعدة البيانات للأكواد الفريدة
- ⚡ **بحث فوري** مع تأخير ذكي 300ms
- 📊 **جدول نتائج متقدم** مع 5 أعمدة شاملة
- 🔄 **ملء تلقائي ذكي** لجميع البيانات المتوفرة
- ⌨️ **اختصارات شاملة** للاستخدام السريع
- 🎯 **تركيز تلقائي** على الحقل التالي
- 💡 **تلميحات واضحة** للمستخدم

### **📊 الإحصائيات:**
- **أعمدة الجدول**: 5 أعمدة شاملة
- **حقول الملء التلقائي**: 6 حقول
- **الاختصارات**: 6 اختصارات رئيسية
- **وقت الاستجابة**: 300ms للبحث الفوري
- **حجم النافذة**: 700x500 بكسل

**وظيفة البحث F9 في نافذة إضافة الصنف جاهزة للاستخدام الإنتاجي مع تجربة مستخدم متقدمة!** 🔍✨📊🎯📦
