#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة نموذج الشحنة (إضافة/تعديل) مع دعم RTL
Shipment Form Window (Add/Edit) with RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime, date
import uuid

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES
from database.database_manager import DatabaseManager
from src.ui_styles import get_style_manager
from src.rtl_components import *
from src.auth_manager import auth_manager

class ShipmentFormWindow:
    def __init__(self, parent=None, mode='add', shipment_data=None):
        self.parent = parent
        self.mode = mode  # 'add' أو 'edit'
        self.shipment_data = shipment_data or {}
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.style_manager = get_style_manager()
        self.db_manager = DatabaseManager()
        
        # متغيرات النموذج
        self.form_vars = {}
        self.suppliers_data = []
        self.items_data = []
        
        self.setup_window()
        self.create_form_interface()
        self.load_suppliers()
        self.load_form_data()
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة"""
        title = "إضافة شحنة جديدة" if self.mode == 'add' else "تعديل الشحنة"
        self.root.title(f"📦 {title}")
        self.root.geometry("900x700")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        
        # ربط أحداث لوحة المفاتيح
        self.root.bind('<Control-s>', lambda e: self.save_shipment())
        self.root.bind('<Escape>', lambda e: self.cancel_form())
        self.root.bind('<F1>', lambda e: self.show_help())
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_form_interface(self):
        """إنشاء واجهة النموذج"""
        # الإطار الرئيسي
        main_frame = create_rtl_frame(self.root, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # محتوى النموذج مع تبويبات
        self.create_tabbed_form(main_frame)
        
        # أزرار الإجراءات
        self.create_action_buttons(main_frame)
        
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = create_rtl_frame(parent, bg=COLORS['primary'], height=60)
        header_frame.pack(fill='x', pady=(0, 15))
        header_frame.pack_propagate(False)
        
        # أيقونة ونص العنوان
        title_text = "إضافة شحنة جديدة" if self.mode == 'add' else f"تعديل الشحنة رقم {self.shipment_data.get('shipment_number', '')}"
        icon = "➕" if self.mode == 'add' else "✏️"
        
        title_label = create_rtl_label(
            header_frame,
            text=f"{icon} {title_text}",
            font=self.style_manager.get_font('arabic_title', size=18, weight='bold'),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        title_label.pack(expand=True, pady=15)
        
    def create_tabbed_form(self, parent):
        """إنشاء نموذج مع تبويبات"""
        # إطار التبويبات
        notebook_frame = create_rtl_frame(parent, bg=COLORS['surface'])
        notebook_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(notebook_frame)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تبويب المعلومات الأساسية
        self.create_basic_info_tab()
        
        # تبويب معلومات الشحن
        self.create_shipping_info_tab()
        
        # تبويب المعلومات المالية
        self.create_financial_info_tab()
        
        # تبويب الأصناف
        self.create_items_tab()
        
        # تبويب الملاحظات والمرفقات
        self.create_notes_attachments_tab()
        
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab_frame = create_rtl_frame(self.notebook, bg=COLORS['surface'])
        self.notebook.add(tab_frame, text="📋 المعلومات الأساسية")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['surface'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = create_rtl_frame(canvas, bg=COLORS['surface'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المحتوى
        content_frame = create_rtl_frame(scrollable_frame, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # رقم الشحنة
        self.create_form_field(
            content_frame,
            "shipment_number",
            "📦 رقم الشحنة:",
            "entry",
            required=True,
            readonly=(self.mode == 'edit')
        )
        
        # المورد
        self.create_form_field(
            content_frame,
            "supplier_id",
            "🏢 المورد:",
            "combobox",
            required=True
        )
        
        # تاريخ الإنشاء
        self.create_form_field(
            content_frame,
            "created_at",
            "📅 تاريخ الإنشاء:",
            "date",
            readonly=True
        )
        
        # المستخدم المنشئ
        self.create_form_field(
            content_frame,
            "created_by",
            "👤 المستخدم المنشئ:",
            "entry",
            readonly=True
        )
        
        # حالة الشحنة
        self.create_form_field(
            content_frame,
            "status",
            "📊 حالة الشحنة:",
            "combobox",
            values=list(SHIPMENT_STATUS.keys()),
            required=True
        )
        
        # تخطيط الكانفاس
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_shipping_info_tab(self):
        """إنشاء تبويب معلومات الشحن"""
        tab_frame = create_rtl_frame(self.notebook, bg=COLORS['surface'])
        self.notebook.add(tab_frame, text="🚢 معلومات الشحن")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['surface'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = create_rtl_frame(canvas, bg=COLORS['surface'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المحتوى
        content_frame = create_rtl_frame(scrollable_frame, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # تاريخ الشحن
        self.create_form_field(
            content_frame,
            "shipment_date",
            "📅 تاريخ الشحن:",
            "date",
            required=True
        )
        
        # تاريخ الوصول المتوقع
        self.create_form_field(
            content_frame,
            "expected_arrival_date",
            "📅 تاريخ الوصول المتوقع:",
            "date"
        )
        
        # ميناء المغادرة
        self.create_form_field(
            content_frame,
            "departure_port",
            "🚢 ميناء المغادرة:",
            "combobox",
            values=list(PORTS.keys()),
            required=True
        )
        
        # ميناء الوصول
        self.create_form_field(
            content_frame,
            "arrival_port",
            "🏁 ميناء الوصول:",
            "combobox",
            values=list(PORTS.keys()),
            required=True
        )
        
        # شركة الشحن
        self.create_form_field(
            content_frame,
            "shipping_company",
            "🚛 شركة الشحن:",
            "combobox",
            values=list(SHIPPING_COMPANIES.keys()),
            required=True
        )
        
        # رقم الحاوية
        self.create_form_field(
            content_frame,
            "container_number",
            "📦 رقم الحاوية:",
            "entry"
        )
        
        # رقم بوليصة الشحن
        self.create_form_field(
            content_frame,
            "bill_of_lading",
            "📋 رقم بوليصة الشحن:",
            "entry"
        )
        
        # نوع الحاوية
        self.create_form_field(
            content_frame,
            "container_type",
            "📦 نوع الحاوية:",
            "combobox",
            values=["20 قدم عادية", "40 قدم عادية", "40 قدم عالية", "مبردة 20 قدم", "مبردة 40 قدم", "مفتوحة", "خاصة"]
        )
        
        # وزن الشحنة
        self.create_form_field(
            content_frame,
            "weight",
            "⚖️ الوزن (كيلو):",
            "entry"
        )
        
        # حجم الشحنة
        self.create_form_field(
            content_frame,
            "volume",
            "📏 الحجم (متر مكعب):",
            "entry"
        )
        
        # تخطيط الكانفاس
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_financial_info_tab(self):
        """إنشاء تبويب المعلومات المالية"""
        tab_frame = create_rtl_frame(self.notebook, bg=COLORS['surface'])
        self.notebook.add(tab_frame, text="💰 المعلومات المالية")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['surface'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = create_rtl_frame(canvas, bg=COLORS['surface'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المحتوى
        content_frame = create_rtl_frame(scrollable_frame, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العملة
        self.create_form_field(
            content_frame,
            "currency",
            "💱 العملة:",
            "combobox",
            values=list(CURRENCIES.keys()),
            required=True
        )
        
        # القيمة الإجمالية
        self.create_form_field(
            content_frame,
            "total_value",
            "💰 القيمة الإجمالية:",
            "entry",
            required=True
        )
        
        # تكلفة الشحن
        self.create_form_field(
            content_frame,
            "shipping_cost",
            "🚢 تكلفة الشحن:",
            "entry"
        )
        
        # رسوم إضافية
        self.create_form_field(
            content_frame,
            "additional_fees",
            "💸 رسوم إضافية:",
            "entry"
        )
        
        # رسوم التأمين
        self.create_form_field(
            content_frame,
            "insurance_cost",
            "🛡️ رسوم التأمين:",
            "entry"
        )
        
        # رسوم الجمارك
        self.create_form_field(
            content_frame,
            "customs_fees",
            "🏛️ رسوم الجمارك:",
            "entry"
        )
        
        # طريقة الدفع
        self.create_form_field(
            content_frame,
            "payment_method",
            "💳 طريقة الدفع:",
            "combobox",
            values=["نقداً", "تحويل بنكي", "شيك", "اعتماد مستندي", "دفع عند الاستلام"]
        )
        
        # حالة الدفع
        self.create_form_field(
            content_frame,
            "payment_status",
            "💰 حالة الدفع:",
            "combobox",
            values=["لم يتم الدفع", "دفع جزئي", "تم الدفع بالكامل", "مسترد"]
        )
        
        # تخطيط الكانفاس
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        tab_frame = create_rtl_frame(self.notebook, bg=COLORS['surface'])
        self.notebook.add(tab_frame, text="📦 الأصناف")
        
        # شريط الأدوات
        toolbar_frame = create_rtl_frame(tab_frame, bg=COLORS['light'], height=50)
        toolbar_frame.pack(fill='x', padx=10, pady=(10, 5))
        toolbar_frame.pack_propagate(False)
        
        # أزرار إدارة الأصناف
        add_item_btn = create_rtl_button(
            toolbar_frame,
            text="➕ إضافة صنف",
            command=self.add_item,
            style='success',
            width=12
        )
        add_item_btn.pack(side='right', padx=5, pady=10)
        
        edit_item_btn = create_rtl_button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_item,
            style='warning',
            width=12
        )
        edit_item_btn.pack(side='right', padx=5, pady=10)
        
        delete_item_btn = create_rtl_button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_item,
            style='danger',
            width=12
        )
        delete_item_btn.pack(side='right', padx=5, pady=10)
        
        # جدول الأصناف
        items_frame = create_rtl_frame(tab_frame, bg=COLORS['surface'])
        items_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تعريف أعمدة جدول الأصناف
        items_columns = [
            ('item_code', 'كود الصنف', 100),
            ('item_name', 'اسم الصنف', 200),
            ('quantity', 'الكمية', 80),
            ('unit', 'الوحدة', 80),
            ('unit_price', 'سعر الوحدة', 100),
            ('total_price', 'الإجمالي', 100)
        ]
        
        # إنشاء جدول الأصناف
        self.items_tree = create_rtl_treeview(
            items_frame,
            columns=[col[0] for col in items_columns],
            show='headings',
            height=10
        )
        
        # تكوين أعمدة جدول الأصناف
        for col_id, col_name, col_width in items_columns:
            self.items_tree.heading(col_id, text=col_name, anchor='e')
            self.items_tree.column(col_id, width=col_width, anchor='e')
        
        # شريط التمرير لجدول الأصناف
        items_scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # تخطيط جدول الأصناف
        self.items_tree.pack(side='left', fill='both', expand=True)
        items_scrollbar.pack(side='right', fill='y')
        
        # ربط أحداث جدول الأصناف
        self.items_tree.bind('<Double-1>', lambda e: self.edit_item())
        
    def create_notes_attachments_tab(self):
        """إنشاء تبويب الملاحظات والمرفقات"""
        tab_frame = create_rtl_frame(self.notebook, bg=COLORS['surface'])
        self.notebook.add(tab_frame, text="📝 ملاحظات ومرفقات")
        
        # إطار المحتوى
        content_frame = create_rtl_frame(tab_frame, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # قسم الملاحظات
        notes_section = create_rtl_card(content_frame, title="📝 الملاحظات")
        notes_section.pack(fill='both', expand=True, pady=(0, 10))
        
        notes_content = create_rtl_frame(notes_section, bg=COLORS['surface'])
        notes_content.pack(fill='both', expand=True, padx=15, pady=15)
        
        # حقل الملاحظات
        self.form_vars['notes'] = tk.StringVar()
        notes_text = create_rtl_text(
            notes_content,
            height=8,
            wrap='word',
            font=self.style_manager.get_font('arabic', size=11)
        )
        notes_text.pack(fill='both', expand=True)
        
        # حفظ مرجع للنص
        self.notes_text = notes_text
        
        # قسم المرفقات
        attachments_section = create_rtl_card(content_frame, title="📎 المرفقات")
        attachments_section.pack(fill='x', pady=(10, 0))
        
        attachments_content = create_rtl_frame(attachments_section, bg=COLORS['surface'])
        attachments_content.pack(fill='x', padx=15, pady=15)
        
        # أزرار المرفقات
        attachments_buttons = create_rtl_frame(attachments_content, bg=COLORS['surface'])
        attachments_buttons.pack(fill='x', pady=(0, 10))
        
        add_attachment_btn = create_rtl_button(
            attachments_buttons,
            text="📎 إضافة مرفق",
            command=self.add_attachment,
            style='info',
            width=15
        )
        add_attachment_btn.pack(side='right', padx=5)
        
        # قائمة المرفقات
        self.attachments_listbox = tk.Listbox(
            attachments_content,
            height=4,
            font=self.style_manager.get_font('arabic', size=10),
            bg=COLORS['light'],
            selectbackground=COLORS['primary']
        )
        self.attachments_listbox.pack(fill='x')
        
    def create_form_field(self, parent, field_name, label_text, field_type, **kwargs):
        """إنشاء حقل في النموذج"""
        # إطار الحقل
        field_frame = create_rtl_frame(parent, bg=COLORS['surface'])
        field_frame.pack(fill='x', pady=8)
        
        # التسمية
        required = kwargs.get('required', False)
        label_color = COLORS['danger'] if required else COLORS['text_primary']
        required_mark = " *" if required else ""
        
        label = create_rtl_label(
            field_frame,
            text=f"{label_text}{required_mark}",
            font=self.style_manager.get_font('arabic', size=11, weight='bold'),
            bg=COLORS['surface'],
            fg=label_color
        )
        label.pack(anchor='e', pady=(0, 5))
        
        # إنشاء متغير للحقل
        self.form_vars[field_name] = tk.StringVar()
        
        # إنشاء الحقل حسب النوع
        if field_type == 'entry':
            widget = create_rtl_entry(
                field_frame,
                textvariable=self.form_vars[field_name],
                font=self.style_manager.get_font('arabic', size=11),
                state='readonly' if kwargs.get('readonly', False) else 'normal'
            )
            widget.pack(fill='x', ipady=5)
            
        elif field_type == 'combobox':
            widget = create_rtl_combobox(
                field_frame,
                textvariable=self.form_vars[field_name],
                values=kwargs.get('values', []),
                state='readonly' if kwargs.get('readonly', False) else 'normal'
            )
            widget.pack(fill='x', ipady=5)
            
        elif field_type == 'date':
            widget = create_rtl_entry(
                field_frame,
                textvariable=self.form_vars[field_name],
                font=self.style_manager.get_font('arabic', size=11),
                state='readonly' if kwargs.get('readonly', False) else 'normal'
            )
            widget.pack(fill='x', ipady=5)
            
            # إضافة تلميح للتاريخ
            if not kwargs.get('readonly', False):
                hint_label = create_rtl_label(
                    field_frame,
                    text="تنسيق التاريخ: YYYY-MM-DD",
                    font=self.style_manager.get_font('arabic_small', size=9),
                    bg=COLORS['surface'],
                    fg=COLORS['text_muted']
                )
                hint_label.pack(anchor='e', pady=(2, 0))
        
        # حفظ مرجع للحقل
        setattr(self, f"{field_name}_widget", widget)
        
        return widget

    def create_action_buttons(self, parent):
        """إنشاء أزرار الإجراءات"""
        buttons_frame = create_rtl_frame(parent, bg=COLORS['light'], height=60)
        buttons_frame.pack(fill='x', side='bottom')
        buttons_frame.pack_propagate(False)

        # إطار الأزرار
        buttons_container = create_rtl_frame(buttons_frame, bg=COLORS['light'])
        buttons_container.pack(expand=True, pady=15)

        # زر الحفظ
        save_text = "💾 حفظ الشحنة" if self.mode == 'add' else "💾 حفظ التعديلات"
        save_btn = create_rtl_button(
            buttons_container,
            text=save_text,
            command=self.save_shipment,
            style='success',
            font=self.style_manager.get_font('arabic_button', size=12, weight='bold'),
            width=15
        )
        save_btn.pack(side='right', padx=10)

        # زر الإلغاء
        cancel_btn = create_rtl_button(
            buttons_container,
            text="❌ إلغاء",
            command=self.cancel_form,
            style='outline',
            font=self.style_manager.get_font('arabic_button', size=12),
            width=15
        )
        cancel_btn.pack(side='right', padx=10)

        # زر المساعدة
        help_btn = create_rtl_button(
            buttons_container,
            text="❓ مساعدة",
            command=self.show_help,
            style='ghost',
            font=self.style_manager.get_font('arabic_button', size=12),
            width=15
        )
        help_btn.pack(side='left', padx=10)

        # اختصارات لوحة المفاتيح
        shortcut_label = create_rtl_label(
            buttons_container,
            text="Ctrl+S: حفظ | Esc: إلغاء | F1: مساعدة",
            font=self.style_manager.get_font('arabic_small', size=9),
            bg=COLORS['light'],
            fg=COLORS['text_muted']
        )
        shortcut_label.pack(side='left', padx=20)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all("SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name")
            self.suppliers_data = suppliers

            # تحديث قائمة الموردين في الحقل
            supplier_names = [f"{supplier['supplier_name']} (ID: {supplier['id']})" for supplier in suppliers]
            if hasattr(self, 'supplier_id_widget'):
                self.supplier_id_widget['values'] = supplier_names

        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")

    def load_form_data(self):
        """تحميل بيانات النموذج"""
        if self.mode == 'add':
            # تعيين القيم الافتراضية للإضافة
            self.form_vars['shipment_number'].set(self.generate_shipment_number())
            self.form_vars['created_at'].set(datetime.now().strftime('%Y-%m-%d'))
            self.form_vars['created_by'].set(auth_manager.get_current_user().get('username', ''))
            self.form_vars['status'].set('في الانتظار')
            self.form_vars['currency'].set('USD')
            self.form_vars['payment_status'].set('لم يتم الدفع')

        else:
            # تحميل بيانات الشحنة للتعديل
            for field_name, var in self.form_vars.items():
                value = self.shipment_data.get(field_name, '')
                if value:
                    if field_name == 'supplier_id':
                        # البحث عن اسم المورد
                        for supplier in self.suppliers_data:
                            if supplier['id'] == value:
                                var.set(f"{supplier['supplier_name']} (ID: {supplier['id']})")
                                break
                    else:
                        var.set(str(value))

            # تحميل الملاحظات
            notes = self.shipment_data.get('notes', '')
            if notes and hasattr(self, 'notes_text'):
                self.notes_text.insert('1.0', notes)

            # تحميل الأصناف
            self.load_shipment_items()

    def generate_shipment_number(self):
        """إنشاء رقم شحنة جديد"""
        try:
            # الحصول على آخر رقم شحنة
            result = self.db_manager.fetch_one(
                "SELECT MAX(CAST(SUBSTR(shipment_number, 4) AS INTEGER)) as max_num FROM shipments WHERE shipment_number LIKE 'SH-%'"
            )

            if result and result['max_num']:
                next_num = result['max_num'] + 1
            else:
                next_num = 1

            return f"SH-{next_num:06d}"

        except Exception as e:
            # في حالة الخطأ، استخدم timestamp
            return f"SH-{int(datetime.now().timestamp())}"

    def load_shipment_items(self):
        """تحميل أصناف الشحنة"""
        try:
            if self.mode == 'edit' and self.shipment_data.get('id'):
                items = self.db_manager.fetch_all(
                    "SELECT * FROM shipment_items WHERE shipment_id = ?",
                    (self.shipment_data['id'],)
                )

                self.items_data = items
                self.update_items_table()

        except Exception as e:
            print(f"خطأ في تحميل أصناف الشحنة: {e}")

    def update_items_table(self):
        """تحديث جدول الأصناف"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة البيانات الجديدة
        for item in self.items_data:
            values = [
                item.get('item_code', ''),
                item.get('item_name', ''),
                item.get('quantity', ''),
                item.get('unit', ''),
                f"{float(item.get('unit_price', 0)):,.2f}",
                f"{float(item.get('total_price', 0)):,.2f}"
            ]

            self.items_tree.insert('', 'end', values=values)

    def add_item(self):
        """إضافة صنف جديد"""
        # إنشاء نافذة إضافة صنف
        self.show_item_form()

    def edit_item(self):
        """تعديل الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return

        # الحصول على بيانات الصنف
        item_index = self.items_tree.index(selection[0])
        item_data = self.items_data[item_index]

        # إنشاء نافذة تعديل الصنف
        self.show_item_form(item_data, item_index)

    def delete_item(self):
        """حذف الصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الصنف؟")
        if result:
            item_index = self.items_tree.index(selection[0])
            del self.items_data[item_index]
            self.update_items_table()

    def show_item_form(self, item_data=None, item_index=None):
        """إظهار نموذج إضافة/تعديل صنف"""
        # إنشاء نافذة فرعية
        item_window = tk.Toplevel(self.root)
        item_window.title("إضافة صنف" if item_data is None else "تعديل صنف")
        item_window.geometry("500x400")
        item_window.configure(bg=COLORS['surface'])
        item_window.transient(self.root)
        item_window.grab_set()

        # توسيط النافذة
        item_window.geometry("+{}+{}".format(
            self.root.winfo_x() + 100,
            self.root.winfo_y() + 100
        ))

        # إطار المحتوى
        content_frame = create_rtl_frame(item_window, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # متغيرات النموذج
        item_vars = {
            'item_code': tk.StringVar(),
            'item_name': tk.StringVar(),
            'quantity': tk.StringVar(),
            'unit': tk.StringVar(),
            'unit_price': tk.StringVar()
        }

        # تحميل البيانات إذا كان تعديل
        if item_data:
            for key, var in item_vars.items():
                var.set(str(item_data.get(key, '')))

        # حقول النموذج
        fields = [
            ('item_code', '🏷️ كود الصنف:', 'entry'),
            ('item_name', '📦 اسم الصنف:', 'entry'),
            ('quantity', '🔢 الكمية:', 'entry'),
            ('unit', '📏 الوحدة:', 'combobox'),
            ('unit_price', '💰 سعر الوحدة:', 'entry')
        ]

        widgets = {}
        for field_name, label_text, field_type in fields:
            # إطار الحقل
            field_frame = create_rtl_frame(content_frame, bg=COLORS['surface'])
            field_frame.pack(fill='x', pady=8)

            # التسمية
            label = create_rtl_label(
                field_frame,
                text=label_text,
                font=self.style_manager.get_font('arabic', size=11, weight='bold'),
                bg=COLORS['surface']
            )
            label.pack(anchor='e', pady=(0, 5))

            # الحقل
            if field_type == 'entry':
                widget = create_rtl_entry(
                    field_frame,
                    textvariable=item_vars[field_name],
                    font=self.style_manager.get_font('arabic', size=11)
                )
            elif field_type == 'combobox':
                values = ['قطعة', 'كيلو', 'متر', 'لتر', 'طن', 'صندوق', 'كرتون', 'حقيبة']
                widget = create_rtl_combobox(
                    field_frame,
                    textvariable=item_vars[field_name],
                    values=values
                )

            widget.pack(fill='x', ipady=5)
            widgets[field_name] = widget

        # أزرار الإجراءات
        buttons_frame = create_rtl_frame(content_frame, bg=COLORS['surface'])
        buttons_frame.pack(fill='x', pady=(20, 0))

        def save_item():
            # التحقق من صحة البيانات
            if not all([item_vars['item_code'].get(), item_vars['item_name'].get(),
                       item_vars['quantity'].get(), item_vars['unit_price'].get()]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return

            try:
                # حساب الإجمالي
                quantity = float(item_vars['quantity'].get())
                unit_price = float(item_vars['unit_price'].get())
                total_price = quantity * unit_price

                # إنشاء بيانات الصنف
                new_item = {
                    'item_code': item_vars['item_code'].get(),
                    'item_name': item_vars['item_name'].get(),
                    'quantity': quantity,
                    'unit': item_vars['unit'].get(),
                    'unit_price': unit_price,
                    'total_price': total_price
                }

                # إضافة أو تعديل الصنف
                if item_index is not None:
                    self.items_data[item_index] = new_item
                else:
                    self.items_data.append(new_item)

                # تحديث الجدول
                self.update_items_table()

                # إغلاق النافذة
                item_window.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للكمية والسعر")

        save_btn = create_rtl_button(
            buttons_frame,
            text="💾 حفظ",
            command=save_item,
            style='success',
            width=12
        )
        save_btn.pack(side='right', padx=5)

        cancel_btn = create_rtl_button(
            buttons_frame,
            text="❌ إلغاء",
            command=item_window.destroy,
            style='outline',
            width=12
        )
        cancel_btn.pack(side='right', padx=5)

    def add_attachment(self):
        """إضافة مرفق"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف للإرفاق",
            filetypes=[
                ("جميع الملفات", "*.*"),
                ("ملفات PDF", "*.pdf"),
                ("ملفات Word", "*.doc;*.docx"),
                ("ملفات Excel", "*.xls;*.xlsx"),
                ("الصور", "*.jpg;*.jpeg;*.png;*.gif")
            ]
        )

        if file_path:
            file_name = os.path.basename(file_path)
            self.attachments_listbox.insert(tk.END, file_name)

    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            # جمع بيانات النموذج
            shipment_data = self.collect_form_data()

            if self.mode == 'add':
                # إضافة شحنة جديدة
                self.insert_new_shipment(shipment_data)
                messagebox.showinfo("نجح", "تم إضافة الشحنة بنجاح")
            else:
                # تعديل شحنة موجودة
                self.update_existing_shipment(shipment_data)
                messagebox.showinfo("نجح", "تم تعديل الشحنة بنجاح")

            # إغلاق النافذة
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الشحنة: {str(e)}")

    def validate_form(self):
        """التحقق من صحة بيانات النموذج"""
        # الحقول المطلوبة
        required_fields = [
            ('shipment_number', 'رقم الشحنة'),
            ('supplier_id', 'المورد'),
            ('status', 'حالة الشحنة'),
            ('shipment_date', 'تاريخ الشحن'),
            ('departure_port', 'ميناء المغادرة'),
            ('arrival_port', 'ميناء الوصول'),
            ('shipping_company', 'شركة الشحن'),
            ('currency', 'العملة'),
            ('total_value', 'القيمة الإجمالية')
        ]

        for field_name, field_label in required_fields:
            if not self.form_vars[field_name].get().strip():
                messagebox.showerror("خطأ", f"يرجى ملء حقل {field_label}")
                return False

        # التحقق من صحة التواريخ
        try:
            shipment_date = self.form_vars['shipment_date'].get()
            if shipment_date:
                datetime.strptime(shipment_date, '%Y-%m-%d')

            arrival_date = self.form_vars['expected_arrival_date'].get()
            if arrival_date:
                datetime.strptime(arrival_date, '%Y-%m-%d')

        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
            return False

        # التحقق من صحة القيم المالية
        try:
            float(self.form_vars['total_value'].get())

            if self.form_vars['shipping_cost'].get():
                float(self.form_vars['shipping_cost'].get())

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم مالية صحيحة")
            return False

        return True

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        data = {}

        # جمع البيانات من المتغيرات
        for field_name, var in self.form_vars.items():
            value = var.get().strip()

            # معالجة خاصة لبعض الحقول
            if field_name == 'supplier_id':
                # استخراج معرف المورد من النص
                if value and '(ID: ' in value:
                    supplier_id = value.split('(ID: ')[1].split(')')[0]
                    data[field_name] = int(supplier_id)
                else:
                    data[field_name] = None
            elif field_name in ['total_value', 'shipping_cost', 'additional_fees', 'insurance_cost', 'customs_fees']:
                # تحويل القيم المالية
                data[field_name] = float(value) if value else 0.0
            elif field_name in ['weight', 'volume']:
                # تحويل القيم الرقمية
                data[field_name] = float(value) if value else None
            else:
                data[field_name] = value if value else None

        # إضافة الملاحظات
        if hasattr(self, 'notes_text'):
            notes = self.notes_text.get('1.0', tk.END).strip()
            data['notes'] = notes if notes else None

        # إضافة معلومات إضافية
        if self.mode == 'add':
            data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.now().isoformat()
            data['updated_at'] = datetime.now().isoformat()
        else:
            data['id'] = self.shipment_data['id']
            data['updated_at'] = datetime.now().isoformat()

        return data

    def insert_new_shipment(self, data):
        """إدراج شحنة جديدة"""
        # إدراج الشحنة الرئيسية
        query = """
            INSERT INTO shipments (
                id, shipment_number, supplier_id, status, shipment_date, expected_arrival_date,
                departure_port, arrival_port, shipping_company, container_number, bill_of_lading,
                container_type, weight, volume, currency, total_value, shipping_cost, additional_fees,
                insurance_cost, customs_fees, payment_method, payment_status, notes, created_at, updated_at, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        values = (
            data['id'], data['shipment_number'], data['supplier_id'], data['status'],
            data['shipment_date'], data['expected_arrival_date'], data['departure_port'],
            data['arrival_port'], data['shipping_company'], data['container_number'],
            data['bill_of_lading'], data['container_type'], data['weight'], data['volume'],
            data['currency'], data['total_value'], data['shipping_cost'], data['additional_fees'],
            data['insurance_cost'], data['customs_fees'], data['payment_method'],
            data['payment_status'], data['notes'], data['created_at'], data['updated_at'], data['created_by']
        )

        self.db_manager.execute_query(query, values)

        # إدراج الأصناف
        self.insert_shipment_items(data['id'])

    def update_existing_shipment(self, data):
        """تحديث شحنة موجودة"""
        # تحديث الشحنة الرئيسية
        query = """
            UPDATE shipments SET
                supplier_id = ?, status = ?, shipment_date = ?, expected_arrival_date = ?,
                departure_port = ?, arrival_port = ?, shipping_company = ?, container_number = ?,
                bill_of_lading = ?, container_type = ?, weight = ?, volume = ?, currency = ?,
                total_value = ?, shipping_cost = ?, additional_fees = ?, insurance_cost = ?,
                customs_fees = ?, payment_method = ?, payment_status = ?, notes = ?, updated_at = ?
            WHERE id = ?
        """

        values = (
            data['supplier_id'], data['status'], data['shipment_date'], data['expected_arrival_date'],
            data['departure_port'], data['arrival_port'], data['shipping_company'],
            data['container_number'], data['bill_of_lading'], data['container_type'],
            data['weight'], data['volume'], data['currency'], data['total_value'],
            data['shipping_cost'], data['additional_fees'], data['insurance_cost'],
            data['customs_fees'], data['payment_method'], data['payment_status'],
            data['notes'], data['updated_at'], data['id']
        )

        self.db_manager.execute_query(query, values)

        # حذف الأصناف القديمة وإدراج الجديدة
        self.db_manager.execute_query("DELETE FROM shipment_items WHERE shipment_id = ?", (data['id'],))
        self.insert_shipment_items(data['id'])

    def insert_shipment_items(self, shipment_id):
        """إدراج أصناف الشحنة"""
        if not self.items_data:
            return

        query = """
            INSERT INTO shipment_items (
                id, shipment_id, item_code, item_name, quantity, unit, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """

        for item in self.items_data:
            values = (
                str(uuid.uuid4()), shipment_id, item['item_code'], item['item_name'],
                item['quantity'], item['unit'], item['unit_price'], item['total_price']
            )
            self.db_manager.execute_query(query, values)

    def cancel_form(self):
        """إلغاء النموذج"""
        # التحقق من وجود تغييرات غير محفوظة
        if self.has_unsaved_changes():
            result = messagebox.askyesno(
                "تأكيد الإلغاء",
                "هناك تغييرات غير محفوظة. هل أنت متأكد من الإلغاء؟"
            )
            if not result:
                return

        self.root.destroy()

    def has_unsaved_changes(self):
        """التحقق من وجود تغييرات غير محفوظة"""
        # فحص بسيط - يمكن تحسينه لاحقاً
        if self.mode == 'add':
            # إذا كان هناك أي بيانات مدخلة
            for var in self.form_vars.values():
                if var.get().strip():
                    return True

        return False

    def show_help(self):
        """إظهار المساعدة"""
        help_text = """
📦 نموذج الشحنة - المساعدة

📋 التبويبات:
• المعلومات الأساسية: رقم الشحنة، المورد، الحالة
• معلومات الشحن: التواريخ، الموانئ، شركة الشحن
• المعلومات المالية: القيم، التكاليف، طريقة الدفع
• الأصناف: قائمة الأصناف المشحونة
• الملاحظات والمرفقات: معلومات إضافية

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+S: حفظ النموذج
• Esc: إلغاء النموذج
• F1: هذه المساعدة

📝 ملاحظات:
• الحقول المميزة بـ * مطلوبة
• تنسيق التاريخ: YYYY-MM-DD
• يمكن إضافة عدة أصناف للشحنة الواحدة
• يمكن إرفاق ملفات مختلفة

💡 نصائح:
• تأكد من صحة أرقام الحاويات
• راجع التواريخ قبل الحفظ
• أضف ملاحظات مفصلة للمتابعة
        """

        messagebox.showinfo("مساعدة - نموذج الشحنة", help_text)

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.cancel_form()

if __name__ == "__main__":
    # اختبار النافذة
    app = ShipmentFormWindow(mode='add')
    app.root.mainloop()
