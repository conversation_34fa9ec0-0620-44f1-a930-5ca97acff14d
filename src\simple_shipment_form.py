#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الشحنة المبسط مع دعم RTL كامل
Simple Shipment Form with Full RTL Support
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys
from datetime import datetime, date
import uuid

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES, CONTAINER_TYPES, PAYMENT_METHODS
from database.database_manager import DatabaseManager
from src.simple_rtl_components import *
from src.auth_manager import auth_manager

class SimpleShipmentForm:
    """نموذج الشحنة المبسط"""
    
    def __init__(self, parent, mode='add', shipment_data=None):
        self.parent = parent
        self.mode = mode  # add, edit, duplicate, view
        self.shipment_data = shipment_data or {}
        self.db_manager = DatabaseManager()
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات النموذج
        self.form_vars = {}
        self.is_modified = False
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_form_data()
        
        # توسيط النافذة
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة"""
        titles = {
            'add': '➕ إضافة شحنة جديدة',
            'edit': '✏️ تعديل الشحنة',
            'duplicate': '📋 نسخ الشحنة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }
        
        self.root.title(titles.get(self.mode, 'نموذج الشحنة'))
        self.root.geometry("1000x700")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        
        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Control-s>', lambda e: self.save_shipment())
        self.root.bind('<Escape>', lambda e: self.close_form())
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        self.main_frame = create_simple_rtl_frame(self.root)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header()
        
        # شريط الأدوات
        self.create_toolbar()
        
        # منطقة النموذج
        self.create_form_area()
        
        # شريط الحالة والأزرار
        self.create_footer()
        
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = create_simple_rtl_frame(self.main_frame)
        header_frame.configure(bg=COLORS['primary'], height=60)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_text = {
            'add': '🚢 إضافة شحنة جديدة',
            'edit': '✏️ تعديل بيانات الشحنة',
            'duplicate': '📋 نسخ شحنة موجودة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }
        
        title_label = create_simple_rtl_label(
            header_frame,
            text=title_text.get(self.mode, 'نموذج الشحنة'),
            font=('Segoe UI', 20, 'bold'),
            fg=COLORS['text_white'],
            bg=COLORS['primary']
        )
        title_label.pack(expand=True, pady=15)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = create_simple_rtl_toolbar(self.main_frame)
        self.toolbar.pack(fill='x', pady=(0, 10))
        
        if self.mode != 'view':
            self.toolbar.add_button("💾 حفظ", self.save_shipment, "success")
            self.toolbar.add_button("💾 حفظ وإغلاق", self.save_and_close, "primary")
        
        self.toolbar.add_button("🔄 إعادة تعيين", self.reset_form, "outline")
        self.toolbar.add_button("❌ إغلاق", self.close_form, "danger")
        
    def create_form_area(self):
        """إنشاء منطقة النموذج"""
        # إطار قابل للتمرير
        canvas = tk.Canvas(self.main_frame, bg=COLORS['background'])
        scrollbar = tk.Scrollbar(self.main_frame, orient="vertical", command=canvas.yview)
        self.form_container = create_simple_rtl_frame(canvas)
        
        self.form_container.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.form_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط منطقة النموذج
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # إنشاء أقسام النموذج
        self.create_basic_info_section()
        self.create_shipping_info_section()
        self.create_financial_info_section()
        self.create_notes_section()
        
    def create_basic_info_section(self):
        """إنشاء قسم المعلومات الأساسية"""
        section = create_simple_rtl_card(
            self.form_container,
            title="📋 المعلومات الأساسية"
        )
        section.pack(fill='x', padx=10, pady=10)
        
        content = create_simple_rtl_frame(section)
        content.pack(fill='x', padx=20, pady=15)
        
        # الصف الأول
        row1 = create_simple_rtl_frame(content)
        row1.pack(fill='x', pady=(0, 15))
        
        # رقم الشحنة
        shipment_frame = create_simple_rtl_frame(row1)
        shipment_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        shipment_label = create_simple_rtl_label(
            shipment_frame,
            text="🔢 رقم الشحنة *",
            font=('Segoe UI', 12, 'bold')
        )
        shipment_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['shipment_number'] = tk.StringVar()
        shipment_entry = create_simple_rtl_entry(
            shipment_frame,
            textvariable=self.form_vars['shipment_number']
        )
        shipment_entry.pack(fill='x', ipady=8)
        
        # المورد
        supplier_frame = create_simple_rtl_frame(row1)
        supplier_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        supplier_label = create_simple_rtl_label(
            supplier_frame,
            text="🏢 المورد *",
            font=('Segoe UI', 12, 'bold')
        )
        supplier_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['supplier_id'] = tk.StringVar()
        self.supplier_combo = create_simple_rtl_combobox(
            supplier_frame,
            textvariable=self.form_vars['supplier_id']
        )
        self.supplier_combo.pack(fill='x', ipady=8)
        
        # تاريخ الشحن
        date_frame = create_simple_rtl_frame(row1)
        date_frame.pack(side='right', fill='x', expand=True)
        
        date_label = create_simple_rtl_label(
            date_frame,
            text="📅 تاريخ الشحن *",
            font=('Segoe UI', 12, 'bold')
        )
        date_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['shipment_date'] = tk.StringVar()
        date_entry = create_simple_rtl_entry(
            date_frame,
            textvariable=self.form_vars['shipment_date'],
            placeholder="YYYY-MM-DD"
        )
        date_entry.pack(fill='x', ipady=8)
        
        # الصف الثاني
        row2 = create_simple_rtl_frame(content)
        row2.pack(fill='x', pady=(15, 0))
        
        # الحالة
        status_frame = create_simple_rtl_frame(row2)
        status_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        status_label = create_simple_rtl_label(
            status_frame,
            text="📊 حالة الشحنة *",
            font=('Segoe UI', 12, 'bold')
        )
        status_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['status'] = tk.StringVar()
        self.status_combo = create_simple_rtl_combobox(
            status_frame,
            textvariable=self.form_vars['status']
        )
        self.status_combo['values'] = list(SHIPMENT_STATUS.values())
        self.status_combo.pack(fill='x', ipady=8)
        
        # تاريخ الوصول المتوقع
        arrival_frame = create_simple_rtl_frame(row2)
        arrival_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        arrival_label = create_simple_rtl_label(
            arrival_frame,
            text="🏁 تاريخ الوصول المتوقع",
            font=('Segoe UI', 12, 'bold')
        )
        arrival_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['expected_arrival_date'] = tk.StringVar()
        arrival_entry = create_simple_rtl_entry(
            arrival_frame,
            textvariable=self.form_vars['expected_arrival_date'],
            placeholder="YYYY-MM-DD"
        )
        arrival_entry.pack(fill='x', ipady=8)
        
        # رقم الحاوية
        container_frame = create_simple_rtl_frame(row2)
        container_frame.pack(side='right', fill='x', expand=True)
        
        container_label = create_simple_rtl_label(
            container_frame,
            text="📦 رقم الحاوية",
            font=('Segoe UI', 12, 'bold')
        )
        container_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['container_number'] = tk.StringVar()
        container_entry = create_simple_rtl_entry(
            container_frame,
            textvariable=self.form_vars['container_number']
        )
        container_entry.pack(fill='x', ipady=8)

    def create_shipping_info_section(self):
        """إنشاء قسم معلومات الشحن"""
        section = create_simple_rtl_card(
            self.form_container,
            title="🚢 معلومات الشحن والنقل"
        )
        section.pack(fill='x', padx=10, pady=10)

        content = create_simple_rtl_frame(section)
        content.pack(fill='x', padx=20, pady=15)

        # الصف الأول
        row1 = create_simple_rtl_frame(content)
        row1.pack(fill='x', pady=(0, 15))

        # ميناء المغادرة
        dep_port_frame = create_simple_rtl_frame(row1)
        dep_port_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        dep_port_label = create_simple_rtl_label(
            dep_port_frame,
            text="🚢 ميناء المغادرة",
            font=('Segoe UI', 12, 'bold')
        )
        dep_port_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['departure_port'] = tk.StringVar()
        self.dep_port_combo = create_simple_rtl_combobox(
            dep_port_frame,
            textvariable=self.form_vars['departure_port']
        )
        self.dep_port_combo['values'] = list(PORTS.keys())
        self.dep_port_combo.pack(fill='x', ipady=8)

        # ميناء الوصول
        arr_port_frame = create_simple_rtl_frame(row1)
        arr_port_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        arr_port_label = create_simple_rtl_label(
            arr_port_frame,
            text="🏁 ميناء الوصول",
            font=('Segoe UI', 12, 'bold')
        )
        arr_port_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['arrival_port'] = tk.StringVar()
        self.arr_port_combo = create_simple_rtl_combobox(
            arr_port_frame,
            textvariable=self.form_vars['arrival_port']
        )
        self.arr_port_combo['values'] = list(PORTS.keys())
        self.arr_port_combo.pack(fill='x', ipady=8)

        # شركة الشحن
        shipping_frame = create_simple_rtl_frame(row1)
        shipping_frame.pack(side='right', fill='x', expand=True)

        shipping_label = create_simple_rtl_label(
            shipping_frame,
            text="🏢 شركة الشحن",
            font=('Segoe UI', 12, 'bold')
        )
        shipping_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['shipping_company'] = tk.StringVar()
        self.shipping_combo = create_simple_rtl_combobox(
            shipping_frame,
            textvariable=self.form_vars['shipping_company']
        )
        self.shipping_combo['values'] = list(SHIPPING_COMPANIES.keys())
        self.shipping_combo.pack(fill='x', ipady=8)

        # الصف الثاني
        row2 = create_simple_rtl_frame(content)
        row2.pack(fill='x', pady=(15, 0))

        # رقم بوليصة الشحن
        bol_frame = create_simple_rtl_frame(row2)
        bol_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        bol_label = create_simple_rtl_label(
            bol_frame,
            text="📋 رقم بوليصة الشحن",
            font=('Segoe UI', 12, 'bold')
        )
        bol_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['bill_of_lading'] = tk.StringVar()
        bol_entry = create_simple_rtl_entry(
            bol_frame,
            textvariable=self.form_vars['bill_of_lading']
        )
        bol_entry.pack(fill='x', ipady=8)

        # نوع الحاوية
        container_type_frame = create_simple_rtl_frame(row2)
        container_type_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        container_type_label = create_simple_rtl_label(
            container_type_frame,
            text="📏 نوع الحاوية",
            font=('Segoe UI', 12, 'bold')
        )
        container_type_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['container_type'] = tk.StringVar()
        self.container_type_combo = create_simple_rtl_combobox(
            container_type_frame,
            textvariable=self.form_vars['container_type']
        )
        self.container_type_combo['values'] = list(CONTAINER_TYPES.keys())
        self.container_type_combo.pack(fill='x', ipady=8)

        # الوزن
        weight_frame = create_simple_rtl_frame(row2)
        weight_frame.pack(side='right', fill='x', expand=True)

        weight_label = create_simple_rtl_label(
            weight_frame,
            text="⚖️ الوزن (كجم)",
            font=('Segoe UI', 12, 'bold')
        )
        weight_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['weight'] = tk.StringVar()
        weight_entry = create_simple_rtl_entry(
            weight_frame,
            textvariable=self.form_vars['weight']
        )
        weight_entry.pack(fill='x', ipady=8)

    def create_financial_info_section(self):
        """إنشاء قسم المعلومات المالية"""
        section = create_simple_rtl_card(
            self.form_container,
            title="💰 المعلومات المالية"
        )
        section.pack(fill='x', padx=10, pady=10)

        content = create_simple_rtl_frame(section)
        content.pack(fill='x', padx=20, pady=15)

        # الصف الأول
        row1 = create_simple_rtl_frame(content)
        row1.pack(fill='x', pady=(0, 15))

        # العملة
        currency_frame = create_simple_rtl_frame(row1)
        currency_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        currency_label = create_simple_rtl_label(
            currency_frame,
            text="💱 العملة",
            font=('Segoe UI', 12, 'bold')
        )
        currency_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['currency'] = tk.StringVar()
        self.currency_combo = create_simple_rtl_combobox(
            currency_frame,
            textvariable=self.form_vars['currency']
        )
        self.currency_combo['values'] = list(CURRENCIES.keys())
        self.currency_combo.set('USD')
        self.currency_combo.pack(fill='x', ipady=8)

        # القيمة الإجمالية
        total_value_frame = create_simple_rtl_frame(row1)
        total_value_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        total_value_label = create_simple_rtl_label(
            total_value_frame,
            text="💰 القيمة الإجمالية",
            font=('Segoe UI', 12, 'bold')
        )
        total_value_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['total_value'] = tk.StringVar()
        total_value_entry = create_simple_rtl_entry(
            total_value_frame,
            textvariable=self.form_vars['total_value']
        )
        total_value_entry.pack(fill='x', ipady=8)

        # تكلفة الشحن
        shipping_cost_frame = create_simple_rtl_frame(row1)
        shipping_cost_frame.pack(side='right', fill='x', expand=True)

        shipping_cost_label = create_simple_rtl_label(
            shipping_cost_frame,
            text="🚢 تكلفة الشحن",
            font=('Segoe UI', 12, 'bold')
        )
        shipping_cost_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['shipping_cost'] = tk.StringVar()
        shipping_cost_entry = create_simple_rtl_entry(
            shipping_cost_frame,
            textvariable=self.form_vars['shipping_cost']
        )
        shipping_cost_entry.pack(fill='x', ipady=8)

        # الصف الثاني
        row2 = create_simple_rtl_frame(content)
        row2.pack(fill='x', pady=(15, 0))

        # طريقة الدفع
        payment_method_frame = create_simple_rtl_frame(row2)
        payment_method_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        payment_method_label = create_simple_rtl_label(
            payment_method_frame,
            text="💳 طريقة الدفع",
            font=('Segoe UI', 12, 'bold')
        )
        payment_method_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['payment_method'] = tk.StringVar()
        self.payment_method_combo = create_simple_rtl_combobox(
            payment_method_frame,
            textvariable=self.form_vars['payment_method']
        )
        self.payment_method_combo['values'] = list(PAYMENT_METHODS.keys())
        self.payment_method_combo.pack(fill='x', ipady=8)

        # حالة الدفع
        payment_status_frame = create_simple_rtl_frame(row2)
        payment_status_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        payment_status_label = create_simple_rtl_label(
            payment_status_frame,
            text="💰 حالة الدفع",
            font=('Segoe UI', 12, 'bold')
        )
        payment_status_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['payment_status'] = tk.StringVar()
        self.payment_status_combo = create_simple_rtl_combobox(
            payment_status_frame,
            textvariable=self.form_vars['payment_status']
        )
        self.payment_status_combo['values'] = ['لم يتم الدفع', 'دفع جزئي', 'تم الدفع بالكامل', 'مسترد']
        self.payment_status_combo.set('لم يتم الدفع')
        self.payment_status_combo.pack(fill='x', ipady=8)

        # رسوم إضافية
        additional_fees_frame = create_simple_rtl_frame(row2)
        additional_fees_frame.pack(side='right', fill='x', expand=True)

        additional_fees_label = create_simple_rtl_label(
            additional_fees_frame,
            text="💸 رسوم إضافية",
            font=('Segoe UI', 12, 'bold')
        )
        additional_fees_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['additional_fees'] = tk.StringVar()
        additional_fees_entry = create_simple_rtl_entry(
            additional_fees_frame,
            textvariable=self.form_vars['additional_fees']
        )
        additional_fees_entry.pack(fill='x', ipady=8)

    def create_notes_section(self):
        """إنشاء قسم الملاحظات"""
        section = create_simple_rtl_card(
            self.form_container,
            title="📝 الملاحظات"
        )
        section.pack(fill='x', padx=10, pady=10)

        content = create_simple_rtl_frame(section)
        content.pack(fill='x', padx=20, pady=15)

        notes_label = create_simple_rtl_label(
            content,
            text="📝 ملاحظات إضافية:",
            font=('Segoe UI', 12, 'bold')
        )
        notes_label.pack(anchor='e', pady=(0, 5))

        self.notes_text = create_simple_rtl_text(
            content,
            height=6
        )
        self.notes_text.pack(fill='x', pady=(5, 0))

    def create_footer(self):
        """إنشاء شريط الحالة والأزرار"""
        footer_frame = create_simple_rtl_frame(self.main_frame)
        footer_frame.configure(bg=COLORS['light'], height=50)
        footer_frame.pack(fill='x', side='bottom', pady=(10, 0))
        footer_frame.pack_propagate(False)

        # أزرار الإجراءات
        actions_frame = create_simple_rtl_frame(footer_frame)
        actions_frame.configure(bg=COLORS['light'])
        actions_frame.pack(side='right', padx=20, pady=10)

        if self.mode != 'view':
            save_btn = create_simple_rtl_button(
                actions_frame,
                text="💾 حفظ",
                button_type="success",
                command=self.save_shipment
            )
            save_btn.pack(side='right', padx=5)

            save_close_btn = create_simple_rtl_button(
                actions_frame,
                text="💾 حفظ وإغلاق",
                button_type="primary",
                command=self.save_and_close
            )
            save_close_btn.pack(side='right', padx=5)

        close_btn = create_simple_rtl_button(
            actions_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_form
        )
        close_btn.pack(side='right', padx=5)

        # معلومات الحالة
        self.status_label = create_simple_rtl_label(
            footer_frame,
            text="جاهز",
            bg=COLORS['light']
        )
        self.status_label.pack(side='left', padx=20, pady=10)

    def load_form_data(self):
        """تحميل بيانات النموذج"""
        try:
            # تحميل قائمة الموردين
            self.load_suppliers()

            # تعيين القيم الافتراضية للشحنات الجديدة
            if self.mode == 'add':
                self.set_default_values()

            # تحميل بيانات الشحنة إذا كانت موجودة
            elif self.shipment_data and self.mode in ['edit', 'duplicate', 'view']:
                self.populate_form_data()

            # تطبيق قيود العرض فقط
            if self.mode == 'view':
                self.make_form_readonly()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all(
                "SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            )

            supplier_values = [f"{s['supplier_name']} (ID: {s['id']})" for s in suppliers]
            self.supplier_combo['values'] = supplier_values

            # حفظ بيانات الموردين للمرجع
            self.suppliers_data = suppliers

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def set_default_values(self):
        """تعيين القيم الافتراضية"""
        # إنشاء رقم شحنة تلقائي
        self.form_vars['shipment_number'].set(self.generate_shipment_number())

        # تعيين التاريخ الحالي
        self.form_vars['shipment_date'].set(date.today().strftime('%Y-%m-%d'))

        # تعيين الحالة الافتراضية
        self.form_vars['status'].set('في الانتظار')

        # تعيين العملة الافتراضية
        self.form_vars['currency'].set('USD')

        # تعيين حالة الدفع الافتراضية
        self.form_vars['payment_status'].set('لم يتم الدفع')

    def generate_shipment_number(self):
        """إنشاء رقم شحنة تلقائي"""
        try:
            # الحصول على آخر رقم شحنة
            last_shipment = self.db_manager.fetch_one(
                "SELECT shipment_number FROM shipments ORDER BY created_at DESC LIMIT 1"
            )

            if last_shipment and last_shipment['shipment_number']:
                last_number = last_shipment['shipment_number']
                if last_number.startswith('SH-'):
                    try:
                        number_part = int(last_number.split('-')[1])
                        new_number = number_part + 1
                        return f"SH-{new_number:06d}"
                    except:
                        pass

            return f"SH-{1:06d}"

        except Exception:
            return f"SH-{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def populate_form_data(self):
        """ملء النموذج بالبيانات"""
        try:
            # ملء جميع الحقول
            for field, var in self.form_vars.items():
                value = self.shipment_data.get(field, '')
                if value:
                    var.set(str(value))

            # معالجة خاصة للمورد
            supplier_id = self.shipment_data.get('supplier_id')
            if supplier_id and hasattr(self, 'suppliers_data'):
                for supplier in self.suppliers_data:
                    if supplier['id'] == supplier_id:
                        supplier_text = f"{supplier['supplier_name']} (ID: {supplier['id']})"
                        self.form_vars['supplier_id'].set(supplier_text)
                        break

            # ملء منطقة الملاحظات
            notes = self.shipment_data.get('notes', '')
            if notes:
                self.notes_text.delete('1.0', tk.END)
                self.notes_text.insert('1.0', notes)

            # إذا كان الوضع نسخ، مسح بعض الحقول
            if self.mode == 'duplicate':
                self.form_vars['shipment_number'].set(self.generate_shipment_number())
                self.form_vars['status'].set('في الانتظار')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في ملء البيانات: {str(e)}")

    def make_form_readonly(self):
        """جعل النموذج للقراءة فقط"""
        # تعطيل جميع حقول الإدخال
        for widget in self.form_container.winfo_children():
            self.disable_widget_recursively(widget)

    def disable_widget_recursively(self, widget):
        """تعطيل عنصر وجميع عناصره الفرعية"""
        try:
            if hasattr(widget, 'configure'):
                if isinstance(widget, (tk.Entry, tk.Combobox)):
                    widget.configure(state='readonly')
                elif isinstance(widget, tk.Text):
                    widget.configure(state='disabled')
                elif isinstance(widget, tk.Button):
                    widget.configure(state='disabled')

            # تطبيق على العناصر الفرعية
            for child in widget.winfo_children():
                self.disable_widget_recursively(child)

        except Exception:
            pass

    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return False

            # جمع البيانات
            shipment_data = self.collect_form_data()

            # حفظ في قاعدة البيانات
            if self.mode == 'add' or self.mode == 'duplicate':
                self.create_new_shipment(shipment_data)
            elif self.mode == 'edit':
                self.update_existing_shipment(shipment_data)

            # تحديث المؤشرات
            self.is_modified = False
            self.status_label.configure(text="✓ تم الحفظ", fg=COLORS['success'])

            messagebox.showinfo("نجح", "تم حفظ الشحنة بنجاح")
            return True

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الشحنة: {str(e)}")
            return False

    def validate_form(self):
        """التحقق من صحة النموذج"""
        errors = []

        # التحقق من الحقول المطلوبة
        required_fields = {
            'shipment_number': 'رقم الشحنة',
            'supplier_id': 'المورد',
            'shipment_date': 'تاريخ الشحن',
            'status': 'حالة الشحنة'
        }

        for field, label in required_fields.items():
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if not value:
                errors.append(f"حقل {label} مطلوب")

        # التحقق من صحة التواريخ
        date_fields = ['shipment_date', 'expected_arrival_date']
        for field in date_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_date(value):
                errors.append(f"تاريخ {field} غير صحيح")

        # عرض الأخطاء إن وجدت
        if errors:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("أخطاء في البيانات", error_message)
            return False

        return True

    def is_valid_date(self, date_string):
        """التحقق من صحة التاريخ"""
        try:
            datetime.strptime(date_string, '%Y-%m-%d')
            return True
        except:
            return False

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        data = {}

        # جمع البيانات من المتغيرات
        for field, var in self.form_vars.items():
            value = var.get().strip()
            data[field] = value if value else None

        # معالجة خاصة للمورد
        supplier_text = self.form_vars.get('supplier_id', tk.StringVar()).get()
        if supplier_text and '(ID:' in supplier_text:
            try:
                supplier_id = int(supplier_text.split('(ID:')[1].split(')')[0].strip())
                data['supplier_id'] = supplier_id
            except:
                data['supplier_id'] = None

        # جمع الملاحظات
        notes = self.notes_text.get('1.0', tk.END).strip()
        data['notes'] = notes if notes else None

        # إضافة معلومات النظام
        current_user = auth_manager.get_current_user()
        data['created_by'] = current_user.get('username', 'system')
        data['updated_at'] = datetime.now().isoformat()

        if self.mode == 'add' or self.mode == 'duplicate':
            data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.now().isoformat()

        return data

    def create_new_shipment(self, data):
        """إنشاء شحنة جديدة"""
        query = """
            INSERT INTO shipments (
                id, shipment_number, supplier_id, status, shipment_date, expected_arrival_date,
                departure_port, arrival_port, shipping_company, container_number, bill_of_lading,
                container_type, weight, currency, total_value, shipping_cost,
                additional_fees, payment_method, payment_status, notes, created_by, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
        """

        params = (
            data.get('id'),
            data.get('shipment_number'),
            data.get('supplier_id'),
            data.get('status'),
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('departure_port'),
            data.get('arrival_port'),
            data.get('shipping_company'),
            data.get('container_number'),
            data.get('bill_of_lading'),
            data.get('container_type'),
            data.get('weight'),
            data.get('currency'),
            data.get('total_value'),
            data.get('shipping_cost'),
            data.get('additional_fees'),
            data.get('payment_method'),
            data.get('payment_status'),
            data.get('notes'),
            data.get('created_by'),
            data.get('created_at'),
            data.get('updated_at')
        )

        self.db_manager.execute_query(query, params)

    def update_existing_shipment(self, data):
        """تحديث شحنة موجودة"""
        query = """
            UPDATE shipments SET
                shipment_number = ?, supplier_id = ?, status = ?, shipment_date = ?,
                expected_arrival_date = ?, departure_port = ?, arrival_port = ?,
                shipping_company = ?, container_number = ?, bill_of_lading = ?,
                container_type = ?, weight = ?, currency = ?, total_value = ?,
                shipping_cost = ?, additional_fees = ?, payment_method = ?,
                payment_status = ?, notes = ?, updated_at = ?
            WHERE id = ?
        """

        params = (
            data.get('shipment_number'),
            data.get('supplier_id'),
            data.get('status'),
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('departure_port'),
            data.get('arrival_port'),
            data.get('shipping_company'),
            data.get('container_number'),
            data.get('bill_of_lading'),
            data.get('container_type'),
            data.get('weight'),
            data.get('currency'),
            data.get('total_value'),
            data.get('shipping_cost'),
            data.get('additional_fees'),
            data.get('payment_method'),
            data.get('payment_status'),
            data.get('notes'),
            data.get('updated_at'),
            self.shipment_data.get('id')
        )

        self.db_manager.execute_query(query, params)

    def save_and_close(self):
        """حفظ وإغلاق"""
        if self.save_shipment():
            self.close_form()

    def reset_form(self):
        """إعادة تعيين النموذج"""
        # مسح جميع الحقول
        for var in self.form_vars.values():
            var.set('')

        # مسح منطقة الملاحظات
        self.notes_text.delete('1.0', tk.END)

        # إعادة تعيين القيم الافتراضية
        if self.mode == 'add':
            self.set_default_values()

        # إعادة تعيين مؤشرات الحالة
        self.is_modified = False
        self.status_label.configure(text="جاهز")

    def close_form(self):
        """إغلاق النموذج"""
        if self.is_modified:
            result = messagebox.askyesnocancel(
                "تأكيد الإغلاق",
                "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟"
            )

            if result is True:  # نعم - احفظ وأغلق
                if not self.save_shipment():
                    return
            elif result is None:  # إلغاء
                return
            # False = لا - أغلق بدون حفظ

        self.root.destroy()

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.close_form()

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

def show_simple_shipment_form(parent, mode='add', shipment_data=None):
    """إظهار نموذج الشحنة المبسط"""
    form = SimpleShipmentForm(parent, mode, shipment_data)
    return form

if __name__ == "__main__":
    # اختبار النموذج
    root = tk.Tk()
    root.withdraw()

    form = SimpleShipmentForm(root, mode='add')
    root.mainloop()
