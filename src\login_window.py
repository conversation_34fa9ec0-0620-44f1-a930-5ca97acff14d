#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from PIL import Image, ImageTk

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from config.config import APP_CONFIG, UI_CONFIG, COLORS, FONTS

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.root.title("تسجيل الدخول - " + APP_CONFIG['name'])
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        self.root.configure(bg=COLORS['background'])
        
        # تعيين الأيقونة إذا كانت متوفرة
        try:
            if os.path.exists(APP_CONFIG['icon_path']):
                self.root.iconbitmap(APP_CONFIG['icon_path'])
        except:
            pass
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=COLORS['white'], relief='raised', bd=2)
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # شعار التطبيق
        logo_frame = tk.Frame(main_frame, bg=COLORS['white'])
        logo_frame.pack(pady=30)
        
        # عنوان التطبيق
        title_label = tk.Label(
            logo_frame,
            text=APP_CONFIG['name'],
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['primary']
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            logo_frame,
            text="نظام متابعة شحنات الموردين",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        subtitle_label.pack(pady=(5, 0))
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=COLORS['white'])
        login_frame.pack(pady=20, padx=40, fill='x')
        
        # اسم المستخدم
        username_label = tk.Label(
            login_frame,
            text="اسم المستخدم:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        username_label.pack(anchor='e', pady=(0, 5))
        
        self.username_entry = tk.Entry(
            login_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            relief='solid',
            bd=1,
            justify='right'
        )
        self.username_entry.pack(fill='x', pady=(0, 15), ipady=8)
        
        # كلمة المرور
        password_label = tk.Label(
            login_frame,
            text="كلمة المرور:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        password_label.pack(anchor='e', pady=(0, 5))
        
        self.password_entry = tk.Entry(
            login_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            show="*",
            relief='solid',
            bd=1,
            justify='right'
        )
        self.password_entry.pack(fill='x', pady=(0, 20), ipady=8)
        
        # زر تسجيل الدخول
        self.login_button = tk.Button(
            login_frame,
            text="تسجيل الدخول",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.login
        )
        self.login_button.pack(fill='x', pady=(0, 10), ipady=10)
        
        # معلومات المستخدم الافتراضي
        info_frame = tk.Frame(main_frame, bg=COLORS['light'], relief='solid', bd=1)
        info_frame.pack(fill='x', padx=40, pady=20)
        
        info_title = tk.Label(
            info_frame,
            text="معلومات تسجيل الدخول الافتراضية:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size'], 'bold'),
            bg=COLORS['light'],
            fg=COLORS['dark']
        )
        info_title.pack(pady=(10, 5))
        
        info_text = tk.Label(
            info_frame,
            text="اسم المستخدم: admin\nكلمة المرور: admin123",
            font=(FONTS['english']['family'], FONTS['english']['size']),
            bg=COLORS['light'],
            fg=COLORS['info'],
            justify='center'
        )
        info_text.pack(pady=(0, 10))
        
        # معلومات الإصدار
        version_label = tk.Label(
            main_frame,
            text=f"الإصدار {APP_CONFIG['version']}",
            font=(FONTS['arabic']['family'], 9),
            bg=COLORS['white'],
            fg=COLORS['secondary']
        )
        version_label.pack(side='bottom', pady=10)
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # تعطيل زر تسجيل الدخول أثناء المعالجة
        self.login_button.config(state='disabled', text='جاري التحقق...')
        self.root.update()
        
        try:
            # محاولة تسجيل الدخول
            result = auth_manager.login(username, password)
            
            if result['success']:
                messagebox.showinfo("نجح", result['message'])
                self.root.destroy()
                # فتح النافذة الرئيسية
                self.open_main_window()
            else:
                messagebox.showerror("خطأ", result['message'])
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
        
        finally:
            # إعادة تفعيل زر تسجيل الدخول
            self.login_button.config(state='normal', text='تسجيل الدخول')
    
    def open_main_window(self):
        """فتح النافذة الرئيسية"""
        try:
            from src.main_window import MainWindow
            main_window = MainWindow()
            main_window.run()
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح النافذة الرئيسية قريباً...")
    
    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    # إنشاء قاعدة البيانات إذا لم تكن موجودة
    from database.database_manager import db_manager
    
    # تشغيل نافذة تسجيل الدخول
    login_window = LoginWindow()
    login_window.run()
