#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين
Suppliers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, COUNTRIES, SAUDI_CITIES

class SuppliersWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_suppliers()

    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الموردين - نظام متابعة الشحنات")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="إدارة الموردين",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(0, 10))

        # أزرار العمليات
        self.add_button = tk.Button(
            buttons_frame,
            text="إضافة مورد جديد",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.add_supplier
        )
        self.add_button.pack(side='right', padx=5)

        self.edit_button = tk.Button(
            buttons_frame,
            text="تعديل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['warning'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.edit_supplier,
            state='disabled'
        )
        self.edit_button.pack(side='right', padx=5)

        self.delete_button = tk.Button(
            buttons_frame,
            text="حذف",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['danger'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.delete_supplier,
            state='disabled'
        )
        self.delete_button.pack(side='right', padx=5)

        self.refresh_button = tk.Button(
            buttons_frame,
            text="تحديث",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.load_suppliers
        )
        self.refresh_button.pack(side='right', padx=5)

        # إطار البحث
        search_frame = tk.Frame(main_frame, bg=COLORS['background'])
        search_frame.pack(fill='x', pady=(0, 10))

        search_label = tk.Label(
            search_frame,
            text="البحث:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        search_label.pack(side='right', padx=(0, 5))

        self.search_entry = tk.Entry(
            search_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            justify='right'
        )
        self.search_entry.pack(side='right', padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.search_suppliers)

        # جدول الموردين
        self.create_suppliers_table(main_frame)

    def create_suppliers_table(self, parent):
        """إنشاء جدول الموردين"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=COLORS['white'])
        table_frame.pack(fill='both', expand=True)

        # إنشاء Treeview
        columns = ('supplier_code', 'supplier_name', 'contact_person', 'phone', 'email', 'city', 'country', 'is_active')
        self.suppliers_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        headers = {
            'supplier_code': 'كود المورد',
            'supplier_name': 'اسم المورد',
            'contact_person': 'الشخص المسؤول',
            'phone': 'الهاتف',
            'email': 'البريد الإلكتروني',
            'city': 'المدينة',
            'country': 'البلد',
            'is_active': 'الحالة'
        }

        for col in columns:
            self.suppliers_tree.heading(col, text=headers[col])
            self.suppliers_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.suppliers_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # ربط الأحداث
        self.suppliers_tree.bind('<<TreeviewSelect>>', self.on_supplier_select)
        self.suppliers_tree.bind('<Double-1>', self.edit_supplier)

    def load_suppliers(self):
        """تحميل الموردين من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT supplier_code, supplier_name, contact_person, phone,
                       email, city, country, is_active
                FROM suppliers
                ORDER BY supplier_name
            ''')

            suppliers = cursor.fetchall()

            for supplier in suppliers:
                status = "نشط" if supplier['is_active'] else "غير نشط"
                values = (
                    supplier['supplier_code'],
                    supplier['supplier_name'],
                    supplier['contact_person'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['city'] or '',
                    supplier['country'] or '',
                    status
                )
                self.suppliers_tree.insert('', 'end', values=values)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")

    def search_suppliers(self, event=None):
        """البحث في الموردين"""
        search_term = self.search_entry.get().strip()

        # مسح البيانات الحالية
        for item in self.suppliers_tree.get_children():
            self.suppliers_tree.delete(item)

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            if search_term:
                cursor.execute('''
                    SELECT supplier_code, supplier_name, contact_person, phone,
                           email, city, country, is_active
                    FROM suppliers
                    WHERE supplier_name LIKE ? OR supplier_code LIKE ? OR contact_person LIKE ?
                    ORDER BY supplier_name
                ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
            else:
                cursor.execute('''
                    SELECT supplier_code, supplier_name, contact_person, phone,
                           email, city, country, is_active
                    FROM suppliers
                    ORDER BY supplier_name
                ''')

            suppliers = cursor.fetchall()

            for supplier in suppliers:
                status = "نشط" if supplier['is_active'] else "غير نشط"
                values = (
                    supplier['supplier_code'],
                    supplier['supplier_name'],
                    supplier['contact_person'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['city'] or '',
                    supplier['country'] or '',
                    status
                )
                self.suppliers_tree.insert('', 'end', values=values)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث: {str(e)}")

    def on_supplier_select(self, event=None):
        """عند اختيار مورد من الجدول"""
        selection = self.suppliers_tree.selection()
        if selection:
            self.edit_button.config(state='normal')
            self.delete_button.config(state='normal')
        else:
            self.edit_button.config(state='disabled')
            self.delete_button.config(state='disabled')

    def add_supplier(self):
        """إضافة مورد جديد"""
        if not auth_manager.has_permission('manage_suppliers'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإضافة الموردين")
            return

        self.open_supplier_form()

    def edit_supplier(self, event=None):
        """تعديل مورد"""
        if not auth_manager.has_permission('manage_suppliers'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل الموردين")
            return

        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return

        item = self.suppliers_tree.item(selection[0])
        supplier_code = item['values'][0]
        self.open_supplier_form(supplier_code)

    def delete_supplier(self):
        """حذف مورد"""
        if not auth_manager.has_permission('manage_suppliers'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لحذف الموردين")
            return

        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return

        item = self.suppliers_tree.item(selection[0])
        supplier_name = item['values'][1]
        supplier_code = item['values'][0]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المورد '{supplier_name}'؟"):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()

                # التحقق من وجود شحنات مرتبطة بالمورد
                cursor.execute('SELECT COUNT(*) as count FROM shipments WHERE supplier_id = (SELECT id FROM suppliers WHERE supplier_code = ?)', (supplier_code,))
                result = cursor.fetchone()

                if result['count'] > 0:
                    messagebox.showerror("خطأ", "لا يمكن حذف هذا المورد لأنه مرتبط بشحنات موجودة")
                    conn.close()
                    return

                # حذف المورد
                cursor.execute('DELETE FROM suppliers WHERE supplier_code = ?', (supplier_code,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.load_suppliers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف المورد: {str(e)}")

    def open_supplier_form(self, supplier_code=None):
        """فتح نموذج المورد"""
        SupplierForm(self.window, self.db_manager, supplier_code, self.load_suppliers)


class SupplierForm:
    def __init__(self, parent, db_manager, supplier_code=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.supplier_code = supplier_code
        self.callback = callback
        self.is_edit_mode = supplier_code is not None

        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()

        if self.is_edit_mode:
            self.load_supplier_data()

    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل مورد" if self.is_edit_mode else "إضافة مورد جديد"
        self.window.title(title)
        self.window.geometry("600x700")
        self.window.configure(bg=COLORS['background'])
        self.window.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر النموذج"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['white'], relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # العنوان
        title = "تعديل بيانات المورد" if self.is_edit_mode else "إضافة مورد جديد"
        title_label = tk.Label(
            main_frame,
            text=title,
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(20, 30))

        # إطار الحقول
        fields_frame = tk.Frame(main_frame, bg=COLORS['white'])
        fields_frame.pack(fill='both', expand=True, padx=40)

        # كود المورد
        self.create_field(fields_frame, "كود المورد:", "supplier_code_entry", required=True)

        # اسم المورد
        self.create_field(fields_frame, "اسم المورد:", "supplier_name_entry", required=True)

        # الشخص المسؤول
        self.create_field(fields_frame, "الشخص المسؤول:", "contact_person_entry")

        # البريد الإلكتروني
        self.create_field(fields_frame, "البريد الإلكتروني:", "email_entry")

        # الهاتف
        self.create_field(fields_frame, "الهاتف:", "phone_entry")

        # الجوال
        self.create_field(fields_frame, "الجوال:", "mobile_entry")

        # العنوان
        self.create_field(fields_frame, "العنوان:", "address_entry", multiline=True)

        # المدينة
        self.create_combobox_field(fields_frame, "المدينة:", "city_combo", SAUDI_CITIES)

        # البلد
        self.create_combobox_field(fields_frame, "البلد:", "country_combo", COUNTRIES)

        # الرقم الضريبي
        self.create_field(fields_frame, "الرقم الضريبي:", "tax_number_entry")

        # شروط الدفع
        self.create_field(fields_frame, "شروط الدفع:", "payment_terms_entry")

        # الحد الائتماني
        self.create_field(fields_frame, "الحد الائتماني:", "credit_limit_entry")

        # الحالة
        status_frame = tk.Frame(fields_frame, bg=COLORS['white'])
        status_frame.pack(fill='x', pady=5)

        status_label = tk.Label(
            status_frame,
            text="الحالة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        status_label.pack(anchor='e')

        self.is_active_var = tk.BooleanVar(value=True)
        self.is_active_check = tk.Checkbutton(
            status_frame,
            text="نشط",
            variable=self.is_active_var,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        self.is_active_check.pack(anchor='e', pady=5)

        # الملاحظات
        self.create_field(fields_frame, "الملاحظات:", "notes_entry", multiline=True)

        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame, bg=COLORS['white'])
        buttons_frame.pack(fill='x', pady=20, padx=40)

        self.save_button = tk.Button(
            buttons_frame,
            text="حفظ",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.save_supplier,
            width=15
        )
        self.save_button.pack(side='right', padx=5)

        cancel_button = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.window.destroy,
            width=15
        )
        cancel_button.pack(side='right', padx=5)

    def create_field(self, parent, label_text, entry_name, required=False, multiline=False):
        """إنشاء حقل إدخال"""
        field_frame = tk.Frame(parent, bg=COLORS['white'])
        field_frame.pack(fill='x', pady=5)

        label_text_with_star = label_text + " *" if required else label_text
        label = tk.Label(
            field_frame,
            text=label_text_with_star,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['danger'] if required else COLORS['dark']
        )
        label.pack(anchor='e')

        if multiline:
            entry = tk.Text(
                field_frame,
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                height=3,
                relief='solid',
                bd=1
            )
        else:
            entry = tk.Entry(
                field_frame,
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                relief='solid',
                bd=1,
                justify='right'
            )

        entry.pack(fill='x', pady=2)
        setattr(self, entry_name, entry)

    def create_combobox_field(self, parent, label_text, combo_name, values):
        """إنشاء حقل اختيار"""
        field_frame = tk.Frame(parent, bg=COLORS['white'])
        field_frame.pack(fill='x', pady=5)

        label = tk.Label(
            field_frame,
            text=label_text,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        label.pack(anchor='e')

        combo = ttk.Combobox(
            field_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            values=values,
            state='readonly',
            justify='right'
        )
        combo.pack(fill='x', pady=2)
        setattr(self, combo_name, combo)

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM suppliers WHERE supplier_code = ?
            ''', (self.supplier_code,))

            supplier = cursor.fetchone()
            conn.close()

            if supplier:
                self.supplier_code_entry.insert(0, supplier['supplier_code'])
                self.supplier_code_entry.config(state='readonly')  # منع تعديل الكود

                self.supplier_name_entry.insert(0, supplier['supplier_name'])

                if supplier['contact_person']:
                    self.contact_person_entry.insert(0, supplier['contact_person'])

                if supplier['email']:
                    self.email_entry.insert(0, supplier['email'])

                if supplier['phone']:
                    self.phone_entry.insert(0, supplier['phone'])

                if supplier['mobile']:
                    self.mobile_entry.insert(0, supplier['mobile'])

                if supplier['address']:
                    self.address_entry.insert('1.0', supplier['address'])

                if supplier['city']:
                    self.city_combo.set(supplier['city'])

                if supplier['country']:
                    self.country_combo.set(supplier['country'])

                if supplier['tax_number']:
                    self.tax_number_entry.insert(0, supplier['tax_number'])

                if supplier['payment_terms']:
                    self.payment_terms_entry.insert(0, supplier['payment_terms'])

                if supplier['credit_limit']:
                    self.credit_limit_entry.insert(0, str(supplier['credit_limit']))

                self.is_active_var.set(supplier['is_active'])

                if supplier['notes']:
                    self.notes_entry.insert('1.0', supplier['notes'])

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات المورد: {str(e)}")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        if not self.supplier_code_entry.get().strip():
            messagebox.showerror("خطأ", "كود المورد مطلوب")
            self.supplier_code_entry.focus()
            return False

        if not self.supplier_name_entry.get().strip():
            messagebox.showerror("خطأ", "اسم المورد مطلوب")
            self.supplier_name_entry.focus()
            return False

        # التحقق من صحة البريد الإلكتروني
        email = self.email_entry.get().strip()
        if email and '@' not in email:
            messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
            self.email_entry.focus()
            return False

        # التحقق من الحد الائتماني
        credit_limit = self.credit_limit_entry.get().strip()
        if credit_limit:
            try:
                float(credit_limit)
            except ValueError:
                messagebox.showerror("خطأ", "الحد الائتماني يجب أن يكون رقماً")
                self.credit_limit_entry.focus()
                return False

        return True

    def save_supplier(self):
        """حفظ بيانات المورد"""
        if not self.validate_data():
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # جمع البيانات
            supplier_code = self.supplier_code_entry.get().strip()
            supplier_name = self.supplier_name_entry.get().strip()
            contact_person = self.contact_person_entry.get().strip() or None
            email = self.email_entry.get().strip() or None
            phone = self.phone_entry.get().strip() or None
            mobile = self.mobile_entry.get().strip() or None
            address = self.address_entry.get('1.0', tk.END).strip() or None
            city = self.city_combo.get() or None
            country = self.country_combo.get() or None
            tax_number = self.tax_number_entry.get().strip() or None
            payment_terms = self.payment_terms_entry.get().strip() or None
            credit_limit = float(self.credit_limit_entry.get().strip()) if self.credit_limit_entry.get().strip() else 0
            is_active = self.is_active_var.get()
            notes = self.notes_entry.get('1.0', tk.END).strip() or None

            if self.is_edit_mode:
                # تحديث المورد
                cursor.execute('''
                    UPDATE suppliers SET
                        supplier_name = ?, contact_person = ?, email = ?, phone = ?,
                        mobile = ?, address = ?, city = ?, country = ?, tax_number = ?,
                        payment_terms = ?, credit_limit = ?, is_active = ?, notes = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE supplier_code = ?
                ''', (supplier_name, contact_person, email, phone, mobile, address,
                      city, country, tax_number, payment_terms, credit_limit,
                      is_active, notes, supplier_code))

                message = "تم تحديث بيانات المورد بنجاح"
            else:
                # التحقق من عدم تكرار الكود
                cursor.execute('SELECT id FROM suppliers WHERE supplier_code = ?', (supplier_code,))
                if cursor.fetchone():
                    messagebox.showerror("خطأ", "كود المورد موجود بالفعل")
                    self.supplier_code_entry.focus()
                    conn.close()
                    return

                # إضافة مورد جديد
                cursor.execute('''
                    INSERT INTO suppliers (
                        supplier_code, supplier_name, contact_person, email, phone,
                        mobile, address, city, country, tax_number, payment_terms,
                        credit_limit, is_active, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (supplier_code, supplier_name, contact_person, email, phone,
                      mobile, address, city, country, tax_number, payment_terms,
                      credit_limit, is_active, notes))

                message = "تم إضافة المورد بنجاح"

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", message)

            # تحديث الجدول في النافذة الرئيسية
            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة الموردين للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    suppliers_window = SuppliersWindow()
    suppliers_window.window.mainloop()