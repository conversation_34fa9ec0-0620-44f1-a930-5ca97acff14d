#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول المتقدمة المبسطة
Simplified Advanced Login Window
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from config.config import APP_CONFIG, COLORS

class SimpleAdvancedLogin:
    def __init__(self):
        self.root = tk.Tk()
        self.login_attempts = 0
        self.max_attempts = 3
        self.setup_window()
        self.create_interface()
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title(f"🔐 تسجيل الدخول - {APP_CONFIG['name']}")
        self.root.geometry("450x600")
        self.root.resizable(False, False)
        self.root.configure(bg=COLORS['background'])
        
        # ربط أحداث لوحة المفاتيح
        self.root.bind('<Return>', self.on_enter_pressed)
        self.root.bind('<Escape>', self.on_escape_pressed)
        
    def create_interface(self):
        """إنشاء واجهة متقدمة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=COLORS['surface'], relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # قسم الشعار والعنوان
        self.create_header(main_frame)
        
        # قسم نموذج تسجيل الدخول
        self.create_login_form(main_frame)
        
        # قسم الخيارات
        self.create_options(main_frame)
        
        # قسم المعلومات
        self.create_info_section(main_frame)
        
    def create_header(self, parent):
        """إنشاء قسم الرأس"""
        header_frame = tk.Frame(parent, bg=COLORS['primary'], height=120)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # الشعار
        logo_label = tk.Label(
            header_frame,
            text="🚢",
            font=('Segoe UI', 36),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        logo_label.pack(pady=(20, 5))
        
        # عنوان التطبيق
        title_label = tk.Label(
            header_frame,
            text=APP_CONFIG['name'],
            font=('Segoe UI', 16, 'bold'),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        title_label.pack()
        
        # وصف
        desc_label = tk.Label(
            header_frame,
            text="نظام متكامل لإدارة ومتابعة الشحنات",
            font=('Segoe UI', 10),
            bg=COLORS['primary'],
            fg=COLORS['text_light']
        )
        desc_label.pack(pady=(5, 15))
        
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = tk.Frame(parent, bg=COLORS['surface'])
        form_frame.pack(fill='x', padx=30, pady=20)
        
        # عنوان النموذج
        form_title = tk.Label(
            form_frame,
            text="🔑 تسجيل الدخول",
            font=('Segoe UI', 14, 'bold'),
            bg=COLORS['surface'],
            fg=COLORS['primary']
        )
        form_title.pack(pady=(0, 20))
        
        # حقل اسم المستخدم
        username_label = tk.Label(
            form_frame,
            text="👤 اسم المستخدم:",
            font=('Segoe UI', 11, 'bold'),
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            anchor='e'
        )
        username_label.pack(fill='x', pady=(0, 5))
        
        self.username_entry = tk.Entry(
            form_frame,
            font=('Segoe UI', 12),
            relief='solid',
            bd=2,
            justify='right'
        )
        self.username_entry.pack(fill='x', ipady=8, pady=(0, 15))
        self.username_entry.insert(0, "admin")
        
        # حقل كلمة المرور
        password_label = tk.Label(
            form_frame,
            text="🔒 كلمة المرور:",
            font=('Segoe UI', 11, 'bold'),
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            anchor='e'
        )
        password_label.pack(fill='x', pady=(0, 5))
        
        password_frame = tk.Frame(form_frame, bg=COLORS['surface'])
        password_frame.pack(fill='x', pady=(0, 15))
        
        self.password_entry = tk.Entry(
            password_frame,
            show="*",
            font=('Segoe UI', 12),
            relief='solid',
            bd=2,
            justify='right'
        )
        self.password_entry.pack(side='right', fill='x', expand=True, ipady=8)
        
        # زر إظهار/إخفاء كلمة المرور
        self.show_password_btn = tk.Button(
            password_frame,
            text="👁️",
            command=self.toggle_password_visibility,
            relief='flat',
            bg=COLORS['surface'],
            fg=COLORS['primary'],
            font=('Segoe UI', 10),
            cursor='hand2',
            width=3
        )
        self.show_password_btn.pack(side='left', padx=(0, 5))
        
        # خيار تذكر بيانات الدخول
        self.remember_var = tk.BooleanVar()
        remember_check = tk.Checkbutton(
            form_frame,
            text="تذكر بيانات الدخول",
            variable=self.remember_var,
            font=('Segoe UI', 10),
            bg=COLORS['surface'],
            fg=COLORS['text_secondary'],
            anchor='e'
        )
        remember_check.pack(fill='x', pady=(0, 20))
        
        # زر تسجيل الدخول
        self.login_btn = tk.Button(
            form_frame,
            text="🚀 تسجيل الدخول",
            command=self.attempt_login,
            font=('Segoe UI', 12, 'bold'),
            bg=COLORS['primary'],
            fg=COLORS['text_white'],
            relief='flat',
            cursor='hand2',
            pady=12
        )
        self.login_btn.pack(fill='x')
        
        # تأثيرات التفاعل للزر
        def on_enter(e):
            self.login_btn.config(bg=COLORS['primary_light'])
        
        def on_leave(e):
            self.login_btn.config(bg=COLORS['primary'])
        
        self.login_btn.bind("<Enter>", on_enter)
        self.login_btn.bind("<Leave>", on_leave)
        
    def create_options(self, parent):
        """إنشاء قسم الخيارات"""
        options_frame = tk.Frame(parent, bg=COLORS['surface'])
        options_frame.pack(fill='x', padx=30, pady=15)
        
        # خط فاصل
        separator_frame = tk.Frame(options_frame, bg=COLORS['surface'])
        separator_frame.pack(fill='x', pady=10)
        
        left_line = tk.Frame(separator_frame, height=1, bg=COLORS['border'])
        left_line.pack(side='left', fill='x', expand=True)
        
        or_label = tk.Label(
            separator_frame,
            text="أو",
            font=('Segoe UI', 9),
            bg=COLORS['surface'],
            fg=COLORS['text_muted']
        )
        or_label.pack(side='left', padx=10)
        
        right_line = tk.Frame(separator_frame, height=1, bg=COLORS['border'])
        right_line.pack(side='left', fill='x', expand=True)
        
        # أزرار الخيارات
        options_buttons = tk.Frame(options_frame, bg=COLORS['surface'])
        options_buttons.pack(fill='x')
        
        forgot_btn = tk.Button(
            options_buttons,
            text="🔄 نسيت كلمة المرور؟",
            command=self.forgot_password,
            font=('Segoe UI', 10),
            bg=COLORS['surface'],
            fg=COLORS['info'],
            relief='flat',
            cursor='hand2'
        )
        forgot_btn.pack(side='right', padx=5)
        
        help_btn = tk.Button(
            options_buttons,
            text="❓ مساعدة",
            command=self.show_help,
            font=('Segoe UI', 10),
            bg=COLORS['surface'],
            fg=COLORS['info'],
            relief='flat',
            cursor='hand2'
        )
        help_btn.pack(side='left', padx=5)
        
    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg=COLORS['light'], relief='solid', bd=1)
        info_frame.pack(fill='x', padx=30, pady=15)
        
        # حالة النظام
        self.status_label = tk.Label(
            info_frame,
            text="🟢 النظام جاهز للاستخدام",
            font=('Segoe UI', 10),
            bg=COLORS['light'],
            fg=COLORS['success'],
            anchor='e'
        )
        self.status_label.pack(fill='x', padx=15, pady=10)
        
        # معلومات الإصدار
        version_info = tk.Label(
            info_frame,
            text=f"الإصدار: {APP_CONFIG['version']} | {datetime.now().strftime('%Y-%m-%d')}",
            font=('Segoe UI', 9),
            bg=COLORS['light'],
            fg=COLORS['text_muted'],
            anchor='e'
        )
        version_info.pack(fill='x', padx=15, pady=(0, 10))
        
        # تذييل
        footer_label = tk.Label(
            parent,
            text=f"© 2024 {APP_CONFIG['author']} - جميع الحقوق محفوظة",
            font=('Segoe UI', 9),
            bg=COLORS['surface'],
            fg=COLORS['text_muted']
        )
        footer_label.pack(pady=(20, 10))
        
    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.password_entry.cget('show') == '*':
            self.password_entry.config(show='')
            self.show_password_btn.config(text='🙈')
        else:
            self.password_entry.config(show='*')
            self.show_password_btn.config(text='👁️')
    
    def on_enter_pressed(self, event):
        """عند الضغط على Enter"""
        self.attempt_login()
    
    def on_escape_pressed(self, event):
        """عند الضغط على Escape"""
        self.root.quit()
    
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # التحقق من صحة البيانات
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # تعطيل زر تسجيل الدخول
        self.login_btn.config(state='disabled', text="🔄 جاري التحقق...")
        self.root.update()
        
        # محاكاة تأخير
        self.root.after(1000, lambda: self.process_login(username, password))
    
    def process_login(self, username, password):
        """معالجة تسجيل الدخول"""
        try:
            if auth_manager.login(username, password):
                self.show_success("تم تسجيل الدخول بنجاح!")
                self.root.after(1000, self.root.destroy)
            else:
                self.login_failed()
        except Exception as e:
            self.show_error(f"خطأ في النظام: {str(e)}")
            self.reset_login_form()
    
    def login_failed(self):
        """فشل تسجيل الدخول"""
        self.login_attempts += 1
        remaining = self.max_attempts - self.login_attempts
        
        if remaining > 0:
            self.show_error(f"اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining}")
        else:
            self.show_error("تم قفل الحساب بسبب المحاولات الفاشلة المتكررة")
            self.login_btn.config(state='disabled')
            return
        
        self.password_entry.delete(0, 'end')
        self.password_entry.focus()
        self.reset_login_form()
    
    def reset_login_form(self):
        """إعادة تعيين نموذج تسجيل الدخول"""
        self.login_btn.config(state='normal', text="🚀 تسجيل الدخول")
    
    def show_success(self, message):
        """إظهار رسالة نجاح"""
        self.status_label.config(text=f"✅ {message}", fg=COLORS['success'])
    
    def show_error(self, message):
        """إظهار رسالة خطأ"""
        self.status_label.config(text=f"❌ {message}", fg=COLORS['danger'])
    
    def forgot_password(self):
        """نسيت كلمة المرور"""
        messagebox.showinfo(
            "استعادة كلمة المرور",
            "لاستعادة كلمة المرور، يرجى التواصل مع مدير النظام.\n\n"
            "البريد الإلكتروني: <EMAIL>\n"
            "الهاتف: +966 11 123 4567"
        )
    
    def show_help(self):
        """إظهار المساعدة"""
        help_text = """تعليمات تسجيل الدخول:

1. أدخل اسم المستخدم وكلمة المرور
2. انقر على زر تسجيل الدخول أو اضغط Enter
3. يمكنك اختيار "تذكر بيانات الدخول"

بيانات الدخول الافتراضية:
• اسم المستخدم: admin
• كلمة المرور: admin123

ملاحظات أمنية:
• يتم قفل الحساب بعد 3 محاولات فاشلة
• تأكد من إغلاق النظام بعد الانتهاء"""
        
        messagebox.showinfo("مساعدة تسجيل الدخول", help_text)
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return auth_manager.is_logged_in()

def show_simple_advanced_login():
    """إظهار نافذة تسجيل الدخول المبسطة"""
    login_window = SimpleAdvancedLogin()
    return login_window.run()

if __name__ == "__main__":
    success = show_simple_advanced_login()
    if success:
        print("تم تسجيل الدخول بنجاح!")
    else:
        print("تم إلغاء تسجيل الدخول")
