#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json

def update_database_schema():
    """تحديث مخطط قاعدة البيانات لإضافة حقول الحاويات الجديدة"""
    
    try:
        conn = sqlite3.connect('shipments.db')
        cursor = conn.cursor()
        
        print("🔄 بدء تحديث مخطط قاعدة البيانات...")
        
        # إضافة حقل عدد الحاويات
        try:
            cursor.execute('''
                ALTER TABLE advanced_shipments 
                ADD COLUMN container_count INTEGER DEFAULT 1
            ''')
            print("✅ تم إضافة حقل container_count")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("ℹ️ حقل container_count موجود بالفعل")
            else:
                raise e
        
        # إضافة حقل أرقام الحاويات JSON
        try:
            cursor.execute('''
                ALTER TABLE advanced_shipments 
                ADD COLUMN container_numbers_json TEXT
            ''')
            print("✅ تم إضافة حقل container_numbers_json")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("ℹ️ حقل container_numbers_json موجود بالفعل")
            else:
                raise e
        
        # تحديث البيانات الموجودة
        print("🔄 تحديث البيانات الموجودة...")
        
        # الحصول على جميع الشحنات التي لها أرقام حاويات
        cursor.execute('''
            SELECT id, container_number 
            FROM advanced_shipments 
            WHERE container_number IS NOT NULL AND container_number != ''
        ''')
        
        shipments = cursor.fetchall()
        
        for shipment_id, container_number in shipments:
            # تحديث عدد الحاويات إلى 1 وحفظ رقم الحاوية في JSON
            container_numbers = [container_number]
            cursor.execute('''
                UPDATE advanced_shipments 
                SET container_count = 1, 
                    container_numbers_json = ?
                WHERE id = ?
            ''', (json.dumps(container_numbers), shipment_id))
        
        # حفظ التغييرات
        conn.commit()
        print(f"✅ تم تحديث {len(shipments)} شحنة")
        
        # عرض هيكل الجدول المحدث
        print("\n📋 هيكل الجدول المحدث:")
        cursor.execute("PRAGMA table_info(advanced_shipments)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} - {col[2]}")
        
        conn.close()
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    update_database_schema()
