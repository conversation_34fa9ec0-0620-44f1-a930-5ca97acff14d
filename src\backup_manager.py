#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة النسخ الاحتياطي
Backup Management System
"""

import os
import sys
import shutil
import sqlite3
import zipfile
from datetime import datetime, timedelta
import logging
from tkinter import messagebox, filedialog
import threading

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database_manager import DatabaseManager
from config.config import get_backups_path, get_database_path, BACKUP_CONFIG

class BackupManager:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.backups_path = get_backups_path()
        self.database_path = get_database_path()
        
    def create_backup(self, backup_name=None, show_message=True):
        """إنشاء نسخة احتياطية"""
        try:
            # تحديد اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            backup_filename = f"{backup_name}.zip"
            backup_filepath = os.path.join(self.backups_path, backup_filename)
            
            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                if os.path.exists(self.database_path):
                    zipf.write(self.database_path, 'database/shipment_system.db')
                
                # إضافة ملفات التكوين
                config_path = os.path.join(os.path.dirname(self.database_path), '..', 'config')
                if os.path.exists(config_path):
                    for root, dirs, files in os.walk(config_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, os.path.dirname(config_path))
                            zipf.write(file_path, arcname)
                
                # إضافة ملفات السجلات (آخر 7 أيام فقط)
                logs_path = os.path.join(os.path.dirname(self.database_path), '..', 'logs')
                if os.path.exists(logs_path):
                    cutoff_date = datetime.now() - timedelta(days=7)
                    for root, dirs, files in os.walk(logs_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                            if file_mtime > cutoff_date:
                                arcname = os.path.relpath(file_path, os.path.dirname(logs_path))
                                zipf.write(file_path, arcname)
            
            # تسجيل النسخة الاحتياطية في قاعدة البيانات
            self._log_backup(backup_name, backup_filepath)
            
            logging.info(f"تم إنشاء نسخة احتياطية: {backup_filepath}")
            
            if show_message:
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_filepath}")
            
            return backup_filepath
            
        except Exception as e:
            error_msg = f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
            logging.error(error_msg)
            if show_message:
                messagebox.showerror("خطأ", error_msg)
            return None
    
    def restore_backup(self, backup_filepath=None):
        """استعادة نسخة احتياطية"""
        try:
            if not backup_filepath:
                backup_filepath = filedialog.askopenfilename(
                    title="اختيار النسخة الاحتياطية للاستعادة",
                    filetypes=[("Backup files", "*.zip"), ("All files", "*.*")],
                    initialdir=self.backups_path
                )
            
            if not backup_filepath or not os.path.exists(backup_filepath):
                return False
            
            # تأكيد الاستعادة
            if not messagebox.askyesno(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n"
                "سيتم استبدال البيانات الحالية.\n\n"
                "يُنصح بإنشاء نسخة احتياطية من البيانات الحالية أولاً."
            ):
                return False
            
            # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            current_backup = self.create_backup("before_restore", show_message=False)
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_filepath, 'r') as zipf:
                # إنشاء مجلد مؤقت للاستخراج
                temp_dir = os.path.join(self.backups_path, 'temp_restore')
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                os.makedirs(temp_dir)
                
                zipf.extractall(temp_dir)
                
                # استعادة قاعدة البيانات
                restored_db_path = os.path.join(temp_dir, 'database', 'shipment_system.db')
                if os.path.exists(restored_db_path):
                    # إغلاق جميع الاتصالات بقاعدة البيانات
                    self.db_manager = None
                    
                    # نسخ قاعدة البيانات المستعادة
                    shutil.copy2(restored_db_path, self.database_path)
                    
                    # إعادة تهيئة مدير قاعدة البيانات
                    self.db_manager = DatabaseManager()
                
                # استعادة ملفات التكوين
                config_dir = os.path.join(temp_dir, 'config')
                if os.path.exists(config_dir):
                    target_config_dir = os.path.join(os.path.dirname(self.database_path), '..', 'config')
                    if os.path.exists(target_config_dir):
                        shutil.rmtree(target_config_dir)
                    shutil.copytree(config_dir, target_config_dir)
                
                # تنظيف المجلد المؤقت
                shutil.rmtree(temp_dir)
            
            logging.info(f"تم استعادة النسخة الاحتياطية: {backup_filepath}")
            messagebox.showinfo(
                "نجح",
                f"تم استعادة النسخة الاحتياطية بنجاح.\n\n"
                f"تم إنشاء نسخة احتياطية من البيانات السابقة:\n{current_backup}\n\n"
                "يُنصح بإعادة تشغيل البرنامج."
            )
            
            return True
            
        except Exception as e:
            error_msg = f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("خطأ", error_msg)
            return False
    
    def auto_backup(self):
        """النسخ الاحتياطي التلقائي"""
        if not BACKUP_CONFIG['auto_backup']:
            return
        
        try:
            # التحقق من آخر نسخة احتياطية
            last_backup_time = self._get_last_backup_time()
            current_time = datetime.now()
            
            if last_backup_time:
                time_diff = current_time - last_backup_time
                if time_diff.total_seconds() < (BACKUP_CONFIG.get('backup_interval_hours', 24) * 3600):
                    return  # لا حاجة للنسخ الاحتياطي بعد
            
            # إنشاء نسخة احتياطية تلقائية
            backup_name = f"auto_backup_{current_time.strftime('%Y%m%d_%H%M%S')}"
            self.create_backup(backup_name, show_message=False)
            
            # تنظيف النسخ الاحتياطية القديمة
            self._cleanup_old_backups()
            
        except Exception as e:
            logging.error(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
    
    def _log_backup(self, backup_name, backup_filepath):
        """تسجيل النسخة الاحتياطية في قاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول النسخ الاحتياطية إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    backup_name TEXT NOT NULL,
                    backup_path TEXT NOT NULL,
                    backup_size INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # حساب حجم الملف
            backup_size = os.path.getsize(backup_filepath) if os.path.exists(backup_filepath) else 0
            
            # إدراج سجل النسخة الاحتياطية
            cursor.execute('''
                INSERT INTO backups (backup_name, backup_path, backup_size)
                VALUES (?, ?, ?)
            ''', (backup_name, backup_filepath, backup_size))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logging.error(f"خطأ في تسجيل النسخة الاحتياطية: {e}")
    
    def _get_last_backup_time(self):
        """الحصول على وقت آخر نسخة احتياطية"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT created_at FROM backups 
                ORDER BY created_at DESC LIMIT 1
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return datetime.strptime(result['created_at'], '%Y-%m-%d %H:%M:%S')
            
            return None
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على وقت آخر نسخة احتياطية: {e}")
            return None
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            keep_days = BACKUP_CONFIG.get('keep_backups_days', 30)
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # الحصول على النسخ الاحتياطية القديمة
            cursor.execute('''
                SELECT backup_path FROM backups 
                WHERE created_at < ?
            ''', (cutoff_date.strftime('%Y-%m-%d %H:%M:%S'),))
            
            old_backups = cursor.fetchall()
            
            # حذف الملفات
            for backup in old_backups:
                backup_path = backup['backup_path']
                if os.path.exists(backup_path):
                    os.remove(backup_path)
            
            # حذف السجلات من قاعدة البيانات
            cursor.execute('''
                DELETE FROM backups WHERE created_at < ?
            ''', (cutoff_date.strftime('%Y-%m-%d %H:%M:%S'),))
            
            conn.commit()
            conn.close()
            
            if old_backups:
                logging.info(f"تم حذف {len(old_backups)} نسخة احتياطية قديمة")
            
        except Exception as e:
            logging.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT backup_name, backup_path, backup_size, created_at
                FROM backups 
                ORDER BY created_at DESC
            ''')
            
            backups = cursor.fetchall()
            conn.close()
            
            return [dict(backup) for backup in backups]
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def export_data(self, export_path=None):
        """تصدير البيانات"""
        try:
            if not export_path:
                export_path = filedialog.askdirectory(title="اختيار مجلد التصدير")
            
            if not export_path:
                return False
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_filename = f"data_export_{timestamp}.zip"
            export_filepath = os.path.join(export_path, export_filename)
            
            # إنشاء ملف التصدير
            with zipfile.ZipFile(export_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                if os.path.exists(self.database_path):
                    zipf.write(self.database_path, 'shipment_system.db')
                
                # إضافة معلومات التصدير
                export_info = f"""
تصدير بيانات نظام متابعة الشحنات
=====================================

تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
إصدار النظام: 1.0.0

المحتويات:
- قاعدة البيانات الكاملة
- جميع بيانات الشحنات والموردين والأصناف

ملاحظة: هذا الملف يحتوي على جميع بيانات النظام
يُرجى الاحتفاظ به في مكان آمن
"""
                zipf.writestr('export_info.txt', export_info.encode('utf-8'))
            
            messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح:\n{export_filepath}")
            return export_filepath
            
        except Exception as e:
            error_msg = f"حدث خطأ في تصدير البيانات: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("خطأ", error_msg)
            return None

# إنشاء مثيل من مدير النسخ الاحتياطي
backup_manager = BackupManager()
