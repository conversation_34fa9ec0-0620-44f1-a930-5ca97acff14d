#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التقارير
Reports Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime, date
from tkcalendar import DateEntry

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from src.reports_manager import reports_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, SHIPMENT_STATUS

class ReportsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("التقارير - نظام متابعة الشحنات")
        self.window.geometry("800x600")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="التقارير",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 30))
        
        # إنشاء دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # تبويب تقرير الشحنات
        self.create_shipments_report_tab()
        
        # تبويب تقرير الموردين
        self.create_suppliers_report_tab()
        
        # تبويب تقرير المخزون
        self.create_inventory_report_tab()
        
        # تبويب الإحصائيات
        self.create_statistics_tab()
    
    def create_shipments_report_tab(self):
        """إنشاء تبويب تقرير الشحنات"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="تقرير الشحنات")
        
        # إطار الخيارات
        options_frame = tk.LabelFrame(frame, text="خيارات التقرير", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white'])
        options_frame.pack(fill='x', padx=20, pady=20)
        
        # فترة التاريخ
        date_frame = tk.Frame(options_frame, bg=COLORS['white'])
        date_frame.pack(fill='x', pady=10)
        
        tk.Label(date_frame, text="من تاريخ:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        self.shipments_start_date = DateEntry(date_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.shipments_start_date.pack(side='right', padx=5)
        
        tk.Label(date_frame, text="إلى تاريخ:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        self.shipments_end_date = DateEntry(date_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.shipments_end_date.pack(side='right', padx=5)
        
        # المورد
        supplier_frame = tk.Frame(options_frame, bg=COLORS['white'])
        supplier_frame.pack(fill='x', pady=10)
        
        tk.Label(supplier_frame, text="المورد:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        self.shipments_supplier = ttk.Combobox(supplier_frame, width=30, state='readonly')
        self.shipments_supplier.pack(side='right', padx=5)
        
        # الحالة
        status_frame = tk.Frame(options_frame, bg=COLORS['white'])
        status_frame.pack(fill='x', pady=10)
        
        tk.Label(status_frame, text="الحالة:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        status_values = ['جميع الحالات'] + list(SHIPMENT_STATUS.values())
        self.shipments_status = ttk.Combobox(status_frame, values=status_values, width=20, state='readonly')
        self.shipments_status.set('جميع الحالات')
        self.shipments_status.pack(side='right', padx=5)
        
        # تنسيق التصدير
        format_frame = tk.Frame(options_frame, bg=COLORS['white'])
        format_frame.pack(fill='x', pady=10)
        
        tk.Label(format_frame, text="تنسيق التصدير:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        self.shipments_format = ttk.Combobox(format_frame, values=['Excel', 'PDF'], width=15, state='readonly')
        self.shipments_format.set('Excel')
        self.shipments_format.pack(side='right', padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(frame, bg=COLORS['white'])
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        generate_btn = tk.Button(
            buttons_frame,
            text="إنشاء التقرير",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.generate_shipments_report,
            width=20
        )
        generate_btn.pack(side='right', padx=5)
        
        # تحميل الموردين
        self.load_suppliers_for_reports()
    
    def create_suppliers_report_tab(self):
        """إنشاء تبويب تقرير الموردين"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="تقرير الموردين")
        
        # إطار الخيارات
        options_frame = tk.LabelFrame(frame, text="خيارات التقرير", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white'])
        options_frame.pack(fill='x', padx=20, pady=20)
        
        # الموردين النشطين فقط
        self.suppliers_active_only = tk.BooleanVar(value=True)
        active_check = tk.Checkbutton(
            options_frame,
            text="الموردين النشطين فقط",
            variable=self.suppliers_active_only,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white']
        )
        active_check.pack(anchor='e', pady=10)
        
        # تنسيق التصدير
        format_frame = tk.Frame(options_frame, bg=COLORS['white'])
        format_frame.pack(fill='x', pady=10)
        
        tk.Label(format_frame, text="تنسيق التصدير:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        self.suppliers_format = ttk.Combobox(format_frame, values=['Excel', 'PDF'], width=15, state='readonly')
        self.suppliers_format.set('Excel')
        self.suppliers_format.pack(side='right', padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(frame, bg=COLORS['white'])
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        generate_btn = tk.Button(
            buttons_frame,
            text="إنشاء التقرير",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.generate_suppliers_report,
            width=20
        )
        generate_btn.pack(side='right', padx=5)
    
    def create_inventory_report_tab(self):
        """إنشاء تبويب تقرير المخزون"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="تقرير المخزون")
        
        # إطار الخيارات
        options_frame = tk.LabelFrame(frame, text="خيارات التقرير", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white'])
        options_frame.pack(fill='x', padx=20, pady=20)
        
        # الأصناف منخفضة المخزون فقط
        self.inventory_low_stock_only = tk.BooleanVar(value=False)
        low_stock_check = tk.Checkbutton(
            options_frame,
            text="الأصناف منخفضة المخزون فقط",
            variable=self.inventory_low_stock_only,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white']
        )
        low_stock_check.pack(anchor='e', pady=10)
        
        # تنسيق التصدير
        format_frame = tk.Frame(options_frame, bg=COLORS['white'])
        format_frame.pack(fill='x', pady=10)
        
        tk.Label(format_frame, text="تنسيق التصدير:", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white']).pack(side='right', padx=5)
        self.inventory_format = ttk.Combobox(format_frame, values=['Excel', 'PDF'], width=15, state='readonly')
        self.inventory_format.set('Excel')
        self.inventory_format.pack(side='right', padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(frame, bg=COLORS['white'])
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        generate_btn = tk.Button(
            buttons_frame,
            text="إنشاء التقرير",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.generate_inventory_report,
            width=20
        )
        generate_btn.pack(side='right', padx=5)
    
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="الإحصائيات")
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(frame, text="إحصائيات الشحنات", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white'])
        stats_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # منطقة عرض الإحصائيات
        self.stats_text = tk.Text(
            stats_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['light'],
            relief='solid',
            bd=1,
            wrap='word'
        )
        self.stats_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # زر تحديث الإحصائيات
        refresh_btn = tk.Button(
            stats_frame,
            text="تحديث الإحصائيات",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.refresh_statistics
        )
        refresh_btn.pack(pady=10)
        
        # تحميل الإحصائيات عند فتح التبويب
        self.refresh_statistics()
    
    def load_suppliers_for_reports(self):
        """تحميل الموردين لتقارير الشحنات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name')
            suppliers = cursor.fetchall()
            
            supplier_list = ['جميع الموردين'] + [f"{sup['supplier_name']}" for sup in suppliers]
            self.shipments_supplier['values'] = supplier_list
            self.shipments_supplier.set('جميع الموردين')
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الموردين: {str(e)}")
    
    def generate_shipments_report(self):
        """إنشاء تقرير الشحنات"""
        if not auth_manager.has_permission('view_reports'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لعرض التقارير")
            return
        
        try:
            # جمع المعايير
            start_date = self.shipments_start_date.get_date() if self.shipments_start_date.get() else None
            end_date = self.shipments_end_date.get_date() if self.shipments_end_date.get() else None
            
            supplier_name = self.shipments_supplier.get()
            supplier_id = None
            if supplier_name != 'جميع الموردين':
                # الحصول على ID المورد
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                cursor.execute('SELECT id FROM suppliers WHERE supplier_name = ?', (supplier_name,))
                result = cursor.fetchone()
                if result:
                    supplier_id = result['id']
                conn.close()
            
            status_ar = self.shipments_status.get()
            status = None
            if status_ar != 'جميع الحالات':
                # البحث عن المفتاح الإنجليزي للحالة
                for key, value in SHIPMENT_STATUS.items():
                    if value == status_ar:
                        status = key
                        break
            
            format_type = self.shipments_format.get().lower()
            
            # إنشاء التقرير
            result = reports_manager.generate_shipments_report(
                start_date=start_date,
                end_date=end_date,
                supplier_id=supplier_id,
                status=status,
                format=format_type
            )
            
            if result:
                messagebox.showinfo("نجح", "تم إنشاء تقرير الشحنات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")
    
    def generate_suppliers_report(self):
        """إنشاء تقرير الموردين"""
        if not auth_manager.has_permission('view_reports'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لعرض التقارير")
            return
        
        try:
            active_only = self.suppliers_active_only.get()
            format_type = self.suppliers_format.get().lower()
            
            result = reports_manager.generate_suppliers_report(
                active_only=active_only,
                format=format_type
            )
            
            if result:
                messagebox.showinfo("نجح", "تم إنشاء تقرير الموردين بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")
    
    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        if not auth_manager.has_permission('view_reports'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لعرض التقارير")
            return
        
        try:
            low_stock_only = self.inventory_low_stock_only.get()
            format_type = self.inventory_format.get().lower()
            
            result = reports_manager.generate_inventory_report(
                low_stock_only=low_stock_only,
                format=format_type
            )
            
            if result:
                messagebox.showinfo("نجح", "تم إنشاء تقرير المخزون بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")
    
    def refresh_statistics(self):
        """تحديث الإحصائيات"""
        try:
            stats = reports_manager.get_shipment_statistics()
            
            # مسح النص السابق
            self.stats_text.delete('1.0', tk.END)
            
            # إضافة الإحصائيات
            stats_text = f"""
إحصائيات الشحنات
================

إجمالي الشحنات: {stats.get('total_shipments', 0)}
الشحنات النشطة: {stats.get('active_shipments', 0)}
الشحنات المتأخرة: {stats.get('delayed_shipments', 0)}

الإحصائيات حسب الحالة:
"""
            
            for status, count in stats.get('by_status', {}).items():
                status_ar = SHIPMENT_STATUS.get(status, status)
                stats_text += f"- {status_ar}: {count}\n"
            
            stats_text += f"\n\nتاريخ آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            self.stats_text.insert('1.0', stats_text)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث الإحصائيات: {str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة التقارير للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    reports_window = ReportsWindow()
    reports_window.window.mainloop()
