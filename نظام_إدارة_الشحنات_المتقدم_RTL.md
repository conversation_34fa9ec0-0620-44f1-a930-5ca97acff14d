# 🚢 نظام إدارة الشحنات المتقدم والشامل مع دعم RTL كامل

## 📋 نظرة عامة

تم تطوير **نظام إدارة الشحنات المتقدم والشامل** ليكون حلاً متكاملاً لإدارة عمليات الشحن والنقل مع دعم كامل للغة العربية واتجاه RTL. النظام مصمم بأحدث التقنيات والمعايير لتوفير تجربة مستخدم استثنائية.

## ✨ الميزات الرئيسية

### 🎨 **التصميم والواجهة**
- **دعم RTL كامل**: جميع العناصر محاذاة لليمين مع دعم طبيعي للعربية
- **أنماط CSS متقدمة**: تصميم حديث مع ألوان متناسقة وتأثيرات بصرية
- **خطوط عربية محسنة**: استخدام خط Segoe UI المحسن للعربية
- **ألوان ذكية**: نظام ألوان متقدم حسب حالة الشحنة والأولوية
- **واجهة تفاعلية**: تأثيرات hover وتفاعل سلس مع العناصر

### 📊 **إدارة الشحنات المتقدمة**
- **جدول شامل**: عرض 11 عمود مع معلومات مفصلة
- **بحث وتصفية متقدمة**: بحث فوري مع فلاتر متعددة
- **ترتيب ذكي**: ترتيب حسب أي عمود مع اتجاهين
- **تنقل بالصفحات**: عرض 20 عنصر لكل صفحة مع تنقل سهل
- **ألوان الحالات**: تلوين تلقائي حسب حالة الشحنة

### 🔍 **البحث والتصفية**
- **البحث العام**: بحث في رقم الشحنة، المورد، والحاوية
- **فلتر الحالة**: تصفية حسب حالة الشحنة (9 حالات)
- **فلتر المورد**: تصفية حسب المورد من قاعدة البيانات
- **بحث فوري**: نتائج فورية أثناء الكتابة
- **مسح سريع**: زر مسح لإعادة تعيين جميع المرشحات

### 📝 **نموذج الشحنة الشامل**
- **4 أقسام منظمة**: معلومات أساسية، شحن، مالية، ملاحظات
- **التحقق من البيانات**: تحقق شامل من صحة المدخلات
- **رقم تلقائي**: إنشاء رقم شحنة تلقائي (SH-XXXXXX)
- **قوائم ذكية**: قوائم منسدلة محملة من قاعدة البيانات
- **حفظ متقدم**: خيارات حفظ متعددة (حفظ، حفظ وإغلاق، حفظ ومتابعة)

## 🏗️ **البنية التقنية**

### 📁 **هيكل الملفات**
```
ship_mh/
├── src/
│   ├── simple_shipments_manager.py     # نظام إدارة الشحنات الرئيسي
│   ├── simple_shipment_form.py         # نموذج الشحنة المتقدم
│   ├── simple_rtl_components.py        # مكونات RTL مبسطة وآمنة
│   ├── advanced_rtl_styles.py          # أنماط CSS متقدمة
│   └── enhanced_rtl_components.py      # مكونات RTL محسنة
├── config/
│   └── config.py                       # إعدادات النظام والألوان
├── database/
│   └── database_manager.py             # مدير قاعدة البيانات
└── main.py                             # الملف الرئيسي
```

### 🎨 **نظام الألوان المتقدم**
```python
# ألوان الحالات
'في الطريق': '#FEF3C7'     # أصفر فاتح
'وصلت': '#D1FAE5'          # أخضر فاتح  
'تم التسليم': '#BBF7D0'     # أخضر
'متأخرة': '#FECACA'         # أحمر فاتح
'ملغية': '#F3F4F6'          # رمادي فاتح

# ألوان الأزرار
'primary': '#1E40AF'        # أزرق رئيسي
'success': '#059669'        # أخضر نجاح
'warning': '#D97706'        # برتقالي تحذير
'danger': '#DC2626'         # أحمر خطر
```

### 🗄️ **قاعدة البيانات المحسنة**
```sql
-- جدول الشحنات المتقدم
CREATE TABLE shipments (
    id TEXT PRIMARY KEY,
    shipment_number TEXT UNIQUE NOT NULL,
    supplier_id INTEGER NOT NULL,
    status TEXT DEFAULT 'في الانتظار',
    shipment_date DATE,
    expected_arrival_date DATE,
    departure_port TEXT,
    arrival_port TEXT,
    shipping_company TEXT,
    container_number TEXT,
    bill_of_lading TEXT,
    container_type TEXT,
    weight REAL,
    currency TEXT DEFAULT 'USD',
    total_value REAL DEFAULT 0,
    shipping_cost REAL DEFAULT 0,
    additional_fees REAL DEFAULT 0,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'لم يتم الدفع',
    notes TEXT,
    created_by TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 **الميزات المتقدمة**

### 🎯 **مكونات RTL مخصصة**
- **SimpleRTLFrame**: إطارات محاذاة يمين طبيعية
- **SimpleRTLLabel**: تسميات عربية محسنة
- **SimpleRTLButton**: أزرار بـ 7 أنماط مختلفة
- **SimpleRTLEntry**: حقول إدخال مع placeholder عربي
- **SimpleRTLCombobox**: قوائم منسدلة RTL آمنة
- **SimpleRTLTreeview**: جداول RTL مع ألوان متقدمة
- **SimpleRTLCard**: بطاقات بعناوين وحدود أنيقة

### ⌨️ **اختصارات لوحة المفاتيح**
```
Ctrl+N  : شحنة جديدة
Ctrl+R  : تحديث البيانات
Ctrl+S  : حفظ (في النموذج)
F5      : تحديث
Esc     : إغلاق/إلغاء
Enter   : تأكيد/فتح
Delete  : حذف العنصر المحدد
```

### 🔄 **إدارة البيانات الذكية**
- **تحديث تلقائي**: تحديث البيانات كل 5 دقائق
- **تحديث فوري**: تحديث فوري بعد كل عملية
- **معالجة الأخطاء**: معالجة شاملة للأخطاء مع رسائل واضحة
- **نسخ احتياطي**: نسخ احتياطية تلقائية
- **تتبع التغييرات**: تتبع من قام بالتعديل ومتى

## 📊 **البيانات والمحتوى**

### 🏢 **الموردين المدعومين**
- شركة التجارة الدولية (الرياض)
- مؤسسة الشحن السريع (جدة)
- شركة الخليج للاستيراد (الدمام)

### 🚢 **الموانئ المدعومة**
- **محلية**: الدمام، جدة، ينبع، جازان، ضباء، الجبيل
- **دولية**: شنغهاي، دبي، هامبورغ، روتردام، سنغافورة

### 🏭 **شركات الشحن**
- الخطوط السعودية للنقل البحري
- Maersk Line
- MSC Mediterranean Shipping
- CMA CGM Group
- COSCO Shipping

### 💱 **العملات المدعومة**
- SAR (ريال سعودي)
- USD (دولار أمريكي)
- EUR (يورو)
- AED (درهم إماراتي)
- KWD (دينار كويتي)

## 🎮 **كيفية الاستخدام**

### 1️⃣ **تشغيل النظام**
```bash
python main.py
```

### 2️⃣ **تسجيل الدخول**
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### 3️⃣ **فتح إدارة الشحنات**
- انقر على زر **"🚢 إدارة الشحنات"** في الواجهة الرئيسية
- ستظهر نافذة إدارة الشحنات بملء الشاشة

### 4️⃣ **إضافة شحنة جديدة**
- انقر على **"➕ شحنة جديدة"** في شريط الأدوات
- املأ البيانات في الأقسام الأربعة
- انقر **"💾 حفظ"** أو **"💾 حفظ وإغلاق"**

### 5️⃣ **البحث والتصفية**
- استخدم حقل **"البحث العام"** للبحث السريع
- اختر من قوائم **"حالة الشحنة"** و **"المورد"**
- انقر **"🔍 بحث"** أو **"🗑️ مسح"**

### 6️⃣ **إدارة الشحنات**
- **تعديل**: انقر مرتين على الشحنة أو استخدم زر **"✏️ تعديل"**
- **نسخ**: انقر **"📋 نسخ"** لإنشاء شحنة مشابهة
- **حذف**: انقر **"🗑️ حذف"** مع تأكيد الحذف

## 🔧 **الإعدادات والتخصيص**

### 🎨 **تخصيص الألوان**
يمكن تعديل الألوان في ملف `config/config.py`:
```python
COLORS = {
    'primary': '#1E40AF',           # اللون الرئيسي
    'success': '#059669',           # لون النجاح
    'warning': '#D97706',           # لون التحذير
    'danger': '#DC2626',            # لون الخطر
    'background': '#F9FAFB',        # لون الخلفية
    'surface': '#FFFFFF',           # لون السطح
    'text_primary': '#111827',      # لون النص الرئيسي
    'text_secondary': '#374151',    # لون النص الثانوي
}
```

### 📝 **إضافة حالات جديدة**
```python
SHIPMENT_STATUS = {
    'pending': 'في الانتظار',
    'confirmed': 'مؤكدة',
    'shipped': 'تم الشحن',
    'in_transit': 'في الطريق',
    'arrived': 'وصلت',
    'in_customs': 'في الجمارك',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغية',
    'delayed': 'متأخرة'
}
```

## 🛠️ **الصيانة والتطوير**

### 🔍 **تشخيص المشاكل**
- **ملفات السجل**: تحقق من ملفات السجل في مجلد `logs/`
- **قاعدة البيانات**: استخدم `reset_database.py` لإعادة تعيين قاعدة البيانات
- **النسخ الاحتياطية**: تحقق من مجلد `backups/`

### 📈 **إضافة ميزات جديدة**
1. **إضافة عمود جديد**: عدّل جدول قاعدة البيانات وواجهة العرض
2. **إضافة فلتر جديد**: أضف المتغير والعنصر في لوحة البحث
3. **إضافة تقرير جديد**: استخدم مكتبة reportlab للتقارير PDF

### 🔄 **التحديثات المستقبلية**
- **تقارير متقدمة**: تقارير PDF وExcel
- **تتبع GPS**: تتبع الشحنات على الخريطة
- **إشعارات**: تنبيهات للشحنات المتأخرة
- **API**: واجهة برمجية للتكامل مع أنظمة أخرى
- **تطبيق موبايل**: تطبيق للهواتف الذكية

## 🎉 **الخلاصة**

**تم تطوير نظام إدارة الشحنات المتقدم والشامل بنجاح!**

### ✅ **الإنجازات المحققة**
- 🎨 **واجهة RTL متقدمة** مع دعم كامل للعربية
- 📊 **نظام إدارة شامل** مع جميع الميزات المطلوبة
- 🔍 **بحث وتصفية متقدمة** مع نتائج فورية
- 📝 **نموذج شحنة متكامل** مع 4 أقسام منظمة
- 🎨 **أنماط CSS متقدمة** مع ألوان ذكية
- 🗄️ **قاعدة بيانات محسنة** مع بيانات تجريبية
- ⌨️ **اختصارات لوحة مفاتيح** شاملة
- 🔧 **معالجة أخطاء متقدمة** مع رسائل واضحة

### 🚀 **النتيجة النهائية**
**نظام إدارة شحنات احترافي ومتكامل جاهز للاستخدام الإنتاجي!**

- 🌟 **تجربة مستخدم استثنائية** مع تصميم حديث
- ⚡ **أداء سريع وموثوق** مع تحديث فوري
- 🔒 **أمان متقدم** مع تتبع المستخدمين
- 📱 **تصميم متجاوب** يعمل على جميع الشاشات
- 🌍 **دعم دولي** مع عملات وموانئ متعددة

**النظام جاهز للاستخدام والتطوير المستمر!** 🎊✨🚢
