#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار البحث في أكواد الأصناف مع دعم RTL كامل
Item Code Search Dialog with Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS
from src.simple_rtl_components import *
from database.database_manager import DatabaseManager

class ItemCodeSearchDialog:
    """نافذة حوار البحث في أكواد الأصناف"""
    
    def __init__(self, parent, item_dialog):
        self.parent = parent
        self.item_dialog = item_dialog
        self.db_manager = DatabaseManager()
        self.items_data = []
        self.filtered_items = []
        self.selected_code = None
        self.selected_item_data = None
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات البحث
        self.search_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_items_from_database()
        
        # توسيط النافذة
        self.center_window()
        
        # تركيز على حقل البحث
        self.focus_search_field()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🔍 البحث في أكواد الأصناف - F9")
        self.root.geometry("700x500")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        
        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.close_dialog)
        
        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Return>', lambda e: self.select_item())
        self.root.bind('<Escape>', lambda e: self.close_dialog())
        self.root.bind('<F9>', lambda e: self.close_dialog())
        self.root.bind('<Double-Button-1>', lambda e: self.select_item())
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = create_simple_rtl_label(
            main_frame,
            text="🔍 البحث في أكواد الأصناف المحفوظة",
            font=('Segoe UI', 18, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e', pady=(0, 20))
        
        # وصف
        desc_label = create_simple_rtl_label(
            main_frame,
            text="ابحث في الأصناف المحفوظة مسبقاً واختر كود الصنف المطلوب",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        desc_label.pack(anchor='e', pady=(0, 20))
        
        # منطقة البحث
        search_frame = create_simple_rtl_frame(main_frame)
        search_frame.pack(fill='x', pady=(0, 20))
        
        # حقل البحث
        search_label = create_simple_rtl_label(
            search_frame,
            text="🔍 البحث في الأكواد والأسماء:",
            font=('Segoe UI', 14, 'bold')
        )
        search_label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars['general'] = tk.StringVar()
        self.general_search_entry = create_simple_rtl_entry(
            search_frame,
            textvariable=self.search_vars['general'],
            placeholder="ابحث في كود الصنف أو اسم الصنف..."
        )
        self.general_search_entry.configure(font=('Segoe UI', 12, 'normal'))
        self.general_search_entry.pack(fill='x', ipady=10)
        
        # ربط البحث الفوري
        self.search_vars['general'].trace('w', self.on_search_change)
        
        # أزرار البحث
        buttons_row = create_simple_rtl_frame(search_frame)
        buttons_row.pack(fill='x', pady=(15, 0))
        
        # زر مسح
        clear_btn = create_simple_rtl_button(
            buttons_row,
            text="🗑️ مسح البحث",
            button_type="secondary",
            command=self.clear_search
        )
        clear_btn.pack(side='right', padx=5)
        
        # عداد النتائج
        self.results_label = create_simple_rtl_label(
            buttons_row,
            text="",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        self.results_label.pack(side='left', padx=10)
        
        # جدول النتائج
        results_frame = create_simple_rtl_frame(main_frame)
        results_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # عنوان النتائج
        results_title = create_simple_rtl_label(
            results_frame,
            text="📊 الأصناف المتاحة:",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        results_title.pack(anchor='e', pady=(0, 10))
        
        # إنشاء جدول النتائج
        table_frame = create_simple_rtl_frame(results_frame)
        table_frame.pack(fill='both', expand=True)
        
        # تعريف أعمدة الجدول
        columns = ['item_code', 'item_name', 'unit', 'unit_price', 'origin_country']
        column_names = ['كود الصنف', 'اسم الصنف', 'الوحدة', 'سعر الوحدة', 'بلد المنشأ']
        
        # إنشاء Treeview
        self.results_tree = create_simple_rtl_treeview(
            table_frame,
            columns=columns,
            show='tree headings',
            height=12
        )
        
        # تكوين الأعمدة
        widths = [120, 200, 80, 100, 120]
        for i, (col_id, col_name, width) in enumerate(zip(columns, column_names, widths)):
            self.results_tree.heading(col_id, text=col_name, anchor='e')
            self.results_tree.column(col_id, width=width, anchor='e', minwidth=50)
        
        # العمود الرئيسي
        self.results_tree.column('#0', width=30, minwidth=30, anchor='e')
        self.results_tree.heading('#0', text='#', anchor='e')
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.on_result_double_click)
        
        # إعداد ألوان الجدول
        self.setup_tree_colors()
        
        # أزرار التحكم
        control_frame = create_simple_rtl_frame(main_frame)
        control_frame.pack(fill='x', pady=(20, 0))
        
        # زر الاختيار
        select_btn = create_simple_rtl_button(
            control_frame,
            text="✅ اختيار الكود",
            button_type="success",
            command=self.select_item
        )
        select_btn.pack(side='right', padx=5)
        
        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            control_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_dialog
        )
        close_btn.pack(side='right', padx=5)
        
        # معلومات المساعدة
        help_label = create_simple_rtl_label(
            control_frame,
            text="💡 نصيحة: انقر مرتين على الصنف لاختياره مباشرة",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        help_label.pack(side='left', padx=10)
    
    def setup_tree_colors(self):
        """إعداد ألوان الجدول"""
        try:
            self.results_tree.tag_configure('odd_row', background='#F9FAFB')
            self.results_tree.tag_configure('even_row', background='#FFFFFF')
            self.results_tree.tag_configure('selected', background='#DBEAFE')
        except Exception as e:
            print(f"خطأ في إعداد ألوان الجدول: {e}")
    
    def load_items_from_database(self):
        """تحميل الأصناف من قاعدة البيانات"""
        try:
            # استعلام للحصول على أكواد الأصناف الفريدة
            query = """
                SELECT DISTINCT item_code, item_name, unit, unit_price, origin_country, description
                FROM shipment_items 
                WHERE item_code IS NOT NULL AND item_code != ''
                ORDER BY item_code
            """
            
            items = self.db_manager.fetch_all(query)
            self.items_data = []
            
            for item in items:
                item_data = {
                    'item_code': item['item_code'],
                    'item_name': item['item_name'] or '',
                    'description': item['description'] or '',
                    'unit': item['unit'] or '',
                    'unit_price': str(item['unit_price'] or 0),
                    'origin_country': item['origin_country'] or ''
                }
                self.items_data.append(item_data)
            
            # تحديث القائمة المفلترة
            self.filtered_items = self.items_data.copy()
            self.update_results_tree()
            self.update_results_count()
            
        except Exception as e:
            print(f"خطأ في تحميل الأصناف من قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)
        self._search_timer = self.root.after(300, self.perform_search)
    
    def perform_search(self):
        """تنفيذ البحث"""
        try:
            search_text = self.search_vars['general'].get().strip().lower()
            
            if not search_text:
                self.filtered_items = self.items_data.copy()
            else:
                self.filtered_items = []
                for item in self.items_data:
                    # البحث في كود الصنف واسم الصنف
                    item_text = f"{item.get('item_code', '')} {item.get('item_name', '')}".lower()
                    if search_text in item_text:
                        self.filtered_items.append(item)
            
            self.update_results_tree()
            self.update_results_count()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
    
    def clear_search(self):
        """مسح البحث"""
        self.search_vars['general'].set('')
        self.perform_search()
        self.focus_search_field()
    
    def update_results_tree(self):
        """تحديث جدول النتائج"""
        # مسح البيانات الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # إضافة النتائج
        for i, item_data in enumerate(self.filtered_items):
            self.add_result_to_tree(item_data, i + 1)
    
    def add_result_to_tree(self, item_data, index):
        """إضافة نتيجة للجدول"""
        try:
            values = [
                item_data.get('item_code', ''),
                item_data.get('item_name', ''),
                item_data.get('unit', ''),
                f"{float(item_data.get('unit_price', 0)):,.2f}",
                item_data.get('origin_country', '')
            ]
            
            # تحديد لون الصف
            tags = ['odd_row' if index % 2 == 1 else 'even_row']
            
            # إدراج العنصر
            item = self.results_tree.insert(
                '', 'end',
                text=str(index),
                values=values,
                tags=tags
            )
            
        except Exception as e:
            print(f"خطأ في إضافة نتيجة للجدول: {e}")
    
    def update_results_count(self):
        """تحديث عداد النتائج"""
        total_items = len(self.items_data)
        filtered_items = len(self.filtered_items)
        
        if filtered_items == total_items:
            self.results_label.configure(text=f"📊 إجمالي الأكواد: {total_items}")
        else:
            self.results_label.configure(text=f"📊 النتائج: {filtered_items} من {total_items}")
    
    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            values = self.results_tree.item(item, 'values')
            
            if values:
                # البحث عن بيانات الصنف الكاملة
                item_code = values[0]
                for item_data in self.filtered_items:
                    if item_data.get('item_code') == item_code:
                        self.selected_code = item_code
                        self.selected_item_data = item_data
                        break
    
    def on_result_double_click(self, event):
        """عند النقر المزدوج على نتيجة"""
        self.select_item()
    
    def select_item(self):
        """اختيار الكود"""
        if not self.selected_code:
            messagebox.showwarning("تحذير", "يرجى اختيار كود صنف من النتائج")
            return
        
        self.close_dialog()
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.root.destroy()
    
    def focus_search_field(self):
        """تركيز على حقل البحث"""
        self.general_search_entry.focus_set()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    
    dialog = ItemCodeSearchDialog(root, None)
    root.mainloop()
