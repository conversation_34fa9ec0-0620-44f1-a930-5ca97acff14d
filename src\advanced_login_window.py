#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول المتقدمة مع دعم RTL
Advanced Login Window with RTL Support
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys
from datetime import datetime
import time

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from config.config import APP_CONFIG, UI_CONFIG, COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG

class AdvancedLoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.login_attempts = 0
        self.max_attempts = 3
        self.is_locked = False
        self.lock_time = 0
        self.setup_window()
        self.create_simple_interface()
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة المتقدمة"""
        self.root.title(f"🔐 تسجيل الدخول - {APP_CONFIG['name']}")
        self.root.geometry("500x700")
        self.root.resizable(False, False)
        self.root.configure(bg=COLORS['background'])
        
        # إزالة شريط العنوان للحصول على تصميم حديث
        self.root.overrideredirect(True)
        
        # تعيين النافذة في المقدمة
        self.root.attributes('-topmost', True)
        self.root.after(100, lambda: self.root.attributes('-topmost', False))
        
        # ربط أحداث لوحة المفاتيح
        self.root.bind('<Return>', self.on_enter_pressed)
        self.root.bind('<Escape>', self.on_escape_pressed)
        
    def create_advanced_interface(self):
        """إنشاء واجهة متقدمة مع تصميم RTL"""
        # الإطار الرئيسي مع تدرج لوني
        self.main_frame = create_rtl_frame(
            self.root,
            bg=COLORS['surface'],
            relief='flat',
            bd=0
        )
        self.main_frame.pack(fill='both', expand=True)
        
        # شريط العنوان المخصص
        self.create_custom_title_bar()
        
        # منطقة المحتوى الرئيسية
        content_frame = create_rtl_frame(
            self.main_frame,
            bg=COLORS['surface']
        )
        content_frame.pack(fill='both', expand=True, padx=30, pady=20)
        
        # قسم الشعار والعنوان
        self.create_header_section(content_frame)
        
        # قسم نموذج تسجيل الدخول
        self.create_login_form(content_frame)
        
        # قسم الخيارات الإضافية
        self.create_options_section(content_frame)
        
        # قسم المعلومات والحالة
        self.create_status_section(content_frame)
        
        # تذييل النافذة
        self.create_footer_section(content_frame)
        
    def create_custom_title_bar(self):
        """إنشاء شريط عنوان مخصص"""
        title_bar = create_rtl_frame(
            self.main_frame,
            bg=COLORS['primary'],
            height=40
        )
        title_bar.pack(fill='x')
        title_bar.pack_propagate(False)
        
        # عنوان النافذة
        title_label = create_rtl_label(
            title_bar,
            text=f"🔐 {APP_CONFIG['name']} - تسجيل الدخول",
            font=self.style_manager.get_font('arabic_header', size=12, weight='bold'),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        title_label.pack(side='right', padx=15, pady=8)
        
        # أزرار التحكم
        controls_frame = create_rtl_frame(title_bar, bg=COLORS['primary'])
        controls_frame.pack(side='left', padx=10, pady=5)
        
        # زر الإغلاق
        close_btn = create_rtl_button(
            controls_frame,
            text="✕",
            style='danger',
            command=self.close_window,
            font=self.style_manager.get_font('arabic', size=10, weight='bold'),
            width=3,
            height=1
        )
        close_btn.pack(side='left', padx=2)
        
        # زر التصغير
        minimize_btn = create_rtl_button(
            controls_frame,
            text="─",
            style='outline',
            command=self.minimize_window,
            font=self.style_manager.get_font('arabic', size=10, weight='bold'),
            width=3,
            height=1
        )
        minimize_btn.pack(side='left', padx=2)
        
        # جعل شريط العنوان قابل للسحب
        title_bar.bind('<Button-1>', self.start_drag)
        title_bar.bind('<B1-Motion>', self.on_drag)
        title_label.bind('<Button-1>', self.start_drag)
        title_label.bind('<B1-Motion>', self.on_drag)
        
    def create_header_section(self, parent):
        """إنشاء قسم الرأس مع الشعار"""
        header_frame = create_rtl_frame(parent, bg=COLORS['surface'])
        header_frame.pack(fill='x', pady=(20, 30))
        
        # إطار الشعار مع تأثير دائري
        logo_frame = create_rtl_frame(
            header_frame,
            bg=COLORS['primary'],
            width=120,
            height=120
        )
        logo_frame.pack(anchor='center')
        logo_frame.pack_propagate(False)
        
        # أيقونة الشعار
        logo_label = create_rtl_label(
            logo_frame,
            text="🚢",
            font=self.style_manager.get_font('arabic_title', size=48),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        logo_label.place(relx=0.5, rely=0.5, anchor='center')
        
        # عنوان التطبيق
        app_title = create_rtl_label(
            header_frame,
            text=APP_CONFIG['name'],
            style='title',
            font=self.style_manager.get_font('arabic_title', size=24, weight='bold'),
            fg=COLORS['primary']
        )
        app_title.pack(pady=(15, 5))
        
        # وصف التطبيق
        app_desc = create_rtl_label(
            header_frame,
            text="نظام متكامل لإدارة ومتابعة الشحنات",
            style='subtitle',
            font=self.style_manager.get_font('arabic', size=12),
            fg=COLORS['text_secondary']
        )
        app_desc.pack(pady=(0, 10))
        
        # خط فاصل زخرفي
        separator = tk.Frame(
            header_frame,
            height=3,
            bg=COLORS['rtl_accent'],
            relief='flat'
        )
        separator.pack(fill='x', padx=100, pady=10)
        
    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول المتقدم"""
        form_frame = create_rtl_card(parent, title="🔑 تسجيل الدخول")
        form_frame.pack(fill='x', pady=20)
        
        # محتوى النموذج
        form_content = create_rtl_frame(form_frame, bg=COLORS['surface'])
        form_content.pack(fill='x', padx=25, pady=20)
        
        # حقل اسم المستخدم
        username_frame = create_rtl_frame(form_content, bg=COLORS['surface'])
        username_frame.pack(fill='x', pady=(0, 15))
        
        username_label = create_rtl_label(
            username_frame,
            text="👤 اسم المستخدم:",
            font=self.style_manager.get_font('arabic', size=12, weight='bold'),
            fg=COLORS['text_primary']
        )
        username_label.pack(anchor='e', pady=(0, 5))
        
        self.username_entry = create_rtl_entry(
            username_frame,
            font=self.style_manager.get_font('arabic', size=12),
            relief='solid',
            bd=2,
            highlightthickness=2,
            highlightcolor=COLORS['primary']
        )
        self.username_entry.pack(fill='x', ipady=8)
        self.username_entry.insert(0, "admin")  # قيمة افتراضية
        
        # حقل كلمة المرور
        password_frame = create_rtl_frame(form_content, bg=COLORS['surface'])
        password_frame.pack(fill='x', pady=(0, 15))
        
        password_label = create_rtl_label(
            password_frame,
            text="🔒 كلمة المرور:",
            font=self.style_manager.get_font('arabic', size=12, weight='bold'),
            fg=COLORS['text_primary']
        )
        password_label.pack(anchor='e', pady=(0, 5))
        
        password_input_frame = create_rtl_frame(password_frame, bg=COLORS['surface'])
        password_input_frame.pack(fill='x')
        
        self.password_entry = create_rtl_entry(
            password_input_frame,
            show="*",
            font=self.style_manager.get_font('arabic', size=12),
            relief='solid',
            bd=2,
            highlightthickness=2,
            highlightcolor=COLORS['primary']
        )
        self.password_entry.pack(side='right', fill='x', expand=True, ipady=8)
        
        # زر إظهار/إخفاء كلمة المرور
        self.show_password_btn = create_rtl_button(
            password_input_frame,
            text="👁️",
            style='ghost',
            command=self.toggle_password_visibility,
            width=3
        )
        self.show_password_btn.pack(side='left', padx=(5, 0))
        
        # خيار تذكر بيانات الدخول
        remember_frame = create_rtl_frame(form_content, bg=COLORS['surface'])
        remember_frame.pack(fill='x', pady=(10, 20))
        
        self.remember_var = tk.BooleanVar()
        remember_check = tk.Checkbutton(
            remember_frame,
            text="تذكر بيانات الدخول",
            variable=self.remember_var,
            font=self.style_manager.get_font('arabic', size=11),
            bg=COLORS['surface'],
            fg=COLORS['text_secondary'],
            selectcolor=COLORS['primary'],
            activebackground=COLORS['surface'],
            activeforeground=COLORS['primary'],
            relief='flat',
            bd=0
        )
        remember_check.pack(anchor='e')
        
        # زر تسجيل الدخول
        self.login_btn = create_rtl_button(
            form_content,
            text="🚀 تسجيل الدخول",
            style='primary',
            command=self.attempt_login,
            font=self.style_manager.get_font('arabic_button', size=14, weight='bold'),
            pady=12
        )
        self.login_btn.pack(fill='x', pady=(10, 0))
        
    def create_options_section(self, parent):
        """إنشاء قسم الخيارات الإضافية"""
        options_frame = create_rtl_frame(parent, bg=COLORS['surface'])
        options_frame.pack(fill='x', pady=15)
        
        # خط فاصل
        separator_frame = create_rtl_frame(options_frame, bg=COLORS['surface'])
        separator_frame.pack(fill='x', pady=10)
        
        left_line = tk.Frame(separator_frame, height=1, bg=COLORS['border'])
        left_line.pack(side='left', fill='x', expand=True)
        
        or_label = create_rtl_label(
            separator_frame,
            text="أو",
            font=self.style_manager.get_font('arabic_small'),
            fg=COLORS['text_muted']
        )
        or_label.pack(side='left', padx=10)
        
        right_line = tk.Frame(separator_frame, height=1, bg=COLORS['border'])
        right_line.pack(side='left', fill='x', expand=True)
        
        # أزرار الخيارات
        options_buttons_frame = create_rtl_frame(options_frame, bg=COLORS['surface'])
        options_buttons_frame.pack(fill='x')
        
        # زر نسيت كلمة المرور
        forgot_btn = create_rtl_button(
            options_buttons_frame,
            text="🔄 نسيت كلمة المرور؟",
            style='ghost',
            command=self.forgot_password,
            font=self.style_manager.get_font('arabic', size=11)
        )
        forgot_btn.pack(side='right', padx=5)
        
        # زر مساعدة
        help_btn = create_rtl_button(
            options_buttons_frame,
            text="❓ مساعدة",
            style='ghost',
            command=self.show_help,
            font=self.style_manager.get_font('arabic', size=11)
        )
        help_btn.pack(side='left', padx=5)
        
    def create_status_section(self, parent):
        """إنشاء قسم المعلومات والحالة"""
        status_frame = create_rtl_frame(parent, bg=COLORS['light'], relief='solid', bd=1)
        status_frame.pack(fill='x', pady=15)
        
        # معلومات النظام
        info_frame = create_rtl_frame(status_frame, bg=COLORS['light'])
        info_frame.pack(fill='x', padx=15, pady=10)
        
        # حالة النظام
        self.status_label = create_rtl_label(
            info_frame,
            text="🟢 النظام جاهز للاستخدام",
            font=self.style_manager.get_font('arabic_small', size=10),
            bg=COLORS['light'],
            fg=COLORS['success']
        )
        self.status_label.pack(anchor='e')
        
        # معلومات الإصدار
        version_label = create_rtl_label(
            info_frame,
            text=f"الإصدار: {APP_CONFIG['version']} | تاريخ اليوم: {datetime.now().strftime('%Y-%m-%d')}",
            font=self.style_manager.get_font('arabic_small', size=9),
            bg=COLORS['light'],
            fg=COLORS['text_muted']
        )
        version_label.pack(anchor='e', pady=(5, 0))
        
        # شريط التقدم (للتحميل)
        self.progress_frame = create_rtl_frame(status_frame, bg=COLORS['light'])
        
        self.progress_label = create_rtl_label(
            self.progress_frame,
            text="جاري تسجيل الدخول...",
            font=self.style_manager.get_font('arabic_small'),
            bg=COLORS['light'],
            fg=COLORS['primary']
        )
        
        # شريط تقدم مخصص
        self.progress_bar_frame = create_rtl_frame(
            self.progress_frame,
            bg=COLORS['border'],
            height=4
        )
        
        self.progress_bar = create_rtl_frame(
            self.progress_bar_frame,
            bg=COLORS['primary'],
            height=4
        )
        
    def create_footer_section(self, parent):
        """إنشاء تذييل النافذة"""
        footer_frame = create_rtl_frame(parent, bg=COLORS['surface'])
        footer_frame.pack(fill='x', side='bottom', pady=(20, 10))
        
        # معلومات حقوق الطبع
        copyright_label = create_rtl_label(
            footer_frame,
            text=f"© 2024 {APP_CONFIG['author']} - جميع الحقوق محفوظة",
            font=self.style_manager.get_font('arabic_small', size=9),
            fg=COLORS['text_muted']
        )
        copyright_label.pack(anchor='center')
        
        # رابط الدعم الفني
        support_label = create_rtl_label(
            footer_frame,
            text="للدعم الفني: <EMAIL>",
            font=self.style_manager.get_font('arabic_small', size=9),
            fg=COLORS['info'],
            cursor='hand2'
        )
        support_label.pack(anchor='center', pady=(2, 0))
        support_label.bind('<Button-1>', lambda e: self.open_support())
        
    def start_animations(self):
        """بدء الرسوم المتحركة"""
        # تأثير الظهور التدريجي
        self.root.attributes('-alpha', 0.0)
        self.fade_in()
        
        # تأثير نبضة للشعار
        self.animate_logo()
        
    def fade_in(self, alpha=0.0):
        """تأثير الظهور التدريجي"""
        if alpha < 1.0:
            alpha += 0.05
            self.root.attributes('-alpha', alpha)
            self.root.after(30, lambda: self.fade_in(alpha))
        
    def animate_logo(self):
        """تحريك الشعار"""
        # يمكن إضافة تأثيرات حركية للشعار هنا
        pass

    def start_drag(self, event):
        """بدء سحب النافذة"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y

    def on_drag(self, event):
        """سحب النافذة"""
        x = self.root.winfo_x() + event.x - self.drag_start_x
        y = self.root.winfo_y() + event.y - self.drag_start_y
        self.root.geometry(f"+{x}+{y}")

    def minimize_window(self):
        """تصغير النافذة"""
        self.root.iconify()

    def close_window(self):
        """إغلاق النافذة"""
        self.fade_out()

    def fade_out(self, alpha=1.0):
        """تأثير الاختفاء التدريجي"""
        if alpha > 0.0:
            alpha -= 0.1
            self.root.attributes('-alpha', alpha)
            self.root.after(30, lambda: self.fade_out(alpha))
        else:
            self.root.quit()

    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.password_entry.cget('show') == '*':
            self.password_entry.config(show='')
            self.show_password_btn.config(text='🙈')
        else:
            self.password_entry.config(show='*')
            self.show_password_btn.config(text='👁️')

    def on_enter_pressed(self, event):
        """عند الضغط على Enter"""
        if not self.is_locked:
            self.attempt_login()

    def on_escape_pressed(self, event):
        """عند الضغط على Escape"""
        self.close_window()

    def show_progress(self):
        """إظهار شريط التقدم"""
        self.progress_frame.pack(fill='x', padx=15, pady=10)
        self.progress_label.pack(anchor='e', pady=(0, 5))
        self.progress_bar_frame.pack(fill='x')
        self.progress_bar.pack(side='right', fill='y')

        # تحريك شريط التقدم
        self.animate_progress()

    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_frame.pack_forget()

    def animate_progress(self, width=0):
        """تحريك شريط التقدم"""
        if width <= 100:
            self.progress_bar.config(width=width * 2)
            self.root.after(20, lambda: self.animate_progress(width + 2))

    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        if self.is_locked:
            remaining_time = self.lock_time - time.time()
            if remaining_time > 0:
                self.show_error_message(f"الحساب مقفل. المحاولة مرة أخرى خلال {int(remaining_time)} ثانية")
                return
            else:
                self.is_locked = False
                self.login_attempts = 0

        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        # التحقق من صحة البيانات
        if not username:
            self.show_error_message("يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return

        if not password:
            self.show_error_message("يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return

        # تعطيل زر تسجيل الدخول وإظهار التقدم
        self.login_btn.config(state='disabled', text="🔄 جاري التحقق...")
        self.show_progress()

        # محاكاة تأخير التحقق
        self.root.after(1500, lambda: self.process_login(username, password))

    def process_login(self, username, password):
        """معالجة تسجيل الدخول"""
        try:
            # محاولة تسجيل الدخول
            if auth_manager.login(username, password):
                self.show_success_message("تم تسجيل الدخول بنجاح!")
                self.root.after(1000, self.login_success)
            else:
                self.login_failed()
        except Exception as e:
            self.show_error_message(f"خطأ في النظام: {str(e)}")
            self.reset_login_form()

    def login_success(self):
        """نجح تسجيل الدخول"""
        # حفظ بيانات الدخول إذا كان مطلوباً
        if self.remember_var.get():
            self.save_login_data()

        # إغلاق نافذة تسجيل الدخول
        self.root.destroy()

    def login_failed(self):
        """فشل تسجيل الدخول"""
        self.login_attempts += 1
        remaining_attempts = self.max_attempts - self.login_attempts

        if remaining_attempts > 0:
            self.show_error_message(f"اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining_attempts}")
        else:
            # قفل الحساب
            self.is_locked = True
            self.lock_time = time.time() + 300  # 5 دقائق
            self.show_error_message("تم قفل الحساب لمدة 5 دقائق بسبب المحاولات الفاشلة المتكررة")

        # مسح كلمة المرور
        self.password_entry.delete(0, 'end')
        self.password_entry.focus()

        self.reset_login_form()

    def reset_login_form(self):
        """إعادة تعيين نموذج تسجيل الدخول"""
        self.hide_progress()
        self.login_btn.config(state='normal', text="🚀 تسجيل الدخول")

    def show_success_message(self, message):
        """إظهار رسالة نجاح"""
        self.status_label.config(
            text=f"✅ {message}",
            fg=COLORS['success']
        )

    def show_error_message(self, message):
        """إظهار رسالة خطأ"""
        self.status_label.config(
            text=f"❌ {message}",
            fg=COLORS['danger']
        )

        # تأثير اهتزاز للنافذة
        self.shake_window()

    def shake_window(self):
        """تأثير اهتزاز للنافذة"""
        original_x = self.root.winfo_x()
        original_y = self.root.winfo_y()

        for i in range(10):
            offset = 5 if i % 2 == 0 else -5
            self.root.geometry(f"+{original_x + offset}+{original_y}")
            self.root.update()
            time.sleep(0.05)

        self.root.geometry(f"+{original_x}+{original_y}")

    def forgot_password(self):
        """نسيت كلمة المرور"""
        self.show_custom_dialog(
            "🔄 استعادة كلمة المرور",
            "لاستعادة كلمة المرور، يرجى التواصل مع مدير النظام.\n\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966 11 123 4567",
            "info"
        )

    def show_help(self):
        """إظهار المساعدة"""
        help_text = """
🔑 تعليمات تسجيل الدخول:

1. أدخل اسم المستخدم وكلمة المرور
2. انقر على زر تسجيل الدخول أو اضغط Enter
3. يمكنك اختيار "تذكر بيانات الدخول" للدخول التلقائي

📋 بيانات الدخول الافتراضية:
• اسم المستخدم: admin
• كلمة المرور: admin123

⚠️ ملاحظات أمنية:
• يتم قفل الحساب بعد 3 محاولات فاشلة
• تأكد من إغلاق النظام بعد الانتهاء
• لا تشارك بيانات الدخول مع الآخرين

📞 للدعم الفني:
<EMAIL>
        """

        self.show_custom_dialog("❓ مساعدة تسجيل الدخول", help_text, "info")

    def show_custom_dialog(self, title, message, dialog_type="info"):
        """إظهار حوار مخصص"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("400x300")
        dialog.configure(bg=COLORS['surface'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # توسيط الحوار
        dialog.geometry("+{}+{}".format(
            self.root.winfo_x() + 50,
            self.root.winfo_y() + 50
        ))

        # محتوى الحوار
        content_frame = create_rtl_frame(dialog, bg=COLORS['surface'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # أيقونة ونوع الحوار
        icon_map = {
            "info": "ℹ️",
            "warning": "⚠️",
            "error": "❌",
            "success": "✅"
        }

        icon_label = create_rtl_label(
            content_frame,
            text=icon_map.get(dialog_type, "ℹ️"),
            font=self.style_manager.get_font('arabic_title', size=24),
            bg=COLORS['surface']
        )
        icon_label.pack(pady=(0, 10))

        # عنوان الحوار
        title_label = create_rtl_label(
            content_frame,
            text=title,
            style='header',
            bg=COLORS['surface']
        )
        title_label.pack(pady=(0, 15))

        # نص الرسالة
        message_text = create_rtl_text(
            content_frame,
            height=8,
            wrap='word',
            state='disabled',
            bg=COLORS['light'],
            relief='flat'
        )
        message_text.pack(fill='both', expand=True, pady=(0, 15))

        # إدراج النص
        message_text.config(state='normal')
        message_text.insert('1.0', message)
        message_text.config(state='disabled')

        # زر الإغلاق
        close_btn = create_rtl_button(
            content_frame,
            text="موافق",
            style='primary',
            command=dialog.destroy
        )
        close_btn.pack(pady=(10, 0))
        close_btn.focus()

        # ربط مفتاح Enter و Escape
        dialog.bind('<Return>', lambda e: dialog.destroy())
        dialog.bind('<Escape>', lambda e: dialog.destroy())

    def save_login_data(self):
        """حفظ بيانات الدخول"""
        # يمكن تنفيذ حفظ بيانات الدخول هنا
        pass

    def open_support(self):
        """فتح رابط الدعم الفني"""
        import webbrowser
        try:
            webbrowser.open("mailto:<EMAIL>")
        except:
            pass

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return auth_manager.is_logged_in()

def show_advanced_login():
    """إظهار نافذة تسجيل الدخول المتقدمة"""
    login_window = AdvancedLoginWindow()
    return login_window.run()

if __name__ == "__main__":
    # اختبار نافذة تسجيل الدخول
    success = show_advanced_login()
    if success:
        print("تم تسجيل الدخول بنجاح!")
    else:
        print("تم إلغاء تسجيل الدخول")
