#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الشحنات
Shipments Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime, date

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES

class ShipmentsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_shipments()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الشحنات - نظام متابعة الشحنات")
        self.window.geometry("1400x800")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="إدارة الشحنات",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار العمليات
        self.add_button = tk.Button(
            buttons_frame,
            text="شحنة جديدة",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.add_shipment
        )
        self.add_button.pack(side='right', padx=5)
        
        self.edit_button = tk.Button(
            buttons_frame,
            text="تعديل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['warning'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.edit_shipment,
            state='disabled'
        )
        self.edit_button.pack(side='right', padx=5)
        
        self.view_button = tk.Button(
            buttons_frame,
            text="عرض التفاصيل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.view_shipment,
            state='disabled'
        )
        self.view_button.pack(side='right', padx=5)
        
        self.track_button = tk.Button(
            buttons_frame,
            text="تتبع الشحنة",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.track_shipment,
            state='disabled'
        )
        self.track_button.pack(side='right', padx=5)
        
        self.refresh_button = tk.Button(
            buttons_frame,
            text="تحديث",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.load_shipments
        )
        self.refresh_button.pack(side='right', padx=5)
        
        # إطار البحث والتصفية
        filter_frame = tk.Frame(main_frame, bg=COLORS['background'])
        filter_frame.pack(fill='x', pady=(0, 10))
        
        # البحث
        search_label = tk.Label(
            filter_frame,
            text="البحث:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        search_label.pack(side='right', padx=(0, 5))
        
        self.search_entry = tk.Entry(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            justify='right',
            width=20
        )
        self.search_entry.pack(side='right', padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.search_shipments)
        
        # تصفية حسب الحالة
        status_label = tk.Label(
            filter_frame,
            text="الحالة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        status_label.pack(side='right', padx=(0, 5))
        
        status_values = ['جميع الحالات'] + list(SHIPMENT_STATUS.values())
        self.status_filter = ttk.Combobox(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            values=status_values,
            state='readonly',
            width=15
        )
        self.status_filter.pack(side='right', padx=(0, 10))
        self.status_filter.set('جميع الحالات')
        self.status_filter.bind('<<ComboboxSelected>>', self.filter_by_status)
        
        # تصفية حسب المورد
        supplier_label = tk.Label(
            filter_frame,
            text="المورد:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        supplier_label.pack(side='right', padx=(0, 5))
        
        self.supplier_filter = ttk.Combobox(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            state='readonly',
            width=20
        )
        self.supplier_filter.pack(side='right', padx=(0, 10))
        self.supplier_filter.bind('<<ComboboxSelected>>', self.filter_by_supplier)
        
        # تحميل الموردين
        self.load_suppliers()
        
        # جدول الشحنات
        self.create_shipments_table(main_frame)
        
    def create_shipments_table(self, parent):
        """إنشاء جدول الشحنات"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=COLORS['white'])
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        columns = ('shipment_number', 'supplier_name', 'shipment_date', 'expected_arrival', 
                  'departure_port', 'arrival_port', 'shipping_company', 'status', 'invoice_amount')
        self.shipments_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        headers = {
            'shipment_number': 'رقم الشحنة',
            'supplier_name': 'المورد',
            'shipment_date': 'تاريخ الشحن',
            'expected_arrival': 'الوصول المتوقع',
            'departure_port': 'ميناء المغادرة',
            'arrival_port': 'ميناء الوصول',
            'shipping_company': 'شركة الشحن',
            'status': 'الحالة',
            'invoice_amount': 'قيمة الفاتورة'
        }
        
        for col in columns:
            self.shipments_tree.heading(col, text=headers[col])
            if col == 'invoice_amount':
                self.shipments_tree.column(col, width=120, anchor='center')
            elif col in ['shipment_date', 'expected_arrival']:
                self.shipments_tree.column(col, width=100, anchor='center')
            else:
                self.shipments_tree.column(col, width=130, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.shipments_tree.yview)
        self.shipments_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.shipments_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.shipments_tree.bind('<<TreeviewSelect>>', self.on_shipment_select)
        self.shipments_tree.bind('<Double-1>', self.view_shipment)
    
    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name')
            suppliers = cursor.fetchall()
            
            supplier_list = ['جميع الموردين'] + [sup['supplier_name'] for sup in suppliers]
            self.supplier_filter['values'] = supplier_list
            self.supplier_filter.set('جميع الموردين')
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الموردين: {str(e)}")
    
    def load_shipments(self):
        """تحميل الشحنات من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.shipments_tree.get_children():
                self.shipments_tree.delete(item)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT s.shipment_number, sup.supplier_name, s.shipment_date, 
                       s.expected_arrival_date, s.departure_port, s.arrival_port,
                       s.shipping_company, s.status, s.invoice_amount, s.currency
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                ORDER BY s.created_at DESC
            ''')
            
            shipments = cursor.fetchall()
            
            for shipment in shipments:
                # تنسيق التواريخ
                shipment_date = shipment['shipment_date'] if shipment['shipment_date'] else ''
                expected_arrival = shipment['expected_arrival_date'] if shipment['expected_arrival_date'] else ''
                
                # تنسيق المبلغ
                amount = ''
                if shipment['invoice_amount']:
                    currency = shipment['currency'] or 'USD'
                    amount = f"{shipment['invoice_amount']:.2f} {currency}"
                
                # ترجمة الحالة
                status_ar = SHIPMENT_STATUS.get(shipment['status'], shipment['status'])
                
                values = (
                    shipment['shipment_number'],
                    shipment['supplier_name'] or '',
                    shipment_date,
                    expected_arrival,
                    shipment['departure_port'] or '',
                    shipment['arrival_port'] or '',
                    shipment['shipping_company'] or '',
                    status_ar,
                    amount
                )
                self.shipments_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")
    
    def search_shipments(self, event=None):
        """البحث في الشحنات"""
        search_term = self.search_entry.get().strip()
        status_filter = self.status_filter.get()
        supplier_filter = self.supplier_filter.get()
        
        self.filter_shipments(search_term, status_filter, supplier_filter)
    
    def filter_by_status(self, event=None):
        """تصفية حسب الحالة"""
        search_term = self.search_entry.get().strip()
        status_filter = self.status_filter.get()
        supplier_filter = self.supplier_filter.get()
        
        self.filter_shipments(search_term, status_filter, supplier_filter)
    
    def filter_by_supplier(self, event=None):
        """تصفية حسب المورد"""
        search_term = self.search_entry.get().strip()
        status_filter = self.status_filter.get()
        supplier_filter = self.supplier_filter.get()
        
        self.filter_shipments(search_term, status_filter, supplier_filter)
    
    def filter_shipments(self, search_term="", status_filter="جميع الحالات", supplier_filter="جميع الموردين"):
        """تصفية الشحنات"""
        # مسح البيانات الحالية
        for item in self.shipments_tree.get_children():
            self.shipments_tree.delete(item)
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            query = '''
                SELECT s.shipment_number, sup.supplier_name, s.shipment_date, 
                       s.expected_arrival_date, s.departure_port, s.arrival_port,
                       s.shipping_company, s.status, s.invoice_amount, s.currency
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                WHERE 1=1
            '''
            params = []
            
            if search_term:
                query += ' AND (s.shipment_number LIKE ? OR sup.supplier_name LIKE ? OR s.container_number LIKE ?)'
                params.extend([f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'])
            
            if status_filter != 'جميع الحالات':
                # البحث عن المفتاح الإنجليزي للحالة
                status_key = None
                for key, value in SHIPMENT_STATUS.items():
                    if value == status_filter:
                        status_key = key
                        break
                if status_key:
                    query += ' AND s.status = ?'
                    params.append(status_key)
            
            if supplier_filter != 'جميع الموردين':
                query += ' AND sup.supplier_name = ?'
                params.append(supplier_filter)
            
            query += ' ORDER BY s.created_at DESC'
            
            cursor.execute(query, params)
            shipments = cursor.fetchall()
            
            for shipment in shipments:
                # تنسيق التواريخ
                shipment_date = shipment['shipment_date'] if shipment['shipment_date'] else ''
                expected_arrival = shipment['expected_arrival_date'] if shipment['expected_arrival_date'] else ''
                
                # تنسيق المبلغ
                amount = ''
                if shipment['invoice_amount']:
                    currency = shipment['currency'] or 'USD'
                    amount = f"{shipment['invoice_amount']:.2f} {currency}"
                
                # ترجمة الحالة
                status_ar = SHIPMENT_STATUS.get(shipment['status'], shipment['status'])
                
                values = (
                    shipment['shipment_number'],
                    shipment['supplier_name'] or '',
                    shipment_date,
                    expected_arrival,
                    shipment['departure_port'] or '',
                    shipment['arrival_port'] or '',
                    shipment['shipping_company'] or '',
                    status_ar,
                    amount
                )
                self.shipments_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التصفية: {str(e)}")
    
    def on_shipment_select(self, event=None):
        """عند اختيار شحنة من الجدول"""
        selection = self.shipments_tree.selection()
        if selection:
            self.edit_button.config(state='normal')
            self.view_button.config(state='normal')
            self.track_button.config(state='normal')
        else:
            self.edit_button.config(state='disabled')
            self.view_button.config(state='disabled')
            self.track_button.config(state='disabled')
    
    def add_shipment(self):
        """إضافة شحنة جديدة"""
        if not auth_manager.has_permission('edit_shipments'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإضافة الشحنات")
            return
        
        self.open_shipment_form()
    
    def edit_shipment(self, event=None):
        """تعديل شحنة"""
        if not auth_manager.has_permission('edit_shipments'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل الشحنات")
            return
        
        selection = self.shipments_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتعديل")
            return
        
        item = self.shipments_tree.item(selection[0])
        shipment_number = item['values'][0]
        self.open_shipment_form(shipment_number)
    
    def view_shipment(self, event=None):
        """عرض تفاصيل الشحنة"""
        selection = self.shipments_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة لعرض التفاصيل")
            return
        
        item = self.shipments_tree.item(selection[0])
        shipment_number = item['values'][0]
        self.open_shipment_details(shipment_number)
    
    def track_shipment(self):
        """تتبع الشحنة"""
        selection = self.shipments_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتتبع")
            return
        
        item = self.shipments_tree.item(selection[0])
        shipment_number = item['values'][0]
        self.open_tracking_window(shipment_number)
    
    def open_shipment_form(self, shipment_number=None):
        """فتح نموذج الشحنة"""
        try:
            from src.shipment_form import ShipmentForm
            ShipmentForm(self.window, self.db_manager, shipment_number, self.load_shipments)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نموذج الشحنة قريباً")
    
    def open_shipment_details(self, shipment_number):
        """فتح تفاصيل الشحنة"""
        try:
            from src.shipment_details import ShipmentDetails
            ShipmentDetails(self.window, self.db_manager, shipment_number)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح تفاصيل الشحنة قريباً")
    
    def open_tracking_window(self, shipment_number):
        """فتح نافذة تتبع الشحنة"""
        try:
            from src.shipment_tracking import ShipmentTracking
            ShipmentTracking(self.window, self.db_manager, shipment_number)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة التتبع قريباً")


if __name__ == "__main__":
    # تشغيل نافذة الشحنات للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    shipments_window = ShipmentsWindow()
    shipments_window.window.mainloop()
