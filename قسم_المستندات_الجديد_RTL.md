# 📄 قسم المستندات الجديد في نموذج الشحنة

## 📋 نظرة عامة

تم إضافة **قسم المستندات** الجديد كالقسم الخامس في نموذج الشحنة بملء الشاشة، ليكون بين قسم المالية وقسم التتبع. هذا القسم مخصص لإدارة جميع المستندات والوثائق الرسمية المطلوبة للشحنة والجمارك.

## ✨ الميزات الرئيسية

### 📊 **9 أنواع مستندات أساسية**

#### **1️⃣ المستندات التجارية**
- **📋 الفاتورة التجارية**: رقم الفاتورة التجارية مع حالة المستند
- **📦 قائمة التعبئة**: رقم قائمة التعبئة مع حالة المستند
- **🏭 شهادة المنشأ**: رقم شهادة المنشأ مع حالة المستند

#### **2️⃣ مستندات التأمين والجودة**
- **🛡️ بوليصة التأمين**: رقم بوليصة التأمين مع حالة المستند
- **✅ شهادة الجودة**: رقم شهادة الجودة مع حالة المستند
- **🏥 شهادة الصحة**: رقم شهادة الصحة مع حالة المستند

#### **3️⃣ مستندات الجمارك والتراخيص**
- **🏛️ الإقرار الجمركي**: رقم الإقرار الجمركي مع حالة المستند
- **📜 رخصة الاستيراد**: رقم رخصة الاستيراد مع حالة المستند
- **🔍 شهادة التفتيش**: رقم شهادة التفتيش مع حالة المستند

#### **4️⃣ مستندات إضافية**
- **📋 مستندات أخرى**: حقل مفتوح لأرقام المستندات الأخرى
- **📊 حالة المستندات العامة**: تقييم شامل لحالة جميع المستندات

### 🎯 **حالات المستندات الذكية**

#### **حالات المستند الفردي:**
- **غير متوفر** ⚪: المستند غير موجود
- **متوفر** 🟢: المستند موجود ومتاح
- **مرسل** 🟡: تم إرسال المستند
- **مؤكد** ✅: تم تأكيد استلام المستند
- **مرفوض** ❌: تم رفض المستند

#### **حالات المستندات العامة:**
- **غير مكتملة** 🔴: أقل من 25% من المستندات مكتملة
- **قيد المراجعة** 🟡: 25-49% من المستندات مكتملة
- **مكتملة جزئياً** 🟠: 50-74% من المستندات مكتملة
- **مكتملة** 🟢: 75-99% من المستندات مكتملة
- **مؤكدة** ✅: 100% من المستندات مكتملة ومؤكدة
- **مرفوضة** ❌: تم رفض بعض المستندات

### 📊 **شريط التقدم الذكي**

#### **حساب نسبة الاكتمال:**
```python
نسبة الاكتمال = (عدد المستندات المكتملة / إجمالي المستندات) × 100

المستندات المكتملة = المستندات بحالة:
- متوفر ✅
- مرسل ✅  
- مؤكد ✅

إجمالي المستندات = 9 مستندات أساسية
```

#### **ألوان شريط التقدم:**
- **🔴 أحمر**: 0-49% (غير مكتملة)
- **🟡 أصفر**: 50-74% (مكتملة جزئياً)
- **🔵 أزرق**: 75-99% (مكتملة)
- **🟢 أخضر**: 100% (مكتملة بالكامل)

### 📝 **ملاحظات المستندات**

#### **منطقة نص مخصصة:**
- **4 أسطر**: مساحة كافية للملاحظات
- **دعم RTL**: محاذاة يمين طبيعية للعربية
- **تمرير تلقائي**: عند الحاجة لمساحة أكبر
- **حفظ تلقائي**: مع باقي بيانات النموذج

## 🛠️ **التقنيات المستخدمة**

### 🗄️ **قاعدة البيانات المحسنة**

#### **حقول جديدة في جدول shipments:**
```sql
-- المستندات التجارية
commercial_invoice TEXT,
commercial_invoice_status TEXT DEFAULT 'غير متوفر',
packing_list TEXT,
packing_list_status TEXT DEFAULT 'غير متوفر',
certificate_of_origin TEXT,
certificate_of_origin_status TEXT DEFAULT 'غير متوفر',

-- مستندات التأمين والجودة
insurance_policy TEXT,
insurance_policy_status TEXT DEFAULT 'غير متوفر',
quality_certificate TEXT,
quality_certificate_status TEXT DEFAULT 'غير متوفر',
health_certificate TEXT,
health_certificate_status TEXT DEFAULT 'غير متوفر',

-- مستندات الجمارك
customs_declaration TEXT,
customs_declaration_status TEXT DEFAULT 'غير متوفر',
import_license TEXT,
import_license_status TEXT DEFAULT 'غير متوفر',
inspection_certificate TEXT,
inspection_certificate_status TEXT DEFAULT 'غير متوفر',

-- مستندات إضافية
other_documents TEXT,
documents_status TEXT DEFAULT 'غير مكتملة',
documents_notes TEXT
```

### 🎨 **مكونات RTL مخصصة**

#### **create_document_field():**
```python
def create_document_field(parent, label_text, var_name, placeholder, width_ratio):
    """إنشاء حقل مستند مع تسمية وحالة"""
    - حقل إدخال لرقم المستند
    - قائمة منسدلة لحالة المستند
    - ربط تلقائي مع تحديث التقدم
    - تخطيط RTL محسن
```

#### **create_documents_status_field():**
```python
def create_documents_status_field(parent, width_ratio):
    """إنشاء حقل حالة المستندات العامة"""
    - قائمة منسدلة للحالة العامة
    - تحديث تلقائي حسب نسبة الاكتمال
    - ألوان ذكية حسب الحالة
```

### 🔄 **الوظائف التفاعلية**

#### **on_document_change():**
```python
def on_document_change(*args):
    """عند تغيير أي مستند"""
    - تحديث مؤشر التعديل
    - إعادة حساب نسبة التقدم
    - تحديث الحالة العامة تلقائياً
```

#### **update_documents_progress():**
```python
def update_documents_progress():
    """تحديث تقدم المستندات"""
    - حساب المستندات المكتملة
    - تحديث شريط التقدم
    - تغيير ألوان النص
    - تحديث الحالة العامة
```

## 🎯 **التكامل مع النظام**

### 📊 **ترقيم الأقسام الجديد:**
1. **📋 المعلومات الأساسية**
2. **🚢 معلومات الشحن**
3. **📦 معلومات الحاوية**
4. **💰 المعلومات المالية**
5. **📄 المستندات** ← **جديد!**
6. **📊 التتبع والحالة**
7. **📝 الملاحظات والمرفقات**

### ⌨️ **اختصارات لوحة المفاتيح المحدثة:**
- **Ctrl+5**: الانتقال المباشر لقسم المستندات
- **Ctrl+1-7**: الانتقال المباشر لجميع الأقسام السبعة

### 🔄 **التحديثات المطلوبة:**

#### **في قاعدة البيانات:**
- ✅ إضافة 21 حقل جديد للمستندات
- ✅ تحديث استعلامات الإنشاء والتحديث
- ✅ دعم القيم الافتراضية

#### **في النموذج:**
- ✅ إضافة قسم المستندات كالقسم الخامس
- ✅ تحديث التنقل والمؤشرات
- ✅ ربط الأحداث والحسابات التلقائية

#### **في الواجهة:**
- ✅ تحديث أزرار التنقل العلوية
- ✅ تحديث مؤشر القسم (1 من 7)
- ✅ تحديث نص المساعدة

## 🚀 **كيفية الاستخدام**

### **1. الوصول للقسم:**
```
طرق الوصول:
- انقر على "📄 المستندات" في الشريط العلوي
- استخدم Ctrl+5 للانتقال المباشر
- استخدم أزرار التنقل السفلية
```

### **2. إدخال بيانات المستندات:**
```
لكل مستند:
1. أدخل رقم المستند في الحقل العلوي
2. اختر حالة المستند من القائمة المنسدلة
3. سيتم تحديث شريط التقدم تلقائياً
```

### **3. مراقبة التقدم:**
```
مؤشرات التقدم:
- شريط التقدم: نسبة مئوية مع ألوان
- النص: "X% مكتمل (Y/9)"
- الحالة العامة: تحديث تلقائي
```

### **4. إضافة ملاحظات:**
```
منطقة الملاحظات:
- ملاحظات حول المستندات المفقودة
- تفاصيل حول المتطلبات الخاصة
- معلومات الاتصال للحصول على المستندات
```

## 🎉 **النتيجة النهائية**

**✅ تم إضافة قسم المستندات بنجاح!**

### **🌟 الإنجازات المحققة:**
- 📄 **قسم مستندات شامل** مع 9 أنواع مستندات أساسية
- 🎯 **حالات ذكية** مع 5 حالات لكل مستند
- 📊 **شريط تقدم تفاعلي** مع حساب تلقائي ونسبة مئوية
- 🎨 **تصميم RTL متقدم** مع ألوان ذكية ومكونات مخصصة
- 🗄️ **قاعدة بيانات محسنة** مع 21 حقل جديد
- 🔄 **تكامل كامل** مع النظام الموجود
- ⌨️ **اختصارات محدثة** للوصول السريع
- 📝 **ملاحظات مخصصة** للمستندات

### **📊 الإحصائيات:**
- **إجمالي الأقسام**: 7 أقسام (كان 6)
- **إجمالي المستندات**: 9 مستندات أساسية
- **إجمالي الحالات**: 5 حالات لكل مستند
- **إجمالي الحقول الجديدة**: 21 حقل في قاعدة البيانات

**قسم المستندات جاهز للاستخدام الإنتاجي مع تجربة مستخدم متقدمة!** 📄✨📊🎨
