#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط RTL متقدمة لنظام إدارة الشحنات
Advanced RTL Styles for Shipments Management System
"""

import tkinter as tk
from tkinter import ttk
import os
import sys

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG

class AdvancedRTLStyleManager:
    """مدير الأنماط المتقدمة مع دعم RTL شامل"""
    
    def __init__(self):
        self.setup_advanced_styles()
        self.setup_rtl_fonts()
        self.setup_color_schemes()
        
    def setup_advanced_styles(self):
        """إعداد الأنماط المتقدمة"""
        # أنماط CSS متقدمة محاكاة في Tkinter
        self.styles = {
            # أنماط الحاويات
            'container_primary': {
                'bg': COLORS['surface'],
                'relief': 'flat',
                'bd': 0,
                'highlightthickness': 1,
                'highlightcolor': COLORS['primary'],
                'highlightbackground': COLORS['border']
            },
            
            'container_elevated': {
                'bg': COLORS['surface'],
                'relief': 'raised',
                'bd': 2,
                'highlightthickness': 0
            },
            
            'container_card': {
                'bg': COLORS['surface'],
                'relief': 'solid',
                'bd': 1,
                'highlightthickness': 2,
                'highlightcolor': COLORS['light'],
                'highlightbackground': COLORS['border']
            },
            
            # أنماط النصوص
            'text_heading_xl': {
                'font': ('Segoe UI', 24, 'bold'),
                'fg': COLORS['text_primary'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            'text_heading_lg': {
                'font': ('Segoe UI', 20, 'bold'),
                'fg': COLORS['text_primary'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            'text_heading_md': {
                'font': ('Segoe UI', 16, 'bold'),
                'fg': COLORS['text_primary'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            'text_body_lg': {
                'font': ('Segoe UI', 14, 'normal'),
                'fg': COLORS['text_primary'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            'text_body_md': {
                'font': ('Segoe UI', 12, 'normal'),
                'fg': COLORS['text_primary'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            'text_body_sm': {
                'font': ('Segoe UI', 10, 'normal'),
                'fg': COLORS['text_secondary'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            'text_caption': {
                'font': ('Segoe UI', 9, 'normal'),
                'fg': COLORS['text_muted'],
                'bg': COLORS['surface'],
                'anchor': 'e',
                'justify': 'right'
            },
            
            # أنماط الأزرار المتقدمة
            'button_primary': {
                'bg': COLORS['primary'],
                'fg': COLORS['text_white'],
                'font': ('Segoe UI', 12, 'bold'),
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2',
                'activebackground': COLORS['primary_dark'],
                'activeforeground': COLORS['text_white']
            },
            
            'button_secondary': {
                'bg': COLORS['secondary'],
                'fg': COLORS['text_white'],
                'font': ('Segoe UI', 12, 'bold'),
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2',
                'activebackground': COLORS['secondary_dark'],
                'activeforeground': COLORS['text_white']
            },
            
            'button_success': {
                'bg': COLORS['success'],
                'fg': COLORS['text_white'],
                'font': ('Segoe UI', 12, 'bold'),
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2',
                'activebackground': COLORS['success_dark'],
                'activeforeground': COLORS['text_white']
            },
            
            'button_warning': {
                'bg': COLORS['warning'],
                'fg': COLORS['text_white'],
                'font': ('Segoe UI', 12, 'bold'),
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2',
                'activebackground': COLORS['warning_dark'],
                'activeforeground': COLORS['text_white']
            },
            
            'button_danger': {
                'bg': COLORS['danger'],
                'fg': COLORS['text_white'],
                'font': ('Segoe UI', 12, 'bold'),
                'relief': 'flat',
                'bd': 0,
                'padx': 20,
                'pady': 10,
                'cursor': 'hand2',
                'activebackground': COLORS['danger_dark'],
                'activeforeground': COLORS['text_white']
            },
            
            'button_outline': {
                'bg': COLORS['surface'],
                'fg': COLORS['primary'],
                'font': ('Segoe UI', 12, 'normal'),
                'relief': 'solid',
                'bd': 2,
                'padx': 20,
                'pady': 8,
                'cursor': 'hand2',
                'highlightthickness': 0,
                'activebackground': COLORS['primary_light'],
                'activeforeground': COLORS['primary']
            },
            
            'button_ghost': {
                'bg': COLORS['surface'],
                'fg': COLORS['text_secondary'],
                'font': ('Segoe UI', 11, 'normal'),
                'relief': 'flat',
                'bd': 0,
                'padx': 15,
                'pady': 8,
                'cursor': 'hand2',
                'activebackground': COLORS['light'],
                'activeforeground': COLORS['text_primary']
            },
            
            # أنماط الحقول المتقدمة
            'input_primary': {
                'font': ('Segoe UI', 12, 'normal'),
                'bg': COLORS['surface'],
                'fg': COLORS['text_primary'],
                'relief': 'solid',
                'bd': 2,
                'highlightthickness': 2,
                'highlightcolor': COLORS['primary'],
                'highlightbackground': COLORS['border'],
                'insertbackground': COLORS['primary'],
                'selectbackground': COLORS['primary_light'],
                'selectforeground': COLORS['text_primary'],
                'justify': 'right'
            },
            
            'input_search': {
                'font': ('Segoe UI', 11, 'normal'),
                'bg': COLORS['light'],
                'fg': COLORS['text_primary'],
                'relief': 'flat',
                'bd': 0,
                'highlightthickness': 1,
                'highlightcolor': COLORS['primary'],
                'highlightbackground': COLORS['border'],
                'insertbackground': COLORS['primary'],
                'selectbackground': COLORS['primary_light'],
                'selectforeground': COLORS['text_primary'],
                'justify': 'right'
            },
            
            # أنماط القوائم المنسدلة
            'combobox_primary': {
                'font': ('Segoe UI', 12, 'normal'),
                'background': COLORS['surface'],
                'foreground': COLORS['text_primary'],
                'fieldbackground': COLORS['surface'],
                'borderwidth': 2,
                'relief': 'solid',
                'justify': 'right'
            },
            
            # أنماط الجداول
            'treeview_primary': {
                'background': COLORS['surface'],
                'foreground': COLORS['text_primary'],
                'fieldbackground': COLORS['surface'],
                'borderwidth': 1,
                'relief': 'solid',
                'font': ('Segoe UI', 11, 'normal'),
                'rowheight': 30
            },
            
            'treeview_heading': {
                'background': COLORS['primary'],
                'foreground': COLORS['text_white'],
                'font': ('Segoe UI', 11, 'bold'),
                'relief': 'flat',
                'borderwidth': 0
            }
        }
    
    def setup_rtl_fonts(self):
        """إعداد الخطوط العربية المتقدمة"""
        self.rtl_fonts = {
            'arabic_display': ('Segoe UI', 28, 'bold'),      # للعناوين الكبيرة
            'arabic_title': ('Segoe UI', 24, 'bold'),        # للعناوين الرئيسية
            'arabic_heading': ('Segoe UI', 20, 'bold'),      # للعناوين الفرعية
            'arabic_subheading': ('Segoe UI', 16, 'bold'),   # للعناوين الصغيرة
            'arabic_body_large': ('Segoe UI', 14, 'normal'), # للنص الكبير
            'arabic_body': ('Segoe UI', 12, 'normal'),       # للنص العادي
            'arabic_body_small': ('Segoe UI', 11, 'normal'), # للنص الصغير
            'arabic_caption': ('Segoe UI', 10, 'normal'),    # للتسميات
            'arabic_tiny': ('Segoe UI', 9, 'normal'),        # للنص الصغير جداً
            
            # خطوط خاصة
            'arabic_button': ('Segoe UI', 12, 'bold'),       # للأزرار
            'arabic_input': ('Segoe UI', 12, 'normal'),      # للحقول
            'arabic_table': ('Segoe UI', 11, 'normal'),      # للجداول
            'arabic_menu': ('Segoe UI', 11, 'normal'),       # للقوائم
            
            # خطوط بديلة
            'arabic_fallback': [
                'Segoe UI',
                'Tahoma',
                'Arial Unicode MS',
                'Microsoft Sans Serif',
                'Arial',
                'sans-serif'
            ]
        }
    
    def setup_color_schemes(self):
        """إعداد مخططات الألوان المتقدمة"""
        self.color_schemes = {
            # مخطط الألوان الأساسي
            'primary_scheme': {
                'primary': '#1E40AF',
                'primary_light': '#3B82F6',
                'primary_dark': '#1E3A8A',
                'primary_subtle': '#EFF6FF'
            },
            
            # مخطط ألوان الحالات
            'status_scheme': {
                'pending': '#FEF3C7',      # في الانتظار - أصفر فاتح
                'confirmed': '#DBEAFE',    # مؤكدة - أزرق فاتح
                'shipped': '#E0E7FF',      # تم الشحن - بنفسجي فاتح
                'in_transit': '#FDE68A',   # في الطريق - أصفر
                'arrived': '#D1FAE5',      # وصلت - أخضر فاتح
                'in_customs': '#FED7AA',   # في الجمارك - برتقالي فاتح
                'delivered': '#BBF7D0',    # تم التسليم - أخضر
                'cancelled': '#FECACA',    # ملغية - أحمر فاتح
                'delayed': '#F87171'       # متأخرة - أحمر
            },
            
            # مخطط ألوان الأولوية
            'priority_scheme': {
                'urgent': '#DC2626',       # عاجل - أحمر
                'high': '#EA580C',         # عالي - برتقالي
                'medium': '#D97706',       # متوسط - أصفر
                'low': '#059669',          # منخفض - أخضر
                'normal': '#6B7280'        # عادي - رمادي
            },
            
            # مخطط ألوان التفاعل
            'interaction_scheme': {
                'hover': '#F3F4F6',
                'active': '#E5E7EB',
                'focus': '#3B82F6',
                'selected': '#1E40AF',
                'disabled': '#9CA3AF'
            }
        }
    
    def get_style(self, style_name):
        """الحصول على نمط محدد"""
        return self.styles.get(style_name, {})
    
    def get_font(self, font_name):
        """الحصول على خط محدد"""
        return self.rtl_fonts.get(font_name, ('Segoe UI', 12, 'normal'))
    
    def get_color_scheme(self, scheme_name):
        """الحصول على مخطط ألوان محدد"""
        return self.color_schemes.get(scheme_name, {})
    
    def apply_rtl_style(self, widget, style_name):
        """تطبيق نمط RTL على عنصر"""
        style = self.get_style(style_name)
        if style:
            widget.configure(**style)
    
    def create_gradient_effect(self, widget, start_color, end_color):
        """إنشاء تأثير تدرج لوني (محاكاة)"""
        # محاكاة التدرج باستخدام ألوان متوسطة
        widget.configure(bg=start_color)
        
        def on_enter(event):
            widget.configure(bg=end_color)
        
        def on_leave(event):
            widget.configure(bg=start_color)
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    def create_shadow_effect(self, widget):
        """إنشاء تأثير ظل (محاكاة)"""
        # محاكاة الظل باستخدام إطار إضافي
        shadow_frame = tk.Frame(
            widget.master,
            bg='#00000020',  # لون رمادي شفاف
            height=2,
            relief='flat'
        )
        
        # وضع الظل تحت العنصر
        widget.update_idletasks()
        shadow_frame.place(
            x=widget.winfo_x() + 2,
            y=widget.winfo_y() + widget.winfo_height(),
            width=widget.winfo_width()
        )
        
        return shadow_frame

# إنشاء مثيل عام من مدير الأنماط
advanced_style_manager = AdvancedRTLStyleManager()
