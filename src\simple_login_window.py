#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول البسيطة مع دعم RTL
Simple Login Window with RTL Support
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys
from datetime import datetime
import time

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from config.config import APP_CONFIG, UI_CONFIG, COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG

class SimpleLoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.login_attempts = 0
        self.max_attempts = 3
        self.is_locked = False
        self.lock_time = 0
        self.setup_window()
        self.create_interface()
        self.center_window()

    def setup_window(self):
        """إعداد النافذة الأساسية"""
        self.root.title(f"🔐 {APP_CONFIG['name']} - تسجيل الدخول")
        self.root.geometry("400x300")
        self.root.configure(bg=COLORS.get('surface', '#FFFFFF'))
        self.root.resizable(False, False)
        
        # جعل النافذة في المقدمة
        self.root.attributes('-topmost', True)
        self.root.lift()
        self.root.focus_force()
        
        # ربط الأحداث
        self.root.bind('<Return>', self.on_enter_pressed)
        self.root.bind('<Escape>', self.on_escape_pressed)

    def create_interface(self):
        """إنشاء واجهة بسيطة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=COLORS.get('surface', '#FFFFFF'))
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان التطبيق
        title_label = tk.Label(
            main_frame,
            text=f"🚢 {APP_CONFIG['name']}",
            font=('Tahoma', 16, 'bold'),
            bg=COLORS.get('surface', '#FFFFFF'),
            fg=COLORS.get('primary', '#2E86AB')
        )
        title_label.pack(pady=(0, 20))
        
        # حقل اسم المستخدم
        username_label = tk.Label(
            main_frame,
            text="👤 اسم المستخدم:",
            font=('Tahoma', 12),
            bg=COLORS.get('surface', '#FFFFFF'),
            fg=COLORS.get('text_primary', '#000000'),
            anchor='e'
        )
        username_label.pack(fill='x', pady=(0, 5))
        
        self.username_entry = tk.Entry(
            main_frame,
            font=('Tahoma', 12),
            justify='right',
            relief='solid',
            bd=1
        )
        self.username_entry.pack(fill='x', pady=(0, 15), ipady=5)
        self.username_entry.insert(0, "admin")
        
        # حقل كلمة المرور
        password_label = tk.Label(
            main_frame,
            text="🔒 كلمة المرور:",
            font=('Tahoma', 12),
            bg=COLORS.get('surface', '#FFFFFF'),
            fg=COLORS.get('text_primary', '#000000'),
            anchor='e'
        )
        password_label.pack(fill='x', pady=(0, 5))
        
        self.password_entry = tk.Entry(
            main_frame,
            font=('Tahoma', 12),
            justify='right',
            show="*",
            relief='solid',
            bd=1
        )
        self.password_entry.pack(fill='x', pady=(0, 20), ipady=5)
        
        # زر تسجيل الدخول
        self.login_btn = tk.Button(
            main_frame,
            text="🚀 تسجيل الدخول",
            font=('Tahoma', 12, 'bold'),
            bg=COLORS.get('primary', '#2E86AB'),
            fg='white',
            relief='flat',
            command=self.attempt_login,
            cursor='hand2'
        )
        self.login_btn.pack(fill='x', pady=(0, 10), ipady=8)
        
        # معلومات الحالة
        self.status_label = tk.Label(
            main_frame,
            text="🟢 النظام جاهز للاستخدام",
            font=('Tahoma', 10),
            bg=COLORS.get('surface', '#FFFFFF'),
            fg=COLORS.get('success', '#28A745')
        )
        self.status_label.pack(pady=(10, 0))

    def on_enter_pressed(self, event):
        """عند الضغط على Enter"""
        if not self.is_locked:
            self.attempt_login()

    def on_escape_pressed(self, event):
        """عند الضغط على Escape"""
        self.root.quit()

    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        if self.is_locked:
            remaining_time = self.lock_time - time.time()
            if remaining_time > 0:
                self.show_error_message(f"الحساب مقفل. المحاولة مرة أخرى خلال {int(remaining_time)} ثانية")
                return
            else:
                self.is_locked = False
                self.login_attempts = 0

        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        # التحقق من صحة البيانات
        if not username:
            self.show_error_message("يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return

        if not password:
            self.show_error_message("يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return

        # تعطيل زر تسجيل الدخول وإظهار التقدم
        self.login_btn.config(state='disabled', text="🔄 جاري التحقق...")
        self.status_label.config(text="🔄 جاري التحقق من البيانات...", fg=COLORS.get('warning', '#FFC107'))
        
        # محاكاة تأخير التحقق
        self.root.after(1000, lambda: self.process_login(username, password))

    def process_login(self, username, password):
        """معالجة تسجيل الدخول"""
        try:
            # محاولة تسجيل الدخول
            if auth_manager.login(username, password):
                self.show_success_message("تم تسجيل الدخول بنجاح!")
                self.root.after(1000, self.login_success)
            else:
                self.login_failed()
        except Exception as e:
            self.show_error_message(f"خطأ في النظام: {str(e)}")
            self.reset_login_form()

    def login_success(self):
        """نجح تسجيل الدخول"""
        self.root.destroy()

    def login_failed(self):
        """فشل تسجيل الدخول"""
        self.login_attempts += 1
        remaining_attempts = self.max_attempts - self.login_attempts

        if remaining_attempts > 0:
            self.show_error_message(f"اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining_attempts}")
        else:
            # قفل الحساب
            self.is_locked = True
            self.lock_time = time.time() + 300  # 5 دقائق
            self.show_error_message("تم قفل الحساب لمدة 5 دقائق بسبب المحاولات الفاشلة المتكررة")

        # مسح كلمة المرور
        self.password_entry.delete(0, 'end')
        self.password_entry.focus()

        self.reset_login_form()

    def reset_login_form(self):
        """إعادة تعيين نموذج تسجيل الدخول"""
        self.login_btn.config(state='normal', text="🚀 تسجيل الدخول")
        self.status_label.config(text="🟢 النظام جاهز للاستخدام", fg=COLORS.get('success', '#28A745'))

    def show_success_message(self, message):
        """إظهار رسالة نجاح"""
        self.status_label.config(text=f"✅ {message}", fg=COLORS.get('success', '#28A745'))

    def show_error_message(self, message):
        """إظهار رسالة خطأ"""
        self.status_label.config(text=f"❌ {message}", fg=COLORS.get('danger', '#DC3545'))

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return auth_manager.is_logged_in()

def show_simple_login():
    """إظهار نافذة تسجيل الدخول البسيطة"""
    login_window = SimpleLoginWindow()
    return login_window.run()

if __name__ == "__main__":
    # اختبار نافذة تسجيل الدخول
    success = show_simple_login()
    if success:
        print("تم تسجيل الدخول بنجاح!")
    else:
        print("تم إلغاء تسجيل الدخول")
