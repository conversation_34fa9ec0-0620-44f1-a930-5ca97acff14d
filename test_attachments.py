#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار المرفقات في قسم المستندات
Test Attachments Buttons in Documents Section
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.fullscreen_shipment_form import FullscreenShipmentForm

def test_attachments():
    """اختبار أزرار المرفقات"""

    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار أزرار المرفقات")
    root.geometry("1200x800")
    root.configure(bg='#f0f0f0')

    try:
        # تهيئة مدير المصادقة المؤقت
        from src.auth_manager import auth_manager
        auth_manager.current_user = {
            'username': 'test_user',
            'role': 'admin',
            'permissions': ['all']
        }

        # إنشاء نموذج الشحنة
        form = FullscreenShipmentForm(root, mode='add')
        
        # التبديل لقسم المستندات مباشرة
        form.switch_section('documents')
        
        # إظهار رسالة ترحيب
        from tkinter import messagebox
        messagebox.showinfo(
            "اختبار أزرار المرفقات",
            """🎉 مرحباً بك في اختبار أزرار المرفقات!

📋 التعليمات:
1. انتقل لقسم "📄 المستندات" (القسم الخامس)
2. ستجد زر "📎 إضافة مرفق" بجانب كل حقل مستند
3. انقر على أي زر لاختيار ملف
4. سيتم عرض اسم الملف وحجمه تحت الحقل
5. انقر على اسم الملف لعرض التفاصيل

✨ الميزات الجديدة:
• زر مرفق لكل مستند (9 أزرار)
• دعم أنواع ملفات متعددة
• عرض حجم الملف
• عرض تفاصيل المرفقات
• واجهة عربية كاملة

🔍 جرب الآن!"""
        )
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        from tkinter import messagebox
        messagebox.showerror("خطأ", f"خطأ في تشغيل الاختبار: {str(e)}")
        print(f"خطأ تفصيلي: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("📎 بدء اختبار أزرار المرفقات...")
    print("💡 سيتم فتح نموذج الشحنة مع التركيز على قسم المستندات")
    print("💡 جرب النقر على أزرار 'إضافة مرفق' بجانب كل حقل")
    print("-" * 60)
    
    try:
        test_attachments()
        print("✅ انتهى الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
