#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الشحنة بملء الشاشة مع تصميم متقدم وشامل
Fullscreen Shipment Form with Advanced Design
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date, timedelta
import uuid
import json
from tkcalendar import DateEntry

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES, CONTAINER_TYPES, PAYMENT_METHODS
from database.database_manager import DatabaseManager
from src.simple_rtl_components import *
from src.auth_manager import auth_manager
from src.item_dialog import ItemDialog
from src.item_search_dialog import ItemSearchDialog

class FullscreenShipmentForm:
    """نموذج الشحنة بملء الشاشة"""
    
    def __init__(self, parent, mode='add', shipment_data=None):
        self.parent = parent
        self.mode = mode  # add, edit, duplicate, view
        self.shipment_data = shipment_data or {}
        self.db_manager = DatabaseManager()
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_fullscreen_window()
        
        # متغيرات النموذج
        self.form_vars = {}
        self.is_modified = False
        self.current_section = 0
        self.sections = []

        # قاموس المرفقات للمستندات
        self.document_attachments = {}
        print("تم تهيئة قاموس المرفقات")

        # قاموس الروابط للمستندات
        self.document_links = {}
        print("تم تهيئة قاموس الروابط")
        
        # إنشاء الواجهة
        self.create_fullscreen_interface()
        
        # تحميل البيانات
        self.load_form_data()
        
        # ربط الأحداث
        self.bind_events()
        
    def setup_fullscreen_window(self):
        """إعداد النافذة في وضع ملء الشاشة الكامل"""
        titles = {
            'add': '➕ إضافة شحنة جديدة',
            'edit': '✏️ تعديل الشحنة',
            'duplicate': '📋 نسخ الشحنة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }

        self.root.title(titles.get(self.mode, 'نموذج الشحنة'))

        # تعيين وضع ملء الشاشة الكامل
        self.root.state('zoomed')  # للويندوز - ملء الشاشة
        self.root.configure(bg=COLORS['background'])

        # إعداد النافذة
        self.root.resizable(True, True)

        # الحد الأدنى للحجم (في حالة الخروج من وضع ملء الشاشة)
        self.root.minsize(1200, 800)

        # جعل النافذة modal
        if self.parent:
            self.root.transient(self.parent)
            self.root.grab_set()

        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # ربط مفاتيح الاختصار للتحكم في وضع ملء الشاشة
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Escape>', self.exit_fullscreen)

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.root.update_idletasks()
        width = 1400
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # إخفاء شريط العنوان (اختياري)
        # self.root.overrideredirect(True)

    def toggle_fullscreen(self, event=None):
        """تبديل وضع ملء الشاشة (F11)"""
        current_state = self.root.state()
        if current_state == 'zoomed':
            # الخروج من وضع ملء الشاشة
            self.root.state('normal')
            self.root.geometry("1400x900")
            self.center_window()
        else:
            # الدخول في وضع ملء الشاشة
            self.root.state('zoomed')

    def exit_fullscreen(self, event=None):
        """الخروج من وضع ملء الشاشة (Escape)"""
        if self.root.state() == 'zoomed':
            self.root.state('normal')
            self.root.geometry("1400x900")
            self.center_window()
        
    def create_fullscreen_interface(self):
        """إنشاء الواجهة بملء الشاشة"""
        # الإطار الرئيسي
        self.main_frame = create_simple_rtl_frame(self.root)
        self.main_frame.pack(fill='both', expand=True)

        # شريط الحالة السفلي (إنشاؤه أولاً وتثبيته)
        self.footer_frame = create_simple_rtl_frame(self.main_frame)
        self.footer_frame.configure(bg=COLORS['light'], height=80)
        self.footer_frame.pack(fill='x', side='bottom')
        self.footer_frame.pack_propagate(False)

        # شريط العنوان العلوي
        self.create_fullscreen_header()

        # المحتوى الرئيسي (سيملأ المساحة المتبقية)
        self.create_main_content()

        # إنشاء محتوى شريط الحالة
        self.create_footer_content()

        # عرض القسم الأول بعد إنشاء جميع العناصر
        self.switch_section(0)
        
    def create_fullscreen_header(self):
        """إنشاء شريط العنوان المحسن بملء الشاشة"""
        header_frame = create_simple_rtl_frame(self.main_frame)
        header_frame.configure(bg=COLORS['primary'], height=120)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # الجانب الأيمن - العنوان والوصف
        title_section = create_simple_rtl_frame(header_frame)
        title_section.configure(bg=COLORS['primary'])
        title_section.pack(side='right', padx=40, pady=15)

        # العنوان الرئيسي
        title_text = {
            'add': '🚢 إضافة شحنة جديدة',
            'edit': '✏️ تعديل بيانات الشحنة',
            'duplicate': '📋 نسخ شحنة موجودة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }

        main_title = create_simple_rtl_label(
            title_section,
            text=title_text.get(self.mode, 'نموذج الشحنة'),
            font=('Segoe UI', 32, 'bold'),
            fg='white',
            bg=COLORS['primary']
        )
        main_title.pack(anchor='e')

        # وصف إضافي
        description_text = {
            'add': 'إدخال بيانات شحنة جديدة في النظام',
            'edit': 'تعديل وتحديث بيانات الشحنة الموجودة',
            'duplicate': 'إنشاء نسخة جديدة من الشحنة المحددة',
            'view': 'عرض تفاصيل الشحنة بدون إمكانية التعديل'
        }

        description = create_simple_rtl_label(
            title_section,
            text=description_text.get(self.mode, 'إدارة بيانات الشحنة'),
            font=('Segoe UI', 14),
            fg='#E3F2FD',
            bg=COLORS['primary']
        )
        description.pack(anchor='e', pady=(5, 0))

        # الوسط - معلومات الشحنة (إذا كانت موجودة)
        if self.shipment_data and self.shipment_data.get('shipment_number'):
            center_section = create_simple_rtl_frame(header_frame)
            center_section.configure(bg=COLORS['primary'])
            center_section.pack(expand=True, pady=20)

            shipment_info = create_simple_rtl_label(
                center_section,
                text=f"رقم الشحنة: {self.shipment_data.get('shipment_number', 'غير محدد')}",
                font=('Segoe UI', 18, 'bold'),
                fg='#FFEB3B',
                bg=COLORS['primary']
            )
            shipment_info.pack(anchor='center')

        # الجانب الأيسر - أزرار التحكم السريع
        controls_section = create_simple_rtl_frame(header_frame)
        controls_section.configure(bg=COLORS['primary'])
        controls_section.pack(side='left', padx=40, pady=20)

        # زر ملء الشاشة
        fullscreen_btn = create_simple_rtl_button(
            controls_section,
            text="🔲 F11",
            command=self.toggle_fullscreen,
            bg='#1976D2',
            fg='white',
            font=('Segoe UI', 10, 'bold'),
            width=8
        )
        fullscreen_btn.pack(pady=2)

        # زر المساعدة
        help_btn = create_simple_rtl_button(
            controls_section,
            text="❓ F1",
            command=self.show_help,
            bg='#388E3C',
            fg='white',
            font=('Segoe UI', 10, 'bold'),
            width=8
        )
        help_btn.pack(pady=2)
        
        # الوصف التفصيلي
        if self.mode in ['edit', 'duplicate', 'view'] and self.shipment_data:
            subtitle = create_simple_rtl_label(
                title_section,
                text=f"رقم الشحنة: {self.shipment_data.get('shipment_number', 'غير محدد')}",
                font=('Segoe UI', 16, 'normal'),
                fg=COLORS['text_white'],
                bg=COLORS['primary']
            )
            subtitle.pack(anchor='e', pady=(10, 0))
        else:
            subtitle = create_simple_rtl_label(
                title_section,
                text="نموذج شامل ومتقدم لإدارة بيانات الشحنة",
                font=('Segoe UI', 16, 'normal'),
                fg=COLORS['text_white'],
                bg=COLORS['primary']
            )
            subtitle.pack(anchor='e', pady=(10, 0))
        
        # الجانب الأيسر - معلومات المستخدم والوقت
        info_section = create_simple_rtl_frame(header_frame)
        info_section.configure(bg=COLORS['primary'])
        info_section.pack(side='left', padx=40, pady=20)
        
        # معلومات المستخدم
        current_user = auth_manager.get_current_user()
        username = current_user.get('username', 'غير محدد') if current_user else 'مستخدم'
        user_info = tk.Label(
            info_section,
            text=f"👤 المستخدم: {username}",
            font=('Segoe UI', 14, 'normal'),
            fg='white',
            bg=COLORS['primary']
        )
        user_info.pack(anchor='w')
        
        # الوقت الحالي
        self.time_label = tk.Label(
            info_section,
            text="",
            font=('Segoe UI', 12, 'normal'),
            fg='white',
            bg=COLORS['primary']
        )
        self.time_label.pack(anchor='w', pady=(10, 0))
        self.update_time()
        
        # الوسط - أزرار التنقل السريع
        nav_section = create_simple_rtl_frame(header_frame)
        nav_section.configure(bg=COLORS['primary'])
        nav_section.pack(expand=True, pady=20)
        
        # أزرار التنقل بين الأقسام
        nav_buttons_frame = create_simple_rtl_frame(nav_section)
        nav_buttons_frame.configure(bg=COLORS['primary'])
        nav_buttons_frame.pack()
        
        self.nav_buttons = []
        nav_items = [
            ("📋 أساسية", 0),
            ("📦 الأصناف", 1),
            ("🚢 الشحن", 2),
            ("📦 الحاوية", 3),
            ("💰 المالية", 4),
            ("📄 المستندات", 5),
            ("📊 التتبع", 6),
            ("📝 الملاحظات", 7)
        ]
        
        for text, section_id in nav_items:
            btn = create_simple_rtl_button(
                nav_buttons_frame,
                text=text,
                button_type="ghost",
                command=lambda s=section_id: self.switch_section(s)
            )
            btn.configure(
                bg=COLORS['primary'],
                fg=COLORS['text_white'],
                activebackground=COLORS['primary_light'],
                activeforeground=COLORS['text_white']
            )
            btn.pack(side='right', padx=5)
            self.nav_buttons.append(btn)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي مع إمكانية التمرير"""
        # إطار المحتوى الرئيسي مع scrollbar
        content_frame = create_simple_rtl_frame(self.main_frame)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(20, 10))

        # إنشاء Canvas و Scrollbar للتمرير
        self.canvas = tk.Canvas(content_frame, bg=COLORS['background'], highlightthickness=0)
        scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = create_simple_rtl_frame(self.canvas)

        # ربط الأحداث
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        # إنشاء نافذة في Canvas
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        # تكوين Canvas
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط العناصر
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط أحداث عجلة الماوس
        self.bind_mousewheel()

        # إنشاء notebook مخصص للأقسام
        self.create_sections_notebook(self.scrollable_frame)

    def bind_mousewheel(self):
        """ربط أحداث عجلة الماوس للتمرير"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            self.canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            self.canvas.unbind_all("<MouseWheel>")

        self.canvas.bind('<Enter>', _bind_to_mousewheel)
        self.canvas.bind('<Leave>', _unbind_from_mousewheel)

        # ربط تغيير حجم Canvas
        self.canvas.bind('<Configure>', self._on_canvas_configure)

    def _on_canvas_configure(self, event):
        """تحديث عرض الإطار القابل للتمرير عند تغيير حجم Canvas"""
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)

    def create_sections_notebook(self, parent):
        """إنشاء دفتر الأقسام"""
        # إطار الأقسام
        self.sections_frame = create_simple_rtl_frame(parent)
        self.sections_frame.pack(fill='both', expand=True)
        
        # إنشاء الأقسام
        self.sections = []
        
        # قسم المعلومات الأساسية
        basic_section = self.create_basic_info_section()
        self.sections.append(basic_section)

        # قسم الأصناف
        items_section = self.create_items_section()
        self.sections.append(items_section)

        # قسم معلومات الشحن
        shipping_section = self.create_shipping_info_section()
        self.sections.append(shipping_section)
        
        # قسم معلومات الحاوية
        container_section = self.create_container_info_section()
        self.sections.append(container_section)
        
        # قسم المعلومات المالية
        financial_section = self.create_financial_info_section()
        self.sections.append(financial_section)

        # قسم المستندات
        documents_section = self.create_documents_section()
        self.sections.append(documents_section)

        # قسم التتبع والحالة
        tracking_section = self.create_tracking_section()
        self.sections.append(tracking_section)

        # قسم الملاحظات والمرفقات
        notes_section = self.create_notes_section()
        self.sections.append(notes_section)

        # عرض القسم الأول (سيتم تأجيله حتى بعد إنشاء جميع العناصر)
        # self.switch_section(0)
        
    def create_basic_info_section(self):
        """إنشاء قسم المعلومات الأساسية"""
        section = create_simple_rtl_frame(self.sections_frame)
        
        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))
        
        title_label = create_simple_rtl_label(
            title_frame,
            text="📋 المعلومات الأساسية للشحنة",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')
        
        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="البيانات الأساسية المطلوبة لتسجيل الشحنة في النظام",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))
        
        # محتوى القسم في شبكة
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)
        
        # الصف الأول
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))
        
        # رقم الشحنة
        self.create_field_group(
            row1, "🔢 رقم الشحنة *", 'shipment_number',
            placeholder="SH-XXXXXX", required=True, width_ratio=1/3
        )

        # المورد
        self.create_supplier_field(row1, width_ratio=1/3)

        # رقم فاتورة المورد
        self.create_field_group(
            row1, "🧾 رقم فاتورة المورد", 'supplier_invoice_number',
            placeholder="INV-XXXXXX", width_ratio=1/3
        )
        
        # الصف الثاني
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # الحالة
        self.create_status_field(row2, width_ratio=1/3)

        # حالة الإفراج
        self.create_combobox_field(
            row2, "🔓 حالة الإفراج", 'release_status',
            values=['بدون إفراج', 'مع الإفراج'], width_ratio=1/3
        )

        # الصف الثالث
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 30))

        # الأولوية (محسوبة تلقائياً)
        self.create_priority_display(row3, width_ratio=1/3)
        
        return section

    def create_items_section(self):
        """إنشاء قسم الأصناف"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📦 الأصناف والمنتجات",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="تفاصيل الأصناف والمنتجات المشحونة مع الكميات والأسعار",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # شريط أدوات الأصناف
        toolbar_frame = create_simple_rtl_frame(content_frame)
        toolbar_frame.pack(fill='x', pady=(0, 20))

        # أزرار إدارة الأصناف
        add_item_btn = create_simple_rtl_button(
            toolbar_frame,
            text="➕ إضافة صنف",
            button_type="success",
            command=self.add_item
        )
        add_item_btn.pack(side='right', padx=5)

        edit_item_btn = create_simple_rtl_button(
            toolbar_frame,
            text="✏️ تعديل صنف",
            button_type="warning",
            command=self.edit_item
        )
        edit_item_btn.pack(side='right', padx=5)

        delete_item_btn = create_simple_rtl_button(
            toolbar_frame,
            text="🗑️ حذف صنف",
            button_type="danger",
            command=self.delete_item
        )
        delete_item_btn.pack(side='right', padx=5)

        duplicate_item_btn = create_simple_rtl_button(
            toolbar_frame,
            text="📋 نسخ صنف",
            button_type="secondary",
            command=self.duplicate_item
        )
        duplicate_item_btn.pack(side='right', padx=5)

        # تلميح البحث
        search_hint_label = create_simple_rtl_label(
            toolbar_frame,
            text="💡 اضغط F9 للبحث في الأصناف",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        search_hint_label.pack(side='left', padx=20)

        # جدول الأصناف
        table_frame = create_simple_rtl_frame(content_frame)
        table_frame.pack(fill='both', expand=True, pady=(0, 20))

        # تعريف أعمدة جدول الأصناف
        items_columns = [
            ('item_code', 'كود الصنف', 100),
            ('item_name', 'اسم الصنف', 200),
            ('description', 'الوصف', 150),
            ('quantity', 'الكمية', 80),
            ('unit', 'الوحدة', 80),
            ('unit_price', 'سعر الوحدة', 100),
            ('total_price', 'الإجمالي', 100),
            ('weight', 'الوزن (كجم)', 100),
            ('volume', 'الحجم (م³)', 100),
            ('hs_code', 'كود HS', 100),
            ('origin_country', 'بلد المنشأ', 120)
        ]

        # إنشاء Treeview للأصناف
        self.items_tree = create_simple_rtl_treeview(
            table_frame,
            columns=[col[0] for col in items_columns],
            show='tree headings',
            height=12
        )

        # تكوين أعمدة الأصناف
        for col_id, col_name, col_width in items_columns:
            self.items_tree.heading(col_id, text=col_name, anchor='e')
            self.items_tree.column(col_id, width=col_width, anchor='e', minwidth=50)

        # تكوين العمود الرئيسي
        self.items_tree.column('#0', width=30, minwidth=30, anchor='e')
        self.items_tree.heading('#0', text='#', anchor='e')

        # أشرطة التمرير للجدول
        items_v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_v_scrollbar.set)

        items_h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.items_tree.xview)
        self.items_tree.configure(xscrollcommand=items_h_scrollbar.set)

        # تخطيط جدول الأصناف
        self.items_tree.grid(row=0, column=0, sticky='nsew')
        items_v_scrollbar.grid(row=0, column=1, sticky='ns')
        items_h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط أحداث الجدول
        self.items_tree.bind('<<TreeviewSelect>>', self.on_item_select)
        self.items_tree.bind('<Double-1>', self.on_item_double_click)
        self.items_tree.bind('<Button-3>', self.show_items_context_menu)
        self.items_tree.bind('<F9>', self.show_item_search)

        # ربط F9 على مستوى النافذة أيضاً
        self.root.bind('<F9>', self.show_item_search)

        # إعداد ألوان جدول الأصناف
        self.setup_items_tree_colors()

        # منطقة ملخص الأصناف
        summary_frame = create_simple_rtl_frame(content_frame)
        summary_frame.pack(fill='x', pady=(20, 0))

        # عنوان الملخص
        summary_title = create_simple_rtl_label(
            summary_frame,
            text="📊 ملخص الأصناف:",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        summary_title.pack(anchor='e', pady=(0, 10))

        # إطار الملخص
        summary_content = create_simple_rtl_frame(summary_frame)
        summary_content.pack(fill='x')

        # الصف الأول من الملخص
        summary_row1 = create_simple_rtl_frame(summary_content)
        summary_row1.pack(fill='x', pady=(0, 10))

        # إجمالي الأصناف
        self.total_items_label = create_simple_rtl_label(
            summary_row1,
            text="إجمالي الأصناف: 0",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_items_label.pack(side='right', padx=20)

        # إجمالي الكمية
        self.total_quantity_label = create_simple_rtl_label(
            summary_row1,
            text="إجمالي الكمية: 0",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_quantity_label.pack(side='right', padx=20)

        # إجمالي الوزن
        self.total_weight_label = create_simple_rtl_label(
            summary_row1,
            text="إجمالي الوزن: 0.00 كجم",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_weight_label.pack(side='right', padx=20)

        # الصف الثاني من الملخص
        summary_row2 = create_simple_rtl_frame(summary_content)
        summary_row2.pack(fill='x')

        # إجمالي الحجم
        self.total_volume_label = create_simple_rtl_label(
            summary_row2,
            text="إجمالي الحجم: 0.00 م³",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_volume_label.pack(side='right', padx=20)

        # إجمالي القيمة
        self.total_value_label = create_simple_rtl_label(
            summary_row2,
            text="إجمالي القيمة: 0.00",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['success']
        )
        self.total_value_label.pack(side='right', padx=20)

        # متوسط سعر الوحدة
        self.avg_price_label = create_simple_rtl_label(
            summary_row2,
            text="متوسط السعر: 0.00",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.avg_price_label.pack(side='right', padx=20)

        # قائمة الأصناف (لحفظ البيانات)
        self.items_data = []
        self.selected_item = None

        # تحديث الملخص
        self.update_items_summary()

        return section

    def setup_items_tree_colors(self):
        """إعداد ألوان جدول الأصناف"""
        try:
            # ألوان الصفوف المتناوبة
            self.items_tree.tag_configure('odd_row', background='#F9FAFB')
            self.items_tree.tag_configure('even_row', background='#FFFFFF')

            # ألوان خاصة
            self.items_tree.tag_configure('high_value', background='#FEF3C7')  # قيمة عالية
            self.items_tree.tag_configure('low_stock', background='#FECACA')   # مخزون منخفض
            self.items_tree.tag_configure('selected', background='#DBEAFE')    # محدد
        except Exception as e:
            print(f"خطأ في إعداد ألوان جدول الأصناف: {e}")

    def create_shipping_info_section(self):
        """إنشاء قسم معلومات الشحن"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="🚢 معلومات الشحن والنقل",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="تفاصيل الموانئ وشركات الشحن ومعلومات الرحلة",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - الموانئ
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # ميناء المغادرة
        self.create_combobox_field(
            row1, "🚢 ميناء المغادرة", 'departure_port',
            values=list(PORTS.keys()), width_ratio=1/2
        )

        # ميناء الوصول
        self.create_combobox_field(
            row1, "🏁 ميناء الوصول", 'arrival_port',
            values=list(PORTS.keys()), width_ratio=1/2
        )

        # الصف الثاني - تواريخ الشحن الأساسية
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # تاريخ الشحن
        self.create_date_field(
            row2, "📅 تاريخ الشحن *", 'shipment_date',
            placeholder="YYYY-MM-DD", required=True, width_ratio=1/3
        )

        # تاريخ الوصول المتوقع
        self.create_date_field(
            row2, "🏁 تاريخ الوصول المتوقع", 'expected_arrival_date',
            placeholder="YYYY-MM-DD", width_ratio=1/3
        )

        # شركة الشحن
        self.create_combobox_field(
            row2, "🏢 شركة الشحن", 'shipping_company',
            values=list(SHIPPING_COMPANIES.keys()), width_ratio=1/3
        )

        # الصف الثالث - التواريخ الفعلية
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 30))

        # تاريخ المغادرة الفعلي
        self.create_date_field(
            row3, "📅 تاريخ المغادرة الفعلي", 'actual_departure_date',
            placeholder="YYYY-MM-DD", width_ratio=1/3
        )

        # تاريخ الوصول الفعلي
        self.create_date_field(
            row3, "🏁 تاريخ الوصول الفعلي", 'actual_arrival_date',
            placeholder="YYYY-MM-DD", width_ratio=1/3
        )

        # الصف الرابع - بوليصة الشحن ومعلومات إضافية
        row4 = create_simple_rtl_frame(content_frame)
        row4.pack(fill='x', pady=(30, 0))

        # رقم بوليصة الشحن
        self.create_field_group(
            row4, "📋 رقم بوليصة الشحن", 'bill_of_lading',
            placeholder="BOL-XXXXXX", width_ratio=1/2
        )

        # رقم التتبع
        self.create_field_group(
            row4, "🔍 رقم التتبع", 'tracking_number',
            placeholder="TRK-XXXXXX", width_ratio=1/2
        )

        return section

    def create_container_info_section(self):
        """إنشاء قسم معلومات الحاوية"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📦 معلومات الحاوية والبضائع",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="تفاصيل الحاوية والأوزان والأحجام والمحتويات",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - عدد الحاويات ونوع الحاوية
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # عدد الحاويات
        self.create_container_count_field(row1)

        # نوع الحاوية
        self.create_combobox_field(
            row1, "📏 نوع الحاوية", 'container_type',
            values=list(CONTAINER_TYPES.keys()), width_ratio=1/3
        )

        # رقم الختم
        self.create_field_group(
            row1, "🔒 رقم الختم", 'seal_number',
            placeholder="SEAL-XXXXXX", width_ratio=1/3
        )

        # إطار أرقام الحاويات الديناميكي
        self.containers_frame = create_simple_rtl_frame(content_frame)
        self.containers_frame.pack(fill='x', pady=(0, 30))

        # قائمة لحفظ حقول أرقام الحاويات
        self.container_number_vars = []
        self.container_number_widgets = []

        # إنشاء حقل واحد افتراضياً
        self.update_container_fields(1)

        # الصف الثاني - الأوزان والأحجام
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # الوزن الإجمالي
        self.create_field_group(
            row2, "⚖️ الوزن الإجمالي (كجم)", 'weight',
            placeholder="0.00", width_ratio=1/4
        )

        # الحجم
        self.create_field_group(
            row2, "📐 الحجم (م³)", 'volume',
            placeholder="0.00", width_ratio=1/4
        )

        # عدد القطع
        self.create_field_group(
            row2, "📊 عدد القطع", 'pieces_count',
            placeholder="0", width_ratio=1/4
        )

        # الوزن الصافي
        self.create_field_group(
            row2, "⚖️ الوزن الصافي (كجم)", 'net_weight',
            placeholder="0.00", width_ratio=1/4
        )

        # الصف الثالث - وصف البضائع
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 0))

        # وصف البضائع
        desc_label = create_simple_rtl_label(
            row3,
            text="📝 وصف البضائع:",
            font=('Segoe UI', 14, 'bold')
        )
        desc_label.pack(anchor='e', pady=(0, 10))

        self.goods_description_text = create_simple_rtl_text(
            row3,
            height=6
        )
        self.goods_description_text.pack(fill='x', pady=(0, 20))

        return section

    def create_container_count_field(self, parent):
        """إنشاء حقل عدد الحاويات"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', padx=(0, 30))

        # تسمية الحقل
        label = create_simple_rtl_label(
            field_frame,
            text="🔢 عدد الحاويات",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        label.pack(anchor='e', pady=(0, 10))

        # إطار الحقل
        entry_frame = create_simple_rtl_frame(field_frame)
        entry_frame.pack(fill='x')

        # متغير عدد الحاويات
        self.form_vars['container_count'] = tk.StringVar()
        self.form_vars['container_count'].set('1')  # القيمة الافتراضية

        # حقل الإدخال
        container_count_entry = create_simple_rtl_entry(
            entry_frame,
            textvariable=self.form_vars['container_count'],
            font=('Segoe UI', 12),
            width=10,
            justify='center'
        )
        container_count_entry.pack(anchor='e')

        # ربط تغيير القيمة بتحديث حقول أرقام الحاويات
        self.form_vars['container_count'].trace('w', self.on_container_count_change)

        # تحديد عرض الحقل
        field_frame.configure(width=int(parent.winfo_reqwidth() * (1/3)))

    def on_container_count_change(self, *args):
        """معالج تغيير عدد الحاويات"""
        try:
            count = int(self.form_vars['container_count'].get() or 1)
            if count < 1:
                count = 1
                self.form_vars['container_count'].set('1')
            elif count > 10:  # حد أقصى 10 حاويات
                count = 10
                self.form_vars['container_count'].set('10')

            self.update_container_fields(count)
        except ValueError:
            # في حالة إدخال قيمة غير صحيحة، استخدم 1
            self.form_vars['container_count'].set('1')
            self.update_container_fields(1)

    def update_container_fields(self, count):
        """تحديث حقول أرقام الحاويات حسب العدد"""
        # مسح الحقول الموجودة
        for widget in self.container_number_widgets:
            widget.destroy()
        self.container_number_widgets.clear()
        self.container_number_vars.clear()

        # إنشاء الحقول الجديدة
        for i in range(count):
            self.create_single_container_field(i + 1)

    def create_single_container_field(self, container_num):
        """إنشاء حقل واحد لرقم الحاوية"""
        # إطار الحقل
        field_frame = create_simple_rtl_frame(self.containers_frame)
        field_frame.pack(fill='x', pady=(10, 0))

        # تسمية الحقل
        label_text = f"📦 رقم الحاوية {container_num}" if container_num > 1 else "📦 رقم الحاوية"
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        label.pack(anchor='e', pady=(0, 10))

        # إطار الإدخال
        entry_frame = create_simple_rtl_frame(field_frame)
        entry_frame.pack(fill='x')

        # متغير الحقل
        var_name = f'container_number_{container_num}' if container_num > 1 else 'container_number'
        container_var = tk.StringVar()
        self.form_vars[var_name] = container_var
        self.container_number_vars.append(container_var)

        # حقل الإدخال
        container_entry = create_simple_rtl_entry(
            entry_frame,
            textvariable=container_var,
            font=('Segoe UI', 12),
            width=30
        )
        container_entry.pack(anchor='e', padx=(0, 50))

        # إضافة placeholder
        container_entry.insert(0, "XXXX-XXXXXXX-X")
        container_entry.bind('<FocusIn>', lambda e: self.clear_placeholder(e, "XXXX-XXXXXXX-X"))
        container_entry.bind('<FocusOut>', lambda e: self.restore_placeholder(e, "XXXX-XXXXXXX-X"))

        # حفظ المرجع للحذف لاحقاً
        self.container_number_widgets.append(field_frame)

    def create_financial_info_section(self):
        """إنشاء قسم المعلومات المالية"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="💰 المعلومات المالية والتكاليف",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="القيم المالية وتكاليف الشحن وطرق الدفع",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - العملة والقيم الأساسية
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # العملة
        self.create_combobox_field(
            row1, "💱 العملة", 'currency',
            values=list(CURRENCIES.keys()), width_ratio=1/4
        )

        # القيمة الإجمالية
        self.create_field_group(
            row1, "💰 القيمة الإجمالية", 'total_value',
            placeholder="0.00", width_ratio=1/4
        )

        # تكلفة الشحن
        self.create_field_group(
            row1, "🚢 تكلفة الشحن", 'shipping_cost',
            placeholder="0.00", width_ratio=1/4
        )

        # رسوم التأمين
        self.create_field_group(
            row1, "🛡️ رسوم التأمين", 'insurance_cost',
            placeholder="0.00", width_ratio=1/4
        )

        # الصف الثاني - التكاليف الإضافية
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # رسوم إضافية
        self.create_field_group(
            row2, "💸 رسوم إضافية", 'additional_fees',
            placeholder="0.00", width_ratio=1/4
        )

        # رسوم الجمارك
        self.create_field_group(
            row2, "🏛️ رسوم الجمارك", 'customs_fees',
            placeholder="0.00", width_ratio=1/4
        )

        # رسوم المناولة
        self.create_field_group(
            row2, "🔧 رسوم المناولة", 'handling_fees',
            placeholder="0.00", width_ratio=1/4
        )

        # إجمالي التكلفة (محسوب تلقائياً)
        self.create_calculated_field(
            row2, "💯 إجمالي التكلفة", 'total_cost', width_ratio=1/4
        )

        # الصف الثالث - معلومات الدفع
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 0))

        # طريقة الدفع
        self.create_combobox_field(
            row3, "💳 طريقة الدفع", 'payment_method',
            values=list(PAYMENT_METHODS.keys()), width_ratio=1/3
        )

        # حالة الدفع
        self.create_combobox_field(
            row3, "💰 حالة الدفع", 'payment_status',
            values=['لم يتم الدفع', 'دفع جزئي', 'تم الدفع بالكامل', 'مسترد'], width_ratio=1/3
        )

        # المبلغ المدفوع
        self.create_field_group(
            row3, "💵 المبلغ المدفوع", 'paid_amount',
            placeholder="0.00", width_ratio=1/3
        )

        return section

    def create_documents_section(self):
        """إنشاء قسم المستندات"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📄 المستندات والوثائق الرسمية",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="جميع المستندات والوثائق المطلوبة للشحنة والجمارك",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - المستندات الأساسية
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # فاتورة تجارية
        self.create_document_link_field(
            row1, "📋 الفاتورة التجارية", 'commercial_invoice',
            "رابط الفاتورة التجارية", width_ratio=1/3
        )

        # قائمة التعبئة
        self.create_document_link_field(
            row1, "📦 قائمة التعبئة", 'packing_list',
            "رابط قائمة التعبئة", width_ratio=1/3
        )

        # شهادة المنشأ
        self.create_document_link_field(
            row1, "🏭 شهادة المنشأ", 'certificate_of_origin',
            "رابط شهادة المنشأ", width_ratio=1/3
        )

        # الصف الثاني - مستندات التأمين والجودة
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # بوليصة التأمين
        self.create_document_link_field(
            row2, "🛡️ بوليصة التأمين", 'insurance_policy',
            "رابط بوليصة التأمين", width_ratio=1/3
        )

        # شهادة الجودة
        self.create_document_link_field(
            row2, "✅ شهادة الجودة", 'quality_certificate',
            "رابط شهادة الجودة", width_ratio=1/3
        )

        # شهادة الصحة
        self.create_document_link_field(
            row2, "🏥 شهادة الصحة", 'health_certificate',
            "رابط شهادة الصحة", width_ratio=1/3
        )

        # الصف الثالث - مستندات الجمارك
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 30))

        # صور الأصناف
        self.create_document_link_field(
            row3, "📸 صور الأصناف", 'customs_declaration',
            "رابط صور الأصناف", width_ratio=1/3
        )

        # رخصة الاستيراد
        self.create_document_link_field(
            row3, "📜 رخصة الاستيراد", 'import_license',
            "رابط رخصة الاستيراد", width_ratio=1/3
        )

        # شهادة التفتيش
        self.create_document_link_field(
            row3, "🔍 شهادة التفتيش", 'inspection_certificate',
            "رابط شهادة التفتيش", width_ratio=1/3
        )

        # الصف الرابع - مستندات إضافية
        row4 = create_simple_rtl_frame(content_frame)
        row4.pack(fill='x', pady=(30, 30))

        # مستندات أخرى
        self.create_document_link_field(
            row4, "📋 مستندات أخرى", 'other_documents',
            "روابط المستندات الأخرى", width_ratio=1/2
        )

        # حالة المستندات
        self.create_documents_status_field(row4, width_ratio=1/2)

        # الصف الخامس - ملاحظات المستندات
        row5 = create_simple_rtl_frame(content_frame)
        row5.pack(fill='x', pady=(30, 0))

        # ملاحظات المستندات
        docs_notes_label = create_simple_rtl_label(
            row5,
            text="📝 ملاحظات حول المستندات:",
            font=('Segoe UI', 14, 'bold')
        )
        docs_notes_label.pack(anchor='e', pady=(0, 10))

        self.documents_notes_text = create_simple_rtl_text(
            row5,
            height=4
        )
        self.documents_notes_text.pack(fill='x', pady=(0, 20))

        # منطقة حالة المستندات
        status_frame = create_simple_rtl_frame(row5)
        status_frame.pack(fill='x', pady=(20, 0))

        # مؤشر اكتمال المستندات
        completion_label = create_simple_rtl_label(
            status_frame,
            text="📊 نسبة اكتمال المستندات:",
            font=('Segoe UI', 14, 'bold')
        )
        completion_label.pack(anchor='e', pady=(0, 10))

        # شريط تقدم المستندات
        docs_progress_frame = create_simple_rtl_frame(status_frame)
        docs_progress_frame.pack(fill='x', pady=(0, 10))

        try:
            self.documents_progress_bar = ttk.Progressbar(
                docs_progress_frame,
                mode='determinate',
                length=400,
                value=0
            )
        except Exception as e:
            self.documents_progress_bar = ttk.Progressbar(
                docs_progress_frame,
                mode='determinate',
                value=0
            )
            print(f"تحذير في Documents Progressbar: {e}")

        self.documents_progress_bar.pack(anchor='e', pady=(0, 10))

        self.documents_progress_label = create_simple_rtl_label(
            docs_progress_frame,
            text="0% مكتمل",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['primary']
        )
        self.documents_progress_label.pack(anchor='e')

        return section

    def create_document_field(self, parent, label_text, var_name, placeholder="", width_ratio=1/3):
        """إنشاء حقل مستند مع تسمية وحالة وزر مرفق"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # إطار التسمية مع زر المرفق
        header_frame = create_simple_rtl_frame(field_frame)
        header_frame.pack(fill='x', pady=(0, 10))

        # زر إضافة مرفق
        print(f"🔧 إنشاء زر المرفق لـ: {label_text} ({var_name})")

        attachment_btn = create_simple_rtl_button(
            header_frame,
            text="📎 إضافة مرفق",
            button_type="secondary",
            command=lambda v=var_name, l=label_text: self.add_attachment(v, l)
        )
        attachment_btn.configure(
            font=('Segoe UI', 9, 'normal'),
            padx=8,
            pady=4
        )
        attachment_btn.pack(side='left', padx=(0, 10))

        print(f"✅ تم إنشاء زر المرفق بنجاح لـ: {label_text}")

        # التسمية
        label = create_simple_rtl_label(
            header_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(side='right', anchor='e')

        # إطار الحقل والحالة
        input_frame = create_simple_rtl_frame(field_frame)
        input_frame.pack(fill='x', pady=(0, 10))

        # الحقل
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            input_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 12, 'normal'))
        entry.pack(fill='x', ipady=8)

        # مؤشر حالة المستند
        status_var_name = f"{var_name}_status"
        self.form_vars[status_var_name] = tk.StringVar()
        status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[status_var_name]
        )
        status_combo['values'] = ['غير متوفر', 'متوفر', 'مرسل', 'مؤكد', 'مرفوض']
        status_combo.set('غير متوفر')
        status_combo.configure(font=('Segoe UI', 10, 'normal'))
        status_combo.pack(fill='x', ipady=6)

        # إطار عرض المرفقات
        attachments_frame = create_simple_rtl_frame(field_frame)
        attachments_frame.pack(fill='x', pady=(5, 0))

        # تسمية المرفقات (مخفية في البداية)
        attachments_label = create_simple_rtl_label(
            attachments_frame,
            text="",
            font=('Segoe UI', 9, 'italic'),
            fg='#6b7280'
        )
        attachments_label.pack(anchor='e')

        # حفظ مرجع إطار المرفقات
        setattr(self, f'{var_name}_attachments_frame', attachments_frame)
        setattr(self, f'{var_name}_attachments_label', attachments_label)

        # تهيئة قائمة المرفقات
        if not hasattr(self, 'document_attachments'):
            self.document_attachments = {}
        self.document_attachments[var_name] = []

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_document_change)
        self.form_vars[status_var_name].trace('w', self.on_document_change)

        return entry

    def create_document_link_field(self, parent, label_text, var_name, placeholder="", width_ratio=1/3):
        """إنشاء حقل رابط مستند مع تسمية وحالة وزر إدخال الرابط"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # إطار التسمية مع أزرار الإجراءات
        header_frame = create_simple_rtl_frame(field_frame)
        header_frame.pack(fill='x', pady=(0, 10))

        # زر فتح الرابط
        open_link_btn = create_simple_rtl_button(
            header_frame,
            text="🔗 فتح الرابط",
            button_type="primary",
            command=lambda v=var_name, l=label_text: self.open_document_link(v, l)
        )
        open_link_btn.configure(
            font=('Segoe UI', 9, 'normal'),
            padx=8,
            pady=4
        )
        open_link_btn.pack(side='left', padx=(0, 5))

        # زر إدخال الرابط
        add_link_btn = create_simple_rtl_button(
            header_frame,
            text="📝 إدخال رابط",
            button_type="secondary",
            command=lambda v=var_name, l=label_text: self.add_document_link(v, l)
        )
        add_link_btn.configure(
            font=('Segoe UI', 9, 'normal'),
            padx=8,
            pady=4
        )
        add_link_btn.pack(side='left', padx=(0, 10))

        print(f"✅ تم إنشاء أزرار الرابط بنجاح لـ: {label_text}")

        # التسمية
        label = create_simple_rtl_label(
            header_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(side='right', anchor='e')

        # إطار الحقل والحالة
        input_frame = create_simple_rtl_frame(field_frame)
        input_frame.pack(fill='x', pady=(0, 10))

        # حقل الرابط (للقراءة فقط)
        self.form_vars[var_name] = tk.StringVar()
        link_entry = create_simple_rtl_entry(
            input_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder or f"🔗 اضغط 'إدخال رابط' لإضافة رابط {label_text}"
        )
        link_entry.configure(
            font=('Segoe UI', 12, 'normal'),
            fg='#2563eb',  # لون أزرق للروابط
            cursor='hand2',  # مؤشر اليد
            state='readonly',  # للقراءة فقط - لا يسمح بالإدخال اليدوي
            readonlybackground='#f8fafc',  # خلفية فاتحة للحقول readonly
            relief='flat',  # حدود مسطحة
            borderwidth=1
        )
        link_entry.pack(fill='x', ipady=8)

        # ربط النقر على الحقل لفتح الرابط
        link_entry.bind('<Button-1>', lambda e, v=var_name, l=label_text: self.open_document_link(v, l))

        # حفظ مرجع للحقل لتحديثه لاحقاً
        if not hasattr(self, 'link_entries'):
            self.link_entries = {}
        self.link_entries[var_name] = link_entry
        link_entry.bind('<Return>', lambda e, v=var_name, l=label_text: self.open_document_link(v, l))

        # مؤشر حالة المستند
        status_var_name = f"{var_name}_status"
        self.form_vars[status_var_name] = tk.StringVar()
        status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[status_var_name]
        )
        status_combo['values'] = ['غير متوفر', 'متوفر', 'مرسل', 'مؤكد', 'مرفوض']
        status_combo.set('غير متوفر')
        status_combo.configure(font=('Segoe UI', 10, 'normal'))
        status_combo.pack(fill='x', ipady=6)

        # إطار معلومات الرابط
        link_info_frame = create_simple_rtl_frame(field_frame)
        link_info_frame.pack(fill='x', pady=(5, 0))

        # تسمية معلومات الرابط (مخفية في البداية)
        link_info_label = create_simple_rtl_label(
            link_info_frame,
            text="",
            font=('Segoe UI', 9, 'italic'),
            fg='#6b7280'
        )
        link_info_label.pack(anchor='e')

        # حفظ مرجع إطار معلومات الرابط
        setattr(self, f'{var_name}_link_info_frame', link_info_frame)
        setattr(self, f'{var_name}_link_info_label', link_info_label)

        # تهيئة قاموس الروابط
        if not hasattr(self, 'document_links'):
            self.document_links = {}
        self.document_links[var_name] = ""

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_document_change)
        self.form_vars[status_var_name].trace('w', self.on_document_change)

        return link_entry

    def add_attachment(self, var_name, document_name):
        """إضافة مرفق لمستند معين"""
        try:
            print(f"🔍 محاولة إضافة مرفق لـ: {document_name} ({var_name})")

            # التأكد من وجود قاموس المرفقات
            if not hasattr(self, 'document_attachments'):
                self.document_attachments = {}
            if var_name not in self.document_attachments:
                self.document_attachments[var_name] = []

            # فتح نافذة اختيار الملف
            file_types = [
                ("جميع الملفات المدعومة", "*.pdf *.jpg *.jpeg *.png *.doc *.docx *.xls *.xlsx"),
                ("ملفات PDF", "*.pdf"),
                ("ملفات الصور", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("ملفات Word", "*.doc *.docx"),
                ("ملفات Excel", "*.xls *.xlsx"),
                ("جميع الملفات", "*.*")
            ]

            print("📂 فتح نافذة اختيار الملف...")
            file_path = filedialog.askopenfilename(
                title=f"اختيار مرفق لـ {document_name}",
                filetypes=file_types,
                initialdir=os.getcwd()
            )

            print(f"📁 الملف المختار: {file_path}")

            if file_path and file_path.strip():
                print("✅ تم اختيار ملف، بدء المعالجة...")

                # إضافة الملف لقائمة المرفقات
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)

                print(f"📄 اسم الملف: {file_name}")
                print(f"📏 حجم الملف: {file_size} بايت")

                # تحويل حجم الملف لوحدة مناسبة
                if file_size < 1024:
                    size_str = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                attachment_info = {
                    'path': file_path,
                    'name': file_name,
                    'size': file_size,
                    'size_str': size_str
                }

                # إضافة للقائمة
                self.document_attachments[var_name].append(attachment_info)
                print(f"📎 تم إضافة المرفق للقائمة. العدد الحالي: {len(self.document_attachments[var_name])}")

                # تحديث عرض المرفقات
                self.update_attachments_display(var_name)

                # إظهار رسالة نجاح
                messagebox.showinfo(
                    "تم إضافة المرفق",
                    f"تم إضافة الملف '{file_name}' بنجاح\nالحجم: {size_str}"
                )
                print("✅ تم إضافة المرفق بنجاح!")

            else:
                print("❌ لم يتم اختيار ملف أو تم إلغاء العملية")

        except Exception as e:
            print(f"❌ خطأ في إضافة المرفق: {str(e)}")
            import traceback
            traceback.print_exc()

            messagebox.showerror(
                "خطأ في إضافة المرفق",
                f"حدث خطأ أثناء إضافة المرفق:\n{str(e)}\n\nتأكد من:\n• وجود الملف\n• صلاحيات القراءة\n• مساحة كافية"
            )

    def update_attachments_display(self, var_name):
        """تحديث عرض المرفقات لمستند معين"""
        try:
            print(f"🔄 تحديث عرض المرفقات لـ: {var_name}")

            attachments = self.document_attachments.get(var_name, [])
            attachments_label = getattr(self, f'{var_name}_attachments_label', None)

            print(f"📊 عدد المرفقات: {len(attachments)}")
            print(f"🏷️ تسمية العرض موجودة: {attachments_label is not None}")

            if attachments_label:
                if attachments:
                    # عرض عدد المرفقات
                    count = len(attachments)
                    if count == 1:
                        text = f"📎 مرفق واحد: {attachments[0]['name']}"
                    else:
                        text = f"📎 {count} مرفقات"

                    print(f"📝 النص المعروض: {text}")
                    attachments_label.configure(text=text)

                    # إضافة tooltip مع تفاصيل المرفقات
                    tooltip_text = "\n".join([
                        f"• {att['name']} ({att['size_str']})"
                        for att in attachments
                    ])

                    # ربط حدث النقر لعرض التفاصيل
                    attachments_label.bind(
                        "<Button-1>",
                        lambda e: self.show_attachments_details(var_name)
                    )
                    attachments_label.configure(cursor="hand2")

                else:
                    attachments_label.configure(text="")
                    attachments_label.unbind("<Button-1>")
                    attachments_label.configure(cursor="")

        except Exception as e:
            print(f"خطأ في تحديث عرض المرفقات: {e}")

    def show_attachments_details(self, var_name):
        """عرض تفاصيل المرفقات لمستند معين"""
        try:
            attachments = self.document_attachments.get(var_name, [])
            if not attachments:
                return

            from tkinter import messagebox

            # إنشاء نص التفاصيل
            details = f"المرفقات ({len(attachments)}):\n\n"
            for i, att in enumerate(attachments, 1):
                details += f"{i}. {att['name']}\n"
                details += f"   الحجم: {att['size_str']}\n"
                details += f"   المسار: {att['path']}\n\n"

            details += "\n💡 يمكنك فتح الملفات من مجلد المشروع"

            messagebox.showinfo("تفاصيل المرفقات", details)

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("خطأ", f"خطأ في عرض تفاصيل المرفقات: {str(e)}")

    def create_documents_status_field(self, parent, width_ratio=1/2):
        """إنشاء حقل حالة المستندات العامة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="📊 حالة المستندات العامة",
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars['documents_status'] = tk.StringVar()
        status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars['documents_status']
        )
        status_combo['values'] = [
            'غير مكتملة',
            'قيد المراجعة',
            'مكتملة جزئياً',
            'مكتملة',
            'مؤكدة',
            'مرفوضة'
        ]
        status_combo.set('غير مكتملة')
        status_combo.configure(font=('Segoe UI', 12, 'normal'))
        status_combo.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars['documents_status'].trace('w', self.on_document_change)

        return status_combo

    def create_tracking_section(self):
        """إنشاء قسم التتبع والحالة"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📊 التتبع والحالة المتقدمة",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="معلومات التتبع والموقع الحالي ونسبة التقدم",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - معلومات التتبع
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # الموقع الحالي
        self.create_field_group(
            row1, "📍 الموقع الحالي", 'current_location',
            placeholder="الموقع الحالي للشحنة", width_ratio=1/2
        )

        # آخر تحديث
        self.create_datetime_display(row1, width_ratio=1/2)

        # الصف الثاني - شريط التقدم
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # شريط التقدم
        progress_label = create_simple_rtl_label(
            row2,
            text="📊 نسبة التقدم:",
            font=('Segoe UI', 14, 'bold')
        )
        progress_label.pack(anchor='e', pady=(0, 10))

        progress_frame = create_simple_rtl_frame(row2)
        progress_frame.pack(fill='x', pady=(0, 20))

        try:
            self.progress_bar = ttk.Progressbar(
                progress_frame,
                mode='determinate',
                length=600,
                value=0
            )
        except Exception as e:
            # في حالة الخطأ، إنشاء شريط تقدم بسيط
            self.progress_bar = ttk.Progressbar(
                progress_frame,
                mode='determinate',
                value=0
            )
            print(f"تحذير في Progressbar: {e}")
        self.progress_bar.pack(anchor='e', pady=(0, 10))

        self.progress_label = create_simple_rtl_label(
            progress_frame,
            text="0%",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        self.progress_label.pack(anchor='e')

        # الصف الثالث - معلومات إضافية
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 0))

        # الأيام المتبقية
        self.create_calculated_field(
            row3, "⏰ الأيام المتبقية", 'days_remaining', width_ratio=1/3
        )

        # نسبة التأخير
        self.create_calculated_field(
            row3, "⚠️ نسبة التأخير", 'delay_percentage', width_ratio=1/3
        )

        # تقييم الأداء
        self.create_calculated_field(
            row3, "⭐ تقييم الأداء", 'performance_rating', width_ratio=1/3
        )

        return section

    def create_notes_section(self):
        """إنشاء قسم الملاحظات والمرفقات"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📝 الملاحظات والمرفقات",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="ملاحظات إضافية ومرفقات ووثائق الشحنة",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # منطقة الملاحظات
        notes_frame = create_simple_rtl_frame(content_frame)
        notes_frame.pack(fill='x', pady=(0, 30))

        notes_label = create_simple_rtl_label(
            notes_frame,
            text="📝 ملاحظات إضافية:",
            font=('Segoe UI', 14, 'bold')
        )
        notes_label.pack(anchor='e', pady=(0, 10))

        self.notes_text = create_simple_rtl_text(
            notes_frame,
            height=8
        )
        self.notes_text.pack(fill='x', pady=(0, 20))

        # منطقة المرفقات
        attachments_frame = create_simple_rtl_frame(content_frame)
        attachments_frame.pack(fill='both', expand=True)

        attachments_label = create_simple_rtl_label(
            attachments_frame,
            text="📎 المرفقات والوثائق:",
            font=('Segoe UI', 14, 'bold')
        )
        attachments_label.pack(anchor='e', pady=(0, 10))

        # أزرار المرفقات
        attachments_buttons = create_simple_rtl_frame(attachments_frame)
        attachments_buttons.pack(fill='x', pady=(0, 10))

        add_file_btn = create_simple_rtl_button(
            attachments_buttons,
            text="📎 إضافة ملف",
            button_type="secondary",
            command=self.add_general_attachment
        )
        add_file_btn.pack(side='right', padx=5)

        view_files_btn = create_simple_rtl_button(
            attachments_buttons,
            text="👁️ عرض الملفات",
            button_type="ghost",
            command=self.view_attachments
        )
        view_files_btn.pack(side='right', padx=5)

        remove_file_btn = create_simple_rtl_button(
            attachments_buttons,
            text="🗑️ حذف ملف",
            button_type="danger",
            command=self.remove_attachment
        )
        remove_file_btn.pack(side='right', padx=5)

        # قائمة المرفقات
        try:
            self.attachments_list = tk.Listbox(
                attachments_frame,
                height=6,
                font=('Segoe UI', 12),
                bg=COLORS['surface'],
                fg=COLORS['text_primary'],
                selectbackground=COLORS['primary_light'],
                relief='solid',
                bd=2
            )
        except Exception as e:
            # في حالة الخطأ، إنشاء قائمة بسيطة
            self.attachments_list = tk.Listbox(
                attachments_frame,
                height=6
            )
            print(f"تحذير في Listbox: {e}")
        self.attachments_list.pack(fill='both', expand=True, pady=(10, 0))

        return section

    def create_footer_content(self):
        """إنشاء محتوى شريط الحالة"""
        # استخدام الإطار المُنشأ مسبقاً
        footer_frame = self.footer_frame

        # الجانب الأيمن - أزرار الإجراءات الرئيسية
        actions_frame = create_simple_rtl_frame(footer_frame)
        actions_frame.configure(bg=COLORS['light'])
        actions_frame.pack(side='right', padx=40, pady=20)

        if self.mode != 'view':
            # زر الحفظ الرئيسي
            save_btn = create_simple_rtl_button(
                actions_frame,
                text="💾 حفظ الشحنة",
                button_type="success",
                command=self.save_shipment
            )
            save_btn.configure(font=('Segoe UI', 14, 'bold'), padx=30, pady=15)
            save_btn.pack(side='right', padx=10)

            # زر الحفظ والإغلاق
            save_close_btn = create_simple_rtl_button(
                actions_frame,
                text="💾 حفظ وإغلاق",
                button_type="primary",
                command=self.save_and_close
            )
            save_close_btn.configure(font=('Segoe UI', 14, 'bold'), padx=30, pady=15)
            save_close_btn.pack(side='right', padx=10)

            # زر معلومات الحالة
            status_info_btn = create_simple_rtl_button(
                actions_frame,
                text="ℹ️ معلومات الحالة",
                button_type="info",
                command=self.show_status_info
            )
            status_info_btn.configure(font=('Segoe UI', 12, 'bold'), padx=20, pady=15)
            status_info_btn.pack(side='right', padx=10)

        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            actions_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_form
        )
        close_btn.configure(font=('Segoe UI', 14, 'bold'), padx=30, pady=15)
        close_btn.pack(side='right', padx=10)

        # الوسط - أزرار التنقل بين الأقسام
        nav_frame = create_simple_rtl_frame(footer_frame)
        nav_frame.configure(bg=COLORS['light'])
        nav_frame.pack(expand=True, pady=20)

        # زر القسم السابق
        self.prev_section_btn = create_simple_rtl_button(
            nav_frame,
            text="◀ القسم السابق",
            button_type="outline",
            command=self.prev_section
        )
        self.prev_section_btn.configure(font=('Segoe UI', 12, 'bold'), padx=20, pady=10)
        self.prev_section_btn.pack(side='right', padx=10)

        # مؤشر القسم الحالي
        self.section_indicator = create_simple_rtl_label(
            nav_frame,
            text="القسم 1 من 8",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['primary'],
            bg=COLORS['light']
        )
        self.section_indicator.pack(side='right', padx=20)

        # زر القسم التالي
        self.next_section_btn = create_simple_rtl_button(
            nav_frame,
            text="القسم التالي ▶",
            button_type="outline",
            command=self.next_section
        )
        self.next_section_btn.configure(font=('Segoe UI', 12, 'bold'), padx=20, pady=10)
        self.next_section_btn.pack(side='right', padx=10)

        # الجانب الأيسر - معلومات الحالة
        status_frame = create_simple_rtl_frame(footer_frame)
        status_frame.configure(bg=COLORS['light'])
        status_frame.pack(side='left', padx=40, pady=20)

        self.status_label = create_simple_rtl_label(
            status_frame,
            text="جاهز",
            font=('Segoe UI', 12, 'normal'),
            bg=COLORS['light']
        )
        self.status_label.pack(side='left')

        # مؤشر الأقسام المطلوبة
        self.sections_indicator = create_simple_rtl_label(
            status_frame,
            text="",
            font=('Segoe UI', 10, 'normal'),
            bg=COLORS['light'],
            fg=COLORS['info']
        )
        self.sections_indicator.pack(side='left', padx=(20, 0))

        # مؤشر التعديل
        self.modified_indicator = create_simple_rtl_label(
            status_frame,
            text="",
            font=('Segoe UI', 12, 'bold'),
            bg=COLORS['light']
        )
        self.modified_indicator.pack(side='left', padx=(20, 0))

        # إضافة شريط مفاتيح الاختصار في أسفل الشاشة
        self.create_shortcuts_bar()

    def create_shortcuts_bar(self):
        """إنشاء شريط مفاتيح الاختصار المحسن في أسفل الشاشة"""
        # شريط مفاتيح الاختصار
        shortcuts_frame = create_simple_rtl_frame(self.main_frame)
        shortcuts_frame.configure(bg='#263238', height=40)
        shortcuts_frame.pack(fill='x', side='bottom')
        shortcuts_frame.pack_propagate(False)

        # معلومات مفاتيح الاختصار الأساسية
        primary_shortcuts = [
            ("F11", "ملء الشاشة", "#2196F3"),
            ("Ctrl+S", "حفظ", "#4CAF50"),
            ("F9", "بحث الأصناف", "#FF9800"),
            ("Esc", "خروج", "#F44336")
        ]

        # معلومات مفاتيح الاختصار الثانوية
        secondary_shortcuts = [
            ("F5", "معاينة"),
            ("Ctrl+N", "نموذج جديد"),
            ("Ctrl+D", "نسخ"),
            ("F1", "مساعدة")
        ]

        # الجانب الأيمن - مفاتيح الاختصار الأساسية
        primary_frame = create_simple_rtl_frame(shortcuts_frame)
        primary_frame.configure(bg='#263238')
        primary_frame.pack(side='right', padx=20, pady=5)

        for i, (key, description, color) in enumerate(primary_shortcuts):
            if i > 0:
                # فاصل
                separator = create_simple_rtl_label(
                    primary_frame,
                    text=" • ",
                    font=('Segoe UI', 10, 'bold'),
                    fg='#90A4AE',
                    bg='#263238'
                )
                separator.pack(side='right', padx=3)

            # إطار المفتاح
            key_frame = create_simple_rtl_frame(primary_frame)
            key_frame.configure(bg='#263238')
            key_frame.pack(side='right', padx=2)

            # مفتاح الاختصار
            key_btn = create_simple_rtl_label(
                key_frame,
                text=key,
                font=('Segoe UI', 9, 'bold'),
                fg='white',
                bg=color,
                relief='raised',
                bd=1
            )
            key_btn.pack(side='right', padx=1)

            # الوصف
            desc_label = create_simple_rtl_label(
                key_frame,
                text=description,
                font=('Segoe UI', 9),
                fg='#CFD8DC',
                bg='#263238'
            )
            desc_label.pack(side='right', padx=(5, 0))

        # الوسط - مفاتيح الاختصار الثانوية
        secondary_frame = create_simple_rtl_frame(shortcuts_frame)
        secondary_frame.configure(bg='#263238')
        secondary_frame.pack(expand=True, pady=5)

        secondary_text = " | ".join([f"{key}: {desc}" for key, desc in secondary_shortcuts])
        secondary_label = create_simple_rtl_label(
            secondary_frame,
            text=secondary_text,
            font=('Segoe UI', 8),
            fg='#90A4AE',
            bg='#263238'
        )
        secondary_label.pack(anchor='center')

        # الجانب الأيسر - معلومات النظام
        info_frame = create_simple_rtl_frame(shortcuts_frame)
        info_frame.configure(bg='#263238')
        info_frame.pack(side='left', padx=20, pady=5)

        # معلومات المستخدم
        user_info = f"👤 {auth_manager.current_user.get('username', 'غير معروف')}"
        current_time = datetime.now().strftime("%H:%M")

        user_label = create_simple_rtl_label(
            info_frame,
            text=user_info,
            font=('Segoe UI', 9, 'bold'),
            fg='#81C784',
            bg='#263238'
        )
        user_label.pack(side='left')

        # فاصل
        separator = create_simple_rtl_label(
            info_frame,
            text=" | ",
            font=('Segoe UI', 9),
            fg='#90A4AE',
            bg='#263238'
        )
        separator.pack(side='left', padx=5)

        # الوقت
        time_label = create_simple_rtl_label(
            info_frame,
            text=f"🕐 {current_time}",
            font=('Segoe UI', 9),
            fg='#90CAF9',
            bg='#263238'
        )
        time_label.pack(side='left')

        # إضافة مؤشر حالة الاتصال
        status_indicator = create_simple_rtl_label(
            info_frame,
            text=" | 🟢 متصل",
            font=('Segoe UI', 8),
            fg='#4CAF50',
            bg='#263238'
        )
        status_indicator.pack(side='left', padx=(5, 0))

    # وظائف إنشاء الحقول المساعدة
    def create_field_group(self, parent, label_text, var_name, placeholder="", required=False, width_ratio=1/3):
        """إنشاء مجموعة حقل مع تسمية"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        if required:
            label.configure(fg=COLORS['danger'])
        label.pack(anchor='e', pady=(0, 10))

        # الحقل
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 12, 'normal'))
        entry.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_data_change)

        return entry

    def create_combobox_field(self, parent, label_text, var_name, values=[], width_ratio=1/3):
        """إنشاء حقل قائمة منسدلة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars[var_name] = tk.StringVar()
        combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[var_name]
        )
        combo['values'] = values
        combo.configure(font=('Segoe UI', 12, 'normal'))
        combo.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_data_change)

        return combo

    def create_date_field(self, parent, label_text, var_name, placeholder="", required=False, width_ratio=1/3):
        """إنشاء حقل تاريخ مع زر اختيار التاريخ"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        if required:
            label.configure(fg=COLORS['danger'])
        label.pack(anchor='e', pady=(0, 10))

        # إطار الحقل مع الزر
        entry_frame = create_simple_rtl_frame(field_frame)
        entry_frame.pack(fill='x')

        # زر اختيار التاريخ
        date_btn = create_simple_rtl_button(
            entry_frame,
            text="📅",
            button_type="secondary",
            command=lambda: self.open_date_picker(var_name)
        )
        date_btn.configure(
            font=('Segoe UI', 12, 'bold'),
            width=3,
            padx=5,
            pady=8
        )
        date_btn.pack(side='left', padx=(0, 10))

        # حقل إدخال التاريخ
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            entry_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 12, 'normal'))
        entry.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_data_change)

        return entry

    def create_supplier_field(self, parent, width_ratio=1/3):
        """إنشاء حقل المورد مع تحميل من قاعدة البيانات"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية مع تلميح F9
        label = create_simple_rtl_label(
            field_frame,
            text="🏢 المورد * (F9 للبحث)",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['danger']
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars['supplier_id'] = tk.StringVar()
        self.supplier_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars['supplier_id']
        )
        self.supplier_combo.configure(font=('Segoe UI', 12, 'normal'))
        self.supplier_combo.pack(fill='x', ipady=12)

        # ربط F9 للبحث في الموردين
        self.supplier_combo.bind('<F9>', self.show_supplier_search)
        self.supplier_combo.bind('<KeyPress-F9>', self.show_supplier_search)

        # ربط تغيير البيانات
        self.form_vars['supplier_id'].trace('w', self.on_data_change)

        return self.supplier_combo

    def open_date_picker(self, var_name):
        """فتح نافذة اختيار التاريخ"""
        try:
            # إنشاء نافذة منبثقة
            date_window = tk.Toplevel(self.root)
            date_window.title("اختيار التاريخ")
            date_window.geometry("300x250")
            date_window.resizable(False, False)
            date_window.transient(self.root)
            date_window.grab_set()

            # توسيط النافذة
            date_window.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))

            # إطار التقويم
            cal_frame = tk.Frame(date_window, bg='white')
            cal_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # التقويم
            current_date = date.today()
            try:
                # محاولة الحصول على التاريخ الحالي من الحقل
                current_value = self.form_vars[var_name].get().strip()
                if current_value:
                    current_date = datetime.strptime(current_value, '%Y-%m-%d').date()
            except:
                pass

            cal = DateEntry(
                cal_frame,
                width=12,
                background='darkblue',
                foreground='white',
                borderwidth=2,
                year=current_date.year,
                month=current_date.month,
                day=current_date.day,
                date_pattern='yyyy-mm-dd',
                font=('Segoe UI', 12)
            )
            cal.pack(pady=20)

            # إطار الأزرار
            btn_frame = tk.Frame(date_window, bg='white')
            btn_frame.pack(fill='x', padx=20, pady=(0, 20))

            # زر الموافقة
            ok_btn = tk.Button(
                btn_frame,
                text="✅ موافق",
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['primary'],
                fg='white',
                padx=20,
                pady=8,
                command=lambda: self.select_date(var_name, cal.get_date(), date_window)
            )
            ok_btn.pack(side='right', padx=(10, 0))

            # زر الإلغاء
            cancel_btn = tk.Button(
                btn_frame,
                text="❌ إلغاء",
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['secondary'],
                fg='white',
                padx=20,
                pady=8,
                command=date_window.destroy
            )
            cancel_btn.pack(side='right')

            # زر اليوم
            today_btn = tk.Button(
                btn_frame,
                text="📅 اليوم",
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['info'],
                fg='white',
                padx=20,
                pady=8,
                command=lambda: self.select_date(var_name, date.today(), date_window)
            )
            today_btn.pack(side='left')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح اختيار التاريخ: {str(e)}")

    def select_date(self, var_name, selected_date, window):
        """تحديد التاريخ المختار"""
        try:
            # تحويل التاريخ إلى النسق المطلوب
            formatted_date = selected_date.strftime('%Y-%m-%d')
            self.form_vars[var_name].set(formatted_date)
            window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديد التاريخ: {str(e)}")

    def create_status_field(self, parent, width_ratio=1/3):
        """إنشاء حقل الحالة مع ألوان"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="📊 حالة الشحنة *",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['danger']
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars['status'] = tk.StringVar()
        self.status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars['status']
        )
        self.status_combo['values'] = list(SHIPMENT_STATUS.values())
        self.status_combo.configure(font=('Segoe UI', 12, 'normal'))
        self.status_combo.pack(fill='x', ipady=12)

        # ربط تغيير البيانات والتقدم
        self.form_vars['status'].trace('w', self.on_status_change)

        return self.status_combo

    def create_priority_display(self, parent, width_ratio=1/3):
        """إنشاء عرض الأولوية المحسوبة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="⚡ الأولوية (تلقائية)",
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # عرض الأولوية
        self.priority_display = create_simple_rtl_label(
            field_frame,
            text="عادي ⚪",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_secondary']
        )
        self.priority_display.pack(anchor='e', pady=(12, 0))

        return self.priority_display

    def create_calculated_field(self, parent, label_text, var_name, width_ratio=1/3):
        """إنشاء حقل محسوب تلقائياً"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # العرض
        display = create_simple_rtl_label(
            field_frame,
            text="--",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['primary']
        )
        display.pack(anchor='e', pady=(12, 0))

        # حفظ المرجع
        setattr(self, f'{var_name}_display', display)

        return display

    def create_datetime_display(self, parent, width_ratio=1/2):
        """إنشاء عرض التاريخ والوقت"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="🕐 آخر تحديث:",
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # العرض
        self.last_update_display = create_simple_rtl_label(
            field_frame,
            text=datetime.now().strftime('%Y/%m/%d %H:%M:%S'),
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['primary']
        )
        self.last_update_display.pack(anchor='e', pady=(12, 0))

        return self.last_update_display

    # وظائف التنقل بين الأقسام
    def switch_section(self, section_id):
        """التبديل إلى قسم معين"""
        if 0 <= section_id < len(self.sections):
            # إخفاء جميع الأقسام
            for section in self.sections:
                section.pack_forget()

            # عرض القسم المحدد
            self.sections[section_id].pack(fill='both', expand=True)
            self.current_section = section_id

            # تحديث أزرار التنقل
            self.update_navigation_buttons()

            # تحديث أزرار التنقل
            self.update_navigation_buttons()

            # تحديث مؤشر القسم المحسن
            if hasattr(self, 'section_indicator') and self.section_indicator:
                section_name = self.sections[section_id]['title']
                section_status = "✅" if self.is_section_complete(section_id) else "⏳"
                required_status = "مطلوب" if self.is_section_required(section_id) else "اختياري"

                indicator_text = f"{section_status} القسم {section_id + 1} من {len(self.sections)}: {section_name} ({required_status})"
                self.section_indicator.configure(text=indicator_text)

            # تحديث ألوان أزرار التنقل العلوية
            if hasattr(self, 'nav_buttons') and self.nav_buttons:
                for i, btn in enumerate(self.nav_buttons):
                    try:
                        if i == section_id:
                            btn.configure(
                                bg=COLORS['primary_light'],
                                fg=COLORS['text_white']
                            )
                        else:
                            btn.configure(
                                bg=COLORS['primary'],
                                fg=COLORS['text_white']
                            )
                    except Exception as e:
                        print(f"خطأ في تحديث زر التنقل {i}: {e}")

    def next_section(self):
        """الانتقال للقسم التالي"""
        if self.current_section < len(self.sections) - 1:
            self.switch_section(self.current_section + 1)

    def prev_section(self):
        """الانتقال للقسم السابق"""
        if self.current_section > 0:
            self.switch_section(self.current_section - 1)

    def update_navigation_buttons(self):
        """تحديث حالة أزرار التنقل"""
        try:
            # التحقق من وجود الأزرار قبل تحديثها
            if hasattr(self, 'prev_section_btn') and self.prev_section_btn:
                # زر السابق
                if self.current_section == 0:
                    self.prev_section_btn.configure(state='disabled')
                else:
                    self.prev_section_btn.configure(state='normal')

            if hasattr(self, 'next_section_btn') and self.next_section_btn:
                # زر التالي
                if self.current_section == len(self.sections) - 1:
                    self.next_section_btn.configure(state='disabled')
                else:
                    self.next_section_btn.configure(state='normal')
        except Exception as e:
            print(f"خطأ في تحديث أزرار التنقل: {e}")

    # وظائف البيانات
    def load_form_data(self):
        """تحميل بيانات النموذج"""
        try:
            # تحميل قائمة الموردين
            self.load_suppliers()

            # تعيين القيم الافتراضية للشحنات الجديدة
            if self.mode == 'add':
                self.set_default_values()

            # تحميل بيانات الشحنة إذا كانت موجودة
            elif self.shipment_data and self.mode in ['edit', 'duplicate', 'view']:
                self.populate_form_data()

            # تطبيق قيود العرض فقط
            if self.mode == 'view':
                self.make_form_readonly()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all(
                "SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            )

            supplier_values = [f"{s['supplier_name']} (ID: {s['id']})" for s in suppliers]
            self.supplier_combo['values'] = supplier_values

            # حفظ بيانات الموردين للمرجع
            self.suppliers_data = suppliers

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def set_default_values(self):
        """تعيين القيم الافتراضية"""
        # إنشاء رقم شحنة تلقائي
        self.form_vars['shipment_number'].set(self.generate_shipment_number())

        # تعيين التاريخ الحالي
        self.form_vars['shipment_date'].set(date.today().strftime('%Y-%m-%d'))

        # تعيين الحالة الافتراضية
        self.form_vars['status'].set('في الانتظار')

        # تعيين العملة الافتراضية
        self.form_vars['currency'].set('USD')

        # تعيين حالة الدفع الافتراضية
        self.form_vars['payment_status'].set('لم يتم الدفع')

        # تعيين حالة الإفراج الافتراضية
        self.form_vars['release_status'].set('بدون إفراج')

        # تعيين عدد الحاويات الافتراضي
        self.form_vars['container_count'].set('1')

        # تحديث شريط التقدم
        self.update_progress_display()

    def generate_shipment_number(self):
        """إنشاء رقم شحنة تلقائي"""
        try:
            # الحصول على آخر رقم شحنة
            last_shipment = self.db_manager.fetch_one(
                "SELECT shipment_number FROM shipments ORDER BY created_at DESC LIMIT 1"
            )

            if last_shipment and last_shipment['shipment_number']:
                last_number = last_shipment['shipment_number']
                if last_number.startswith('SH-'):
                    try:
                        number_part = int(last_number.split('-')[1])
                        new_number = number_part + 1
                        return f"SH-{new_number:06d}"
                    except:
                        pass

            return f"SH-{1:06d}"

        except Exception:
            return f"SH-{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def populate_form_data(self):
        """ملء النموذج بالبيانات"""
        try:
            # ملء جميع الحقول
            for field, var in self.form_vars.items():
                value = self.shipment_data.get(field, '')
                if value:
                    var.set(str(value))

                    # معالجة خاصة لحقول الروابط (readonly)
                    if hasattr(self, 'link_entries') and field in self.link_entries:
                        entry = self.link_entries[field]
                        entry.configure(state='normal')  # تمكين التحديث مؤقتاً
                        entry.delete(0, tk.END)
                        entry.insert(0, str(value))
                        entry.configure(state='readonly')  # إعادة تعيين للقراءة فقط

            # معالجة خاصة للمورد
            supplier_id = self.shipment_data.get('supplier_id')
            if supplier_id and hasattr(self, 'suppliers_data'):
                for supplier in self.suppliers_data:
                    if supplier['id'] == supplier_id:
                        supplier_text = f"{supplier['supplier_name']} (ID: {supplier['id']})"
                        self.form_vars['supplier_id'].set(supplier_text)
                        break

            # ملء منطقة الملاحظات
            notes = self.shipment_data.get('notes', '')
            if notes:
                self.notes_text.delete('1.0', tk.END)
                self.notes_text.insert('1.0', notes)

            # ملء وصف البضائع
            goods_desc = self.shipment_data.get('goods_description', '')
            if goods_desc:
                self.goods_description_text.delete('1.0', tk.END)
                self.goods_description_text.insert('1.0', goods_desc)

            # ملء ملاحظات المستندات
            if hasattr(self, 'documents_notes_text'):
                docs_notes = self.shipment_data.get('documents_notes', '')
                if docs_notes:
                    self.documents_notes_text.delete('1.0', tk.END)
                    self.documents_notes_text.insert('1.0', docs_notes)

            # تحديث شريط التقدم
            self.update_progress_display()

            # تحديث تقدم المستندات
            self.update_documents_progress()

            # تحديث رؤية الأقسام والتنقل
            self.update_sections_visibility()
            self.update_navigation_buttons()

            # معالجة خاصة لأرقام الحاويات المتعددة
            self.populate_container_numbers()

            # تحميل أصناف الشحنة
            if self.shipment_data.get('id'):
                self.load_shipment_items(self.shipment_data.get('id'))

            # إذا كان الوضع نسخ، مسح بعض الحقول
            if self.mode == 'duplicate':
                self.form_vars['shipment_number'].set(self.generate_shipment_number())
                self.form_vars['status'].set('في الانتظار')
                self.form_vars['tracking_number'].set('')
                self.form_vars['current_location'].set('')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في ملء البيانات: {str(e)}")

    def populate_container_numbers(self):
        """ملء أرقام الحاويات المتعددة"""
        try:
            # الحصول على عدد الحاويات وأرقامها
            container_count = self.shipment_data.get('container_count', 1)
            container_numbers_json = self.shipment_data.get('container_numbers_json', '')

            # تحليل JSON لأرقام الحاويات
            container_numbers = []
            if container_numbers_json:
                try:
                    container_numbers = json.loads(container_numbers_json)
                except:
                    # في حالة فشل تحليل JSON، استخدم رقم الحاوية الواحد
                    single_container = self.shipment_data.get('container_number', '')
                    if single_container:
                        container_numbers = [single_container]
            else:
                # إذا لم يكن هناك JSON، استخدم رقم الحاوية الواحد
                single_container = self.shipment_data.get('container_number', '')
                if single_container:
                    container_numbers = [single_container]

            # تحديث عدد الحاويات
            if container_count and container_count > 0:
                self.form_vars['container_count'].set(str(container_count))
                # تحديث حقول أرقام الحاويات
                self.update_container_fields(container_count)

                # ملء أرقام الحاويات
                for i, container_num in enumerate(container_numbers):
                    if i < len(self.container_number_vars):
                        self.container_number_vars[i].set(container_num)

        except Exception as e:
            print(f"خطأ في ملء أرقام الحاويات: {e}")

    def make_form_readonly(self):
        """جعل النموذج للقراءة فقط"""
        # تعطيل جميع حقول الإدخال
        for widget in self.sections_frame.winfo_children():
            self.disable_widget_recursively(widget)

    def disable_widget_recursively(self, widget):
        """تعطيل عنصر وجميع عناصره الفرعية"""
        try:
            if hasattr(widget, 'configure'):
                if isinstance(widget, (tk.Entry, ttk.Combobox)):
                    widget.configure(state='readonly')
                elif isinstance(widget, tk.Text):
                    widget.configure(state='disabled')
                elif isinstance(widget, tk.Button):
                    widget.configure(state='disabled')

            # تطبيق على العناصر الفرعية
            for child in widget.winfo_children():
                self.disable_widget_recursively(child)

        except Exception:
            pass

    # وظائف التفاعل
    def on_data_change(self, *args):
        """عند تغيير البيانات"""
        if not self.is_modified:
            self.is_modified = True
            self.modified_indicator.configure(
                text="● تم التعديل",
                fg=COLORS['warning']
            )

        # تحديث الحسابات التلقائية
        self.update_calculations()

    def on_status_change(self, *args):
        """عند تغيير الحالة"""
        self.on_data_change()
        self.update_progress_display()
        self.update_priority_display()
        self.update_sections_visibility()
        self.update_navigation_buttons()

    def on_document_change(self, *args):
        """عند تغيير المستندات"""
        self.on_data_change()
        self.update_documents_progress()

    def update_progress_display(self):
        """تحديث عرض التقدم"""
        try:
            status = self.form_vars.get('status', tk.StringVar()).get()

            # خريطة التقدم
            progress_map = {
                'في الانتظار': 10,
                'تحت الطلب': 5,
                'مؤكدة': 25,
                'تم الشحن': 40,
                'في الطريق': 60,
                'وصلت': 80,
                'في الجمارك': 85,
                'تم التسليم': 100,
                'ملغية': 0,
                'متأخرة': 50
            }

            progress = progress_map.get(status, 0)
            self.progress_bar['value'] = progress
            self.progress_label.configure(text=f"{progress}%")

            # تغيير لون شريط التقدم حسب الحالة
            if progress == 100:
                self.progress_label.configure(fg=COLORS['success'])
            elif progress >= 80:
                self.progress_label.configure(fg=COLORS['primary'])
            elif progress >= 50:
                self.progress_label.configure(fg=COLORS['warning'])
            else:
                self.progress_label.configure(fg=COLORS['danger'])

        except Exception:
            pass

    def update_priority_display(self):
        """تحديث عرض الأولوية"""
        try:
            priority = self.calculate_priority()

            priority_icons = {
                'urgent': '🔴 عاجل',
                'high': '🟠 عالي',
                'medium': '🟡 متوسط',
                'low': '🟢 منخفض',
                'normal': '⚪ عادي'
            }

            priority_colors = {
                'urgent': COLORS['danger'],
                'high': COLORS['warning'],
                'medium': COLORS['info'],
                'low': COLORS['success'],
                'normal': COLORS['text_secondary']
            }

            icon_text = priority_icons.get(priority, '⚪ عادي')
            color = priority_colors.get(priority, COLORS['text_secondary'])

            self.priority_display.configure(text=icon_text, fg=color)

        except Exception:
            pass

    def update_sections_visibility(self):
        """تحديث رؤية الأقسام بناءً على حالة الشحنة"""
        try:
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            required_sections = self.get_required_sections_by_status(current_status)

            # إخفاء جميع الأقسام أولاً
            for i, section in enumerate(self.sections):
                if i in required_sections:
                    section.pack(fill='both', expand=True)
                else:
                    section.pack_forget()

            # التأكد من عرض القسم الحالي إذا كان مطلوباً
            if hasattr(self, 'current_section') and self.current_section not in required_sections:
                # الانتقال إلى أول قسم مطلوب
                if required_sections:
                    self.switch_section(required_sections[0])

            # تحديث مؤشر الأقسام
            self.update_sections_indicator(current_status, required_sections)

        except Exception as e:
            print(f"خطأ في تحديث رؤية الأقسام: {e}")

    def update_sections_indicator(self, status, required_sections):
        """تحديث مؤشر الأقسام المطلوبة"""
        try:
            if hasattr(self, 'sections_indicator'):
                section_count = len(required_sections)
                total_sections = 8

                indicator_text = f"📋 {section_count}/{total_sections} أقسام مطلوبة لحالة '{status}'"
                self.sections_indicator.configure(text=indicator_text)

        except Exception as e:
            print(f"خطأ في تحديث مؤشر الأقسام: {e}")

    def update_navigation_buttons(self):
        """تحديث أزرار التنقل بناءً على الأقسام المطلوبة"""
        try:
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            required_sections = self.get_required_sections_by_status(current_status)

            # تحديث حالة أزرار التنقل
            for i, button in enumerate(self.nav_buttons):
                if i in required_sections:
                    button.configure(state='normal', bg=COLORS['secondary'])
                    # إضافة مؤشر للأقسام المطلوبة
                    if '✓' not in button.cget('text'):
                        current_text = button.cget('text')
                        if not current_text.endswith(' ✓'):
                            button.configure(text=current_text + ' ✓')
                else:
                    button.configure(state='disabled', bg=COLORS['disabled'])
                    # إزالة المؤشر من الأقسام غير المطلوبة
                    current_text = button.cget('text')
                    if current_text.endswith(' ✓'):
                        button.configure(text=current_text.replace(' ✓', ''))

        except Exception as e:
            print(f"خطأ في تحديث أزرار التنقل: {e}")

    def calculate_priority(self):
        """حساب أولوية الشحنة"""
        try:
            priority_score = 0

            # عامل القيمة
            total_value = float(self.form_vars.get('total_value', tk.StringVar()).get() or 0)
            if total_value > 100000:
                priority_score += 3
            elif total_value > 50000:
                priority_score += 2
            elif total_value > 10000:
                priority_score += 1

            # عامل التأخير
            expected_date = self.form_vars.get('expected_arrival_date', tk.StringVar()).get()
            if expected_date:
                try:
                    expected = datetime.strptime(expected_date, '%Y-%m-%d').date()
                    today = date.today()
                    if expected < today:
                        priority_score += 4  # متأخرة
                    elif (expected - today).days <= 3:
                        priority_score += 2  # قريبة
                except:
                    pass

            # عامل الحالة
            status = self.form_vars.get('status', tk.StringVar()).get()
            if status in ['متأخرة', 'في الجمارك']:
                priority_score += 2
            elif status in ['في الطريق', 'وصلت']:
                priority_score += 1

            # تحديد الأولوية النهائية
            if priority_score >= 6:
                return 'urgent'
            elif priority_score >= 4:
                return 'high'
            elif priority_score >= 2:
                return 'medium'
            elif priority_score >= 1:
                return 'low'
            else:
                return 'normal'

        except Exception:
            return 'normal'

    def update_calculations(self):
        """تحديث الحسابات التلقائية"""
        try:
            # حساب إجمالي التكلفة
            total_value = float(self.form_vars.get('total_value', tk.StringVar()).get() or 0)
            shipping_cost = float(self.form_vars.get('shipping_cost', tk.StringVar()).get() or 0)
            insurance_cost = float(self.form_vars.get('insurance_cost', tk.StringVar()).get() or 0)
            additional_fees = float(self.form_vars.get('additional_fees', tk.StringVar()).get() or 0)
            customs_fees = float(self.form_vars.get('customs_fees', tk.StringVar()).get() or 0)
            handling_fees = float(self.form_vars.get('handling_fees', tk.StringVar()).get() or 0)

            total_cost = total_value + shipping_cost + insurance_cost + additional_fees + customs_fees + handling_fees

            if hasattr(self, 'total_cost_display'):
                currency = self.form_vars.get('currency', tk.StringVar()).get() or 'USD'
                self.total_cost_display.configure(text=f"{total_cost:,.2f} {currency}")

            # حساب الأيام المتبقية
            expected_date = self.form_vars.get('expected_arrival_date', tk.StringVar()).get()
            if expected_date and hasattr(self, 'days_remaining_display'):
                try:
                    expected = datetime.strptime(expected_date, '%Y-%m-%d').date()
                    today = date.today()
                    diff = (expected - today).days

                    if diff < 0:
                        self.days_remaining_display.configure(
                            text=f"متأخرة {abs(diff)} يوم",
                            fg=COLORS['danger']
                        )
                    elif diff == 0:
                        self.days_remaining_display.configure(
                            text="اليوم",
                            fg=COLORS['warning']
                        )
                    elif diff == 1:
                        self.days_remaining_display.configure(
                            text="غداً",
                            fg=COLORS['warning']
                        )
                    else:
                        self.days_remaining_display.configure(
                            text=f"{diff} يوم",
                            fg=COLORS['success']
                        )
                except:
                    self.days_remaining_display.configure(text="غير صحيح", fg=COLORS['danger'])

        except Exception:
            pass

    def update_documents_progress(self):
        """تحديث تقدم المستندات"""
        try:
            # قائمة المستندات الأساسية
            document_fields = [
                'commercial_invoice_status',
                'packing_list_status',
                'certificate_of_origin_status',
                'insurance_policy_status',
                'quality_certificate_status',
                'health_certificate_status',
                'customs_declaration_status',
                'import_license_status',
                'inspection_certificate_status'
            ]

            total_docs = len(document_fields)
            completed_docs = 0

            # حساب المستندات المكتملة
            for field in document_fields:
                if field in self.form_vars:
                    status = self.form_vars[field].get()
                    if status in ['متوفر', 'مرسل', 'مؤكد']:
                        completed_docs += 1

            # حساب النسبة المئوية
            if total_docs > 0:
                progress_percentage = (completed_docs / total_docs) * 100
            else:
                progress_percentage = 0

            # تحديث شريط التقدم
            if hasattr(self, 'documents_progress_bar'):
                self.documents_progress_bar['value'] = progress_percentage

            # تحديث النص
            if hasattr(self, 'documents_progress_label'):
                self.documents_progress_label.configure(
                    text=f"{progress_percentage:.0f}% مكتمل ({completed_docs}/{total_docs})"
                )

                # تغيير لون النص حسب النسبة
                if progress_percentage == 100:
                    self.documents_progress_label.configure(fg=COLORS['success'])
                elif progress_percentage >= 75:
                    self.documents_progress_label.configure(fg=COLORS['primary'])
                elif progress_percentage >= 50:
                    self.documents_progress_label.configure(fg=COLORS['warning'])
                else:
                    self.documents_progress_label.configure(fg=COLORS['danger'])

            # تحديث حالة المستندات العامة تلقائياً
            if hasattr(self, 'form_vars') and 'documents_status' in self.form_vars:
                if progress_percentage == 100:
                    self.form_vars['documents_status'].set('مكتملة')
                elif progress_percentage >= 75:
                    self.form_vars['documents_status'].set('مكتملة جزئياً')
                elif progress_percentage >= 25:
                    self.form_vars['documents_status'].set('قيد المراجعة')
                else:
                    self.form_vars['documents_status'].set('غير مكتملة')

        except Exception as e:
            print(f"خطأ في تحديث تقدم المستندات: {e}")

    # وظائف إدارة الأصناف
    def add_item(self):
        """إضافة صنف جديد"""
        try:
            item_dialog = ItemDialog(self.root, mode='add')
            self.root.wait_window(item_dialog.root)

            if hasattr(item_dialog, 'result') and item_dialog.result:
                # إضافة الصنف للقائمة
                item_data = item_dialog.result
                item_data['id'] = str(uuid.uuid4())
                self.items_data.append(item_data)

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # تحديث مؤشر التعديل
                self.on_data_change()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الصنف: {str(e)}")

    def edit_item(self):
        """تعديل صنف موجود"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return

        try:
            item_dialog = ItemDialog(self.root, mode='edit', item_data=self.selected_item)
            self.root.wait_window(item_dialog.root)

            if hasattr(item_dialog, 'result') and item_dialog.result:
                # تحديث الصنف في القائمة
                for i, item in enumerate(self.items_data):
                    if item.get('id') == self.selected_item.get('id'):
                        self.items_data[i] = item_dialog.result
                        break

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # تحديث مؤشر التعديل
                self.on_data_change()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الصنف: {str(e)}")

    def delete_item(self):
        """حذف صنف"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الصنف '{self.selected_item.get('item_name', '')}'؟"
        )

        if result:
            try:
                # حذف الصنف من القائمة
                self.items_data = [item for item in self.items_data
                                 if item.get('id') != self.selected_item.get('id')]

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # مسح التحديد
                self.selected_item = None

                # تحديث مؤشر التعديل
                self.on_data_change()

                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الصنف: {str(e)}")

    def duplicate_item(self):
        """نسخ صنف"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للنسخ")
            return

        try:
            item_dialog = ItemDialog(self.root, mode='duplicate', item_data=self.selected_item)
            self.root.wait_window(item_dialog.root)

            if hasattr(item_dialog, 'result') and item_dialog.result:
                # إضافة الصنف المنسوخ للقائمة
                item_data = item_dialog.result
                item_data['id'] = str(uuid.uuid4())
                self.items_data.append(item_data)

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # تحديث مؤشر التعديل
                self.on_data_change()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نسخ الصنف: {str(e)}")

    def on_item_select(self, event):
        """عند اختيار صنف"""
        selection = self.items_tree.selection()
        if selection:
            item = selection[0]
            item_text = self.items_tree.item(item, 'text')

            # البحث عن الصنف
            if '(ID:' in item_text:
                try:
                    item_id = item_text.split('(ID:')[1].split(')')[0].strip()

                    for item_data in self.items_data:
                        if str(item_data.get('id', '')) == str(item_id):
                            self.selected_item = item_data
                            break
                except:
                    self.selected_item = None

    def on_item_double_click(self, event):
        """عند النقر المزدوج على صنف"""
        if self.selected_item:
            self.edit_item()

    def show_items_context_menu(self, event):
        """إظهار قائمة السياق للأصناف"""
        context_menu = tk.Menu(self.root, tearoff=0)

        context_menu.add_command(label="➕ إضافة صنف", command=self.add_item)
        context_menu.add_separator()
        context_menu.add_command(label="✏️ تعديل", command=self.edit_item)
        context_menu.add_command(label="📋 نسخ", command=self.duplicate_item)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_item)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def update_items_tree(self):
        """تحديث جدول الأصناف"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة الأصناف للجدول
        for i, item_data in enumerate(self.items_data):
            self.add_item_to_tree(item_data, i + 1)

    def add_item_to_tree(self, item_data, index):
        """إضافة صنف للجدول"""
        try:
            # تنسيق البيانات
            values = [
                item_data.get('item_code', ''),
                item_data.get('item_name', ''),
                item_data.get('description', ''),
                str(item_data.get('quantity', 0)),
                item_data.get('unit', ''),
                f"{float(item_data.get('unit_price', 0)):,.2f}",
                f"{float(item_data.get('total_price', 0)):,.2f}",
                f"{float(item_data.get('weight', 0)):,.2f}",
                f"{float(item_data.get('volume', 0)):,.2f}",
                item_data.get('hs_code', ''),
                item_data.get('origin_country', '')
            ]

            # تحديد لون الصف
            tags = []

            # لون متناوب
            if index % 2 == 0:
                tags.append('even_row')
            else:
                tags.append('odd_row')

            # لون خاص للقيم العالية
            total_price = float(item_data.get('total_price', 0))
            if total_price > 10000:
                tags.append('high_value')

            # إدراج العنصر
            item = self.items_tree.insert(
                '', 'end',
                text=f"{index} (ID: {item_data.get('id', '')})",
                values=values,
                tags=tags
            )

        except Exception as e:
            print(f"خطأ في إضافة صنف للجدول: {e}")

    def update_items_summary(self):
        """تحديث ملخص الأصناف"""
        try:
            total_items = len(self.items_data)
            total_quantity = sum(float(item.get('quantity', 0)) for item in self.items_data)
            total_weight = sum(float(item.get('weight', 0)) for item in self.items_data)
            total_volume = sum(float(item.get('volume', 0)) for item in self.items_data)
            total_value = sum(float(item.get('total_price', 0)) for item in self.items_data)
            avg_price = total_value / total_items if total_items > 0 else 0

            # تحديث التسميات
            self.total_items_label.configure(text=f"إجمالي الأصناف: {total_items}")
            self.total_quantity_label.configure(text=f"إجمالي الكمية: {total_quantity:,.0f}")
            self.total_weight_label.configure(text=f"إجمالي الوزن: {total_weight:,.2f} كجم")
            self.total_volume_label.configure(text=f"إجمالي الحجم: {total_volume:,.2f} م³")
            self.total_value_label.configure(text=f"إجمالي القيمة: {total_value:,.2f}")
            self.avg_price_label.configure(text=f"متوسط السعر: {avg_price:,.2f}")

            # تحديث القيمة الإجمالية في القسم المالي إذا كان موجوداً
            if hasattr(self, 'form_vars') and 'total_value' in self.form_vars:
                self.form_vars['total_value'].set(f"{total_value:.2f}")

        except Exception as e:
            print(f"خطأ في تحديث ملخص الأصناف: {e}")

    def show_item_search(self, event=None):
        """إظهار نافذة البحث في الأصناف عند الضغط على F9"""
        try:
            # التحقق من أننا في قسم الأصناف
            if self.current_section != 1:  # قسم الأصناف هو القسم رقم 1
                messagebox.showinfo("تنبيه", "البحث في الأصناف متاح فقط في قسم الأصناف")
                return

            # إنشاء نافذة البحث
            search_dialog = ItemSearchDialog(self.root, self.items_data, self)
            self.root.wait_window(search_dialog.root)

            # إذا تم اختيار صنف، تحديد الصنف في الجدول
            if hasattr(search_dialog, 'selected_item') and search_dialog.selected_item:
                self.highlight_item_in_tree(search_dialog.selected_item)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")

    def highlight_item_in_tree(self, item_data):
        """تمييز صنف في الجدول"""
        try:
            # البحث عن الصنف في الجدول
            for item in self.items_tree.get_children():
                item_text = self.items_tree.item(item, 'text')
                if f"ID: {item_data.get('id')}" in item_text:
                    # تحديد الصنف
                    self.items_tree.selection_set(item)
                    self.items_tree.focus(item)
                    self.items_tree.see(item)

                    # تحديث الصنف المحدد
                    self.selected_item = item_data
                    break

        except Exception as e:
            print(f"خطأ في تمييز الصنف: {e}")

    def show_supplier_search(self, event=None):
        """إظهار نافذة البحث في الموردين عند الضغط على F9"""
        try:
            # التحقق من أننا في القسم الأساسي
            if self.current_section != 0:  # القسم الأساسي هو القسم رقم 0
                messagebox.showinfo("تنبيه", "البحث في الموردين متاح فقط في القسم الأساسي")
                return

            # إنشاء نافذة البحث في الموردين
            from src.supplier_search_dialog import SupplierSearchDialog
            search_dialog = SupplierSearchDialog(self.root, self)
            self.root.wait_window(search_dialog.root)

            # إذا تم اختيار مورد، تحديث حقل المورد
            if hasattr(search_dialog, 'selected_supplier') and search_dialog.selected_supplier:
                supplier_data = search_dialog.selected_supplier
                # تحديث القائمة المنسدلة
                supplier_name = str(supplier_data.get('name', ''))
                supplier_contact = str(supplier_data.get('contact_info', ''))

                # إذا كانت معلومات الاتصال متوفرة وليست "غير محدد"
                if supplier_contact and supplier_contact != 'غير محدد':
                    supplier_text = f"{supplier_name} - {supplier_contact}"
                else:
                    supplier_text = supplier_name

                self.form_vars['supplier_id'].set(supplier_text)

                # التركيز على الحقل التالي (تاريخ الشحن)
                if hasattr(self, 'shipment_date_entry'):
                    self.shipment_date_entry.focus_set()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث في الموردين: {str(e)}")

    # وظائف الأحداث والتفاعل
    def bind_events(self):
        """ربط الأحداث ومفاتيح الاختصار المحسنة"""
        # اختصارات لوحة المفاتيح الأساسية
        self.root.bind('<Control-s>', lambda e: self.save_shipment())
        self.root.bind('<Control-w>', lambda e: self.close_form())
        self.root.bind('<Escape>', lambda e: self.exit_fullscreen())
        self.root.bind('<F1>', lambda e: self.show_help())
        self.root.bind('<F2>', lambda e: self.show_status_info())
        self.root.bind('<F5>', lambda e: self.preview_shipment())
        self.root.bind('<F9>', lambda e: self.focus_item_search())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())

        # اختصارات الحفظ المتقدمة
        self.root.bind('<Control-Shift-s>', lambda e: self.save_and_close())
        self.root.bind('<Alt-s>', lambda e: self.save_as_draft())

        # أحداث التنقل بين الأقسام
        self.root.bind('<Control-Right>', lambda e: self.next_section())
        self.root.bind('<Control-Left>', lambda e: self.prev_section())
        self.root.bind('<Page_Down>', lambda e: self.next_section())
        self.root.bind('<Page_Up>', lambda e: self.prev_section())
        self.root.bind('<Control-Home>', lambda e: self.switch_section(0))
        self.root.bind('<Control-End>', lambda e: self.switch_section(len(self.sections)-1))

        # أحداث الأرقام للانتقال المباشر للأقسام
        for i in range(1, 9):
            self.root.bind(f'<Control-Key-{i}>', lambda e, s=i-1: self.switch_section(s))

        # اختصارات إضافية مفيدة
        self.root.bind('<Control-n>', lambda e: self.clear_form())
        self.root.bind('<Control-d>', lambda e: self.duplicate_shipment())
        self.root.bind('<Control-p>', lambda e: self.print_shipment())
        self.root.bind('<Control-e>', lambda e: self.export_shipment())

    def focus_item_search(self):
        """التركيز على بحث الأصناف (F9)"""
        try:
            # التبديل إلى قسم الأصناف إذا لم نكن فيه
            if self.current_section != 1:  # قسم الأصناف هو الثاني (index 1)
                self.switch_section(1)

            # محاولة التركيز على حقل البحث في جدول الأصناف
            if hasattr(self, 'items_table') and self.items_table:
                self.items_table.focus_set()
                messagebox.showinfo("بحث الأصناف", "اضغط F9 مرة أخرى في جدول الأصناف للبحث")
        except Exception as e:
            print(f"خطأ في التركيز على بحث الأصناف: {e}")

    def save_as_draft(self):
        """حفظ كمسودة (Alt+S)"""
        try:
            # تغيير الحالة إلى مسودة مؤقتاً
            original_status = self.form_vars.get('status', tk.StringVar()).get()
            self.form_vars['status'].set('مسودة')

            # حفظ الشحنة
            result = self.save_shipment()

            # إرجاع الحالة الأصلية
            self.form_vars['status'].set(original_status)

            if result:
                messagebox.showinfo("حفظ المسودة", "تم حفظ الشحنة كمسودة بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ المسودة: {str(e)}")

    def clear_form(self):
        """مسح النموذج (Ctrl+N)"""
        if messagebox.askyesno("مسح النموذج", "هل تريد مسح جميع البيانات وبدء نموذج جديد؟"):
            try:
                # مسح جميع المتغيرات
                for var in self.form_vars.values():
                    if isinstance(var, tk.StringVar):
                        var.set("")
                    elif isinstance(var, tk.IntVar):
                        var.set(0)
                    elif isinstance(var, tk.DoubleVar):
                        var.set(0.0)

                # مسح جدول الأصناف
                if hasattr(self, 'items_table'):
                    for item in self.items_table.get_children():
                        self.items_table.delete(item)

                # إعادة تعيين الحالة
                self.is_modified = False
                self.update_status("تم مسح النموذج - جاهز لإدخال بيانات جديدة")

                # العودة للقسم الأول
                self.switch_section(0)

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في مسح النموذج: {str(e)}")

    def duplicate_shipment(self):
        """نسخ الشحنة (Ctrl+D)"""
        if messagebox.askyesno("نسخ الشحنة", "هل تريد إنشاء نسخة من الشحنة الحالية؟"):
            try:
                # إنشاء رقم شحنة جديد
                new_shipment_number = f"SH-{datetime.now().strftime('%Y%m%d%H%M%S')}"
                self.form_vars['shipment_number'].set(new_shipment_number)

                # تغيير الحالة إلى جديدة
                self.form_vars['status'].set('في الانتظار')

                # مسح التواريخ المحددة
                self.form_vars['shipment_date'].set("")
                self.form_vars['expected_arrival'].set("")

                self.is_modified = True
                self.update_status("تم إنشاء نسخة من الشحنة - يرجى مراجعة البيانات")

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في نسخ الشحنة: {str(e)}")

    def print_shipment(self):
        """طباعة الشحنة (Ctrl+P)"""
        messagebox.showinfo("طباعة", "ميزة الطباعة ستكون متاحة قريباً")

    def export_shipment(self):
        """تصدير الشحنة (Ctrl+E)"""
        messagebox.showinfo("تصدير", "ميزة التصدير ستكون متاحة قريباً")

    def preview_shipment(self):
        """معاينة الشحنة (F5)"""
        try:
            # جمع البيانات الحالية
            shipment_data = {}
            for key, var in self.form_vars.items():
                if isinstance(var, (tk.StringVar, tk.IntVar, tk.DoubleVar)):
                    shipment_data[key] = var.get()

            # بناء نص المعاينة
            preview_text = f"""
🚢 معاينة بيانات الشحنة

📋 المعلومات الأساسية:
• رقم الشحنة: {shipment_data.get('shipment_number', 'غير محدد')}
• المورد: {shipment_data.get('supplier', 'غير محدد')}
• الحالة: {shipment_data.get('status', 'غير محدد')}
• حالة الإفراج: {shipment_data.get('release_status', 'غير محدد')}
• رقم فاتورة المورد: {shipment_data.get('supplier_invoice_number', 'غير محدد')}

📅 التواريخ:
• تاريخ الشحن: {shipment_data.get('shipment_date', 'غير محدد')}
• التاريخ المتوقع للوصول: {shipment_data.get('expected_arrival', 'غير محدد')}

🚢 معلومات الشحن:
• ميناء المغادرة: {shipment_data.get('departure_port', 'غير محدد')}
• ميناء الوصول: {shipment_data.get('arrival_port', 'غير محدد')}
• شركة الشحن: {shipment_data.get('shipping_company', 'غير محدد')}

📦 معلومات الحاوية:
• عدد الحاويات: {shipment_data.get('container_count', 0)}
• نوع الحاوية: {shipment_data.get('container_type', 'غير محدد')}

💰 المعلومات المالية:
• إجمالي القيمة: {shipment_data.get('total_value', 0)}
• العملة: {shipment_data.get('currency', 'غير محدد')}

📝 الملاحظات:
{shipment_data.get('notes', 'لا توجد ملاحظات')}

⚠️ هذه معاينة للبيانات المدخلة حالياً
            """

            # إظهار نافذة المعاينة
            preview_window = tk.Toplevel(self.root)
            preview_window.title("معاينة الشحنة")
            preview_window.geometry("800x600")
            preview_window.configure(bg=COLORS['background'])

            # جعل النافذة في المقدمة
            preview_window.transient(self.root)
            preview_window.grab_set()

            # إنشاء منطقة النص
            text_frame = create_simple_rtl_frame(preview_window)
            text_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # منطقة النص مع شريط التمرير
            text_widget = tk.Text(
                text_frame,
                font=('Segoe UI', 12),
                bg='white',
                fg=COLORS['text_primary'],
                wrap=tk.WORD,
                padx=20,
                pady=20
            )

            scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side='right', fill='both', expand=True)
            scrollbar.pack(side='left', fill='y')

            # إدراج النص
            text_widget.insert('1.0', preview_text)
            text_widget.configure(state='disabled')  # للقراءة فقط

            # أزرار الإجراءات
            buttons_frame = create_simple_rtl_frame(preview_window)
            buttons_frame.pack(fill='x', padx=20, pady=(0, 20))

            close_btn = create_simple_rtl_button(
                buttons_frame,
                text="إغلاق",
                command=preview_window.destroy,
                bg=COLORS['primary'],
                fg='white',
                font=('Segoe UI', 12, 'bold'),
                width=15
            )
            close_btn.pack(side='left', padx=10)

            print_btn = create_simple_rtl_button(
                buttons_frame,
                text="طباعة",
                command=lambda: messagebox.showinfo("طباعة", "ميزة الطباعة ستكون متاحة قريباً"),
                bg=COLORS['info'],
                fg='white',
                font=('Segoe UI', 12, 'bold'),
                width=15
            )
            print_btn.pack(side='left', padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معاينة الشحنة: {str(e)}")

    def update_time(self):
        """تحديث الوقت"""
        try:
            if self.root and self.root.winfo_exists():
                current_time = datetime.now().strftime("📅 %Y/%m/%d - ⏰ %H:%M:%S")
                self.time_label.configure(text=current_time)
                self.root.after(1000, self.update_time)
        except tk.TclError:
            # النافذة تم إغلاقها، توقف عن التحديث
            pass

    # وظائف المرفقات
    def add_general_attachment(self):
        """إضافة مرفق عام"""
        try:
            filename = filedialog.askopenfilename(
                title="اختيار ملف للإرفاق",
                filetypes=[
                    ("PDF files", "*.pdf"),
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("Document files", "*.doc *.docx *.txt"),
                    ("Excel files", "*.xls *.xlsx"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                # إضافة الملف للقائمة
                file_name = os.path.basename(filename)
                self.attachments_list.insert(tk.END, file_name)

                messagebox.showinfo("تم الإرفاق", f"تم إرفاق الملف: {file_name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إرفاق الملف: {str(e)}")

    def add_document_link(self, var_name, document_name):
        """إضافة رابط لمستند معين"""
        try:
            print(f"🔗 محاولة إضافة رابط لـ: {document_name} ({var_name})")

            # نافذة إدخال الرابط
            link_dialog = tk.Toplevel(self.root)
            link_dialog.title(f"إدخال رابط - {document_name}")
            link_dialog.geometry("600x400")
            link_dialog.resizable(True, True)
            link_dialog.transient(self.root)
            link_dialog.grab_set()

            # تطبيق نمط RTL
            link_dialog.configure(bg=COLORS['background'])

            # إطار المحتوى
            content_frame = create_simple_rtl_frame(link_dialog)
            content_frame.pack(fill='both', expand=True, padx=30, pady=30)

            # العنوان
            title_label = create_simple_rtl_label(
                content_frame,
                text=f"🔗 إدخال رابط {document_name}",
                font=('Segoe UI', 18, 'bold'),
                fg=COLORS['primary']
            )
            title_label.pack(anchor='e', pady=(0, 20))

            # تسمية الرابط
            link_label = create_simple_rtl_label(
                content_frame,
                text="🌐 رابط المستند:",
                font=('Segoe UI', 14, 'bold')
            )
            link_label.pack(anchor='e', pady=(0, 10))

            # حقل الرابط
            link_var = tk.StringVar()
            current_link = self.form_vars[var_name].get()
            if current_link:
                link_var.set(current_link)

            link_entry = create_simple_rtl_entry(
                content_frame,
                textvariable=link_var,
                placeholder="https://example.com/document.pdf"
            )
            link_entry.configure(font=('Segoe UI', 12, 'normal'))
            link_entry.pack(fill='x', ipady=10, pady=(0, 20))
            link_entry.focus()

            # تسمية الوصف
            desc_label = create_simple_rtl_label(
                content_frame,
                text="📝 وصف المستند (اختياري):",
                font=('Segoe UI', 14, 'bold')
            )
            desc_label.pack(anchor='e', pady=(0, 10))

            # حقل الوصف
            desc_var = tk.StringVar()
            desc_entry = create_simple_rtl_entry(
                content_frame,
                textvariable=desc_var,
                placeholder="وصف مختصر للمستند"
            )
            desc_entry.configure(font=('Segoe UI', 12, 'normal'))
            desc_entry.pack(fill='x', ipady=10, pady=(0, 20))

            # إطار الأزرار
            buttons_frame = create_simple_rtl_frame(content_frame)
            buttons_frame.pack(fill='x', pady=(20, 0))

            def save_link():
                """حفظ الرابط"""
                link_url = link_var.get().strip()
                description = desc_var.get().strip()

                if not link_url:
                    messagebox.showwarning("تحذير", "يرجى إدخال رابط المستند")
                    return

                # التحقق من صحة الرابط
                if not (link_url.startswith('http://') or link_url.startswith('https://') or link_url.startswith('ftp://')):
                    if not link_url.startswith('www.'):
                        link_url = 'https://' + link_url
                    else:
                        link_url = 'https://' + link_url

                # حفظ الرابط
                self.form_vars[var_name].set(link_url)
                self.document_links[var_name] = {
                    'url': link_url,
                    'description': description,
                    'added_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }

                # تحديث الحقل readonly
                if hasattr(self, 'link_entries') and var_name in self.link_entries:
                    entry = self.link_entries[var_name]
                    entry.configure(state='normal')  # تمكين التحديث مؤقتاً
                    entry.delete(0, tk.END)
                    entry.insert(0, link_url)
                    entry.configure(state='readonly')  # إعادة تعيين للقراءة فقط

                # تحديث معلومات الرابط
                self.update_link_info_display(var_name, document_name)

                print(f"✅ تم حفظ الرابط لـ: {document_name}")
                messagebox.showinfo("تم الحفظ", f"تم حفظ رابط {document_name} بنجاح")
                link_dialog.destroy()

            def test_link():
                """اختبار الرابط"""
                link_url = link_var.get().strip()
                if link_url:
                    self.open_link_in_browser(link_url)
                else:
                    messagebox.showwarning("تحذير", "يرجى إدخال رابط أولاً")

            # زر الحفظ
            save_btn = create_simple_rtl_button(
                buttons_frame,
                text="💾 حفظ الرابط",
                button_type="primary",
                command=save_link
            )
            save_btn.pack(side='left', padx=(0, 10))

            # زر الاختبار
            test_btn = create_simple_rtl_button(
                buttons_frame,
                text="🧪 اختبار الرابط",
                button_type="secondary",
                command=test_link
            )
            test_btn.pack(side='left', padx=(0, 10))

            # زر الإلغاء
            cancel_btn = create_simple_rtl_button(
                buttons_frame,
                text="❌ إلغاء",
                button_type="ghost",
                command=link_dialog.destroy
            )
            cancel_btn.pack(side='right')

            # ربط مفتاح Enter بالحفظ
            link_entry.bind('<Return>', lambda e: save_link())
            desc_entry.bind('<Return>', lambda e: save_link())

        except Exception as e:
            print(f"❌ خطأ في إضافة الرابط: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"خطأ في إضافة الرابط: {str(e)}")

    def open_document_link(self, var_name, document_name):
        """فتح رابط المستند"""
        try:
            link_url = self.form_vars[var_name].get().strip()

            if not link_url:
                messagebox.showinfo("لا يوجد رابط", f"لا يوجد رابط محفوظ لـ {document_name}")
                return

            print(f"🔗 فتح رابط {document_name}: {link_url}")
            self.open_link_in_browser(link_url)

        except Exception as e:
            print(f"❌ خطأ في فتح الرابط: {e}")
            messagebox.showerror("خطأ", f"خطأ في فتح الرابط: {str(e)}")

    def open_link_in_browser(self, url):
        """فتح الرابط في المتصفح"""
        try:
            import webbrowser

            # التأكد من وجود بروتوكول
            if not (url.startswith('http://') or url.startswith('https://') or url.startswith('ftp://')):
                url = 'https://' + url

            webbrowser.open(url)
            print(f"✅ تم فتح الرابط: {url}")

        except Exception as e:
            print(f"❌ خطأ في فتح المتصفح: {e}")
            messagebox.showerror("خطأ", f"خطأ في فتح الرابط في المتصفح: {str(e)}")

    def update_link_info_display(self, var_name, document_name):
        """تحديث عرض معلومات الرابط"""
        try:
            link_info_label = getattr(self, f'{var_name}_link_info_label', None)
            if not link_info_label:
                return

            if var_name in self.document_links and self.document_links[var_name]:
                link_data = self.document_links[var_name]
                url = link_data.get('url', '')
                description = link_data.get('description', '')
                added_date = link_data.get('added_date', '')

                # عرض معلومات مختصرة
                display_url = url[:50] + "..." if len(url) > 50 else url
                info_text = f"🔗 {display_url}"

                if description:
                    info_text += f" | 📝 {description}"

                if added_date:
                    info_text += f" | 📅 {added_date}"

                link_info_label.configure(text=info_text, fg='#2563eb')
            else:
                link_info_label.configure(text="", fg='#6b7280')

        except Exception as e:
            print(f"❌ خطأ في تحديث معلومات الرابط: {e}")

    def view_attachments(self):
        """عرض المرفقات"""
        attachments = list(self.attachments_list.get(0, tk.END))

        if not attachments:
            messagebox.showinfo("لا توجد مرفقات", "لا توجد ملفات مرفقة")
            return

        attachments_text = "\n".join(f"• {att}" for att in attachments)
        messagebox.showinfo("المرفقات", f"الملفات المرفقة:\n\n{attachments_text}")

    def remove_attachment(self):
        """حذف مرفق"""
        selection = self.attachments_list.curselection()
        if selection:
            self.attachments_list.delete(selection[0])
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للحذف")

    def show_status_info(self):
        """عرض معلومات الحالة المحسنة مع تفاصيل شاملة"""
        try:
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            if not current_status:
                current_status = "غير محدد"

            # معلومات الحالة الأساسية
            status_info = self.get_status_info_message(current_status)
            required_sections = self.get_required_sections_by_status(current_status)

            # إحصائيات النموذج
            total_sections = len(self.sections)
            completed_sections = sum(1 for i in range(total_sections) if self.is_section_complete(i))
            required_sections_count = len(required_sections)
            completed_required = sum(1 for i in required_sections if self.is_section_complete(i))

            # معلومات المستخدم والوقت
            user_info = auth_manager.current_user.get('username', 'غير معروف')
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # بناء رسالة المعلومات الشاملة
            info_message = f"""
🚢 معلومات حالة الشحنة - التفاصيل الكاملة

📊 الإحصائيات العامة:
• الحالة الحالية: {current_status}
• إجمالي الأقسام: {total_sections}
• الأقسام المكتملة: {completed_sections} من {total_sections}
• الأقسام المطلوبة: {required_sections_count}
• المطلوبة المكتملة: {completed_required} من {required_sections_count}
• نسبة الإنجاز: {(completed_sections/total_sections)*100:.1f}%

👤 معلومات الجلسة:
• المستخدم: {user_info}
• الوقت الحالي: {current_time}
• وضع العرض: {"ملء الشاشة" if self.is_fullscreen else "نافذة عادية"}

ℹ️ معلومات الحالة:
{status_info}

📋 حالة الأقسام:

            # أسماء الأقسام
            section_names = [
                "📋 المعلومات الأساسية",
                "📦 الأصناف",
                "🚢 معلومات الشحن",
                "📦 معلومات الحاوية",
                "💰 المعلومات المالية",
                "📄 المستندات",
                "📊 التتبع والحالة",
                "📝 الملاحظات والمرفقات"
            ]

            # إضافة تفاصيل كل قسم
            for i, name in enumerate(section_names):
                status_icon = "✅" if self.is_section_complete(i) else "⏳"
                required_icon = "🔴" if i in required_sections else "🔵"
                completion_status = "مكتمل" if self.is_section_complete(i) else "غير مكتمل"
                requirement_status = "مطلوب" if i in required_sections else "اختياري"

                info_message += f"{status_icon} {required_icon} {name} - {completion_status} ({requirement_status})\n"

            info_message += """

مفاتيح الاختصار السريعة:
• F11: تبديل ملء الشاشة
• Ctrl+S: حفظ سريع
• F9: بحث الأصناف
• Ctrl+يمين/يسار: التنقل بين الأقسام
• F1: المساعدة الكاملة

نصائح مفيدة:
• استخدم Tab للتنقل السريع بين الحقول
• الأقسام المطلوبة تظهر بالأحمر
• الأقسام الاختيارية تظهر بالأزرق
• يمكن حفظ الشحنة بملء الأقسام المطلوبة فقط
            """

            messagebox.showinfo("معلومات الحالة التفصيلية", info_message)

        except Exception as e:
            print(f"خطأ في عرض معلومات الحالة: {e}")

    def get_status_info_message(self, status):
        """الحصول على رسالة معلومات الحالة"""
        status_info = {
            'تحت الطلب': {
                'message': 'حالة "تحت الطلب" - يكفي ملء البيانات الأساسية فقط',
                'sections': 'الأقسام المطلوبة: المعلومات الأساسية',
                'color': '#FFA500'
            },
            'في الانتظار': {
                'message': 'حالة "في الانتظار" - يكفي ملء البيانات الأساسية',
                'sections': 'الأقسام المطلوبة: المعلومات الأساسية',
                'color': '#4CAF50'
            },
            'مؤكدة': {
                'message': 'حالة "مؤكدة" - مطلوب البيانات الأساسية والأصناف',
                'sections': 'الأقسام المطلوبة: المعلومات الأساسية، الأصناف',
                'color': '#2196F3'
            },
            'تم الشحن': {
                'message': 'حالة "تم الشحن" - مطلوب البيانات الأساسية والأصناف ومعلومات الشحن والحاوية والمالية والمستندات',
                'sections': 'الأقسام المطلوبة: الأساسية، الأصناف، الشحن، الحاوية، المالية، المستندات',
                'color': '#FF9800'
            },
            'في الطريق': {
                'message': 'حالة "في الطريق" - مطلوب معظم البيانات للتتبع',
                'sections': 'الأقسام المطلوبة: الأساسية، الأصناف، الشحن، الحاوية، التتبع',
                'color': '#9C27B0'
            },
            'وصلت': {
                'message': 'حالة "وصلت" - مطلوب البيانات المالية والتتبع',
                'sections': 'الأقسام المطلوبة: الأساسية، الأصناف، الشحن، الحاوية، المالية، التتبع',
                'color': '#607D8B'
            },
            'في الجمارك': {
                'message': 'حالة "في الجمارك" - مطلوب جميع البيانات والمستندات',
                'sections': 'الأقسام المطلوبة: جميع الأقسام عدا الملاحظات',
                'color': '#795548'
            },
            'تم التسليم': {
                'message': 'حالة "تم التسليم" - مطلوب جميع البيانات مكتملة',
                'sections': 'الأقسام المطلوبة: جميع الأقسام',
                'color': '#4CAF50'
            }
        }

        return status_info.get(status, {
            'message': 'حالة غير محددة - يرجى ملء البيانات الأساسية',
            'sections': 'الأقسام المطلوبة: المعلومات الأساسية',
            'color': '#757575'
        })

    # وظائف الحفظ والإدارة
    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return False

            # جمع البيانات
            shipment_data = self.collect_form_data()

            # حفظ في قاعدة البيانات
            if self.mode == 'add' or self.mode == 'duplicate':
                self.create_new_shipment(shipment_data)
            elif self.mode == 'edit':
                self.update_existing_shipment(shipment_data)

            # تحديث المؤشرات
            self.is_modified = False
            self.modified_indicator.configure(text="✓ تم الحفظ", fg=COLORS['success'])
            self.status_label.configure(text="تم الحفظ بنجاح")

            # تحديث آخر تحديث
            self.last_update_display.configure(text=datetime.now().strftime('%Y/%m/%d %H:%M:%S'))

            # رسالة نجاح مخصصة حسب الحالة
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            required_sections = self.get_required_sections_by_status(current_status)
            section_names = [
                "المعلومات الأساسية", "الأصناف", "معلومات الشحن", "معلومات الحاوية",
                "المعلومات المالية", "المستندات", "التتبع والحالة", "الملاحظات والمرفقات"
            ]
            saved_sections = [section_names[i] for i in required_sections]

            success_message = f"""✅ تم حفظ الشحنة بنجاح!

🔄 الحالة: {current_status}
📋 الأقسام المحفوظة: {', '.join(saved_sections)}

💡 يمكنك إضافة المزيد من التفاصيل لاحقاً عند تغيير حالة الشحنة."""

            messagebox.showinfo("تم الحفظ بنجاح", success_message)
            return True

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الشحنة: {str(e)}")
            return False

    def get_required_fields_by_status(self, status):
        """تحديد الحقول المطلوبة حسب حالة الشحنة"""
        # الحقول الأساسية المطلوبة دائماً
        basic_required = {
            'shipment_number': 'رقم الشحنة',
            'supplier_id': 'المورد',
            'status': 'حالة الشحنة'
        }

        # الحقول المطلوبة حسب الحالة
        status_requirements = {
            'تحت الطلب': {
                # فقط الحقول الأساسية
                **basic_required
            },
            'في الانتظار': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن'
            },
            'مؤكدة': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع'
            },
            'تم الشحن': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'container_type': 'نوع الحاوية',
                'total_value': 'القيمة الإجمالية',
                'payment_method': 'طريقة الدفع',
                'invoice_document': 'فاتورة المورد',
                'packing_list': 'قائمة التعبئة'
            },
            'في الطريق': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'tracking_number': 'رقم التتبع'
            },
            'وصلت': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'actual_arrival_date': 'تاريخ الوصول الفعلي',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية'
            },
            'في الجمارك': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'actual_arrival_date': 'تاريخ الوصول الفعلي',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'bill_of_lading': 'بوليصة الشحن'
            },
            'تم التسليم': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'actual_arrival_date': 'تاريخ الوصول الفعلي',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'bill_of_lading': 'بوليصة الشحن',
                'total_value': 'القيمة الإجمالية'
            }
        }

        # إرجاع الحقول المطلوبة للحالة المحددة أو الحقول الأساسية
        return status_requirements.get(status, basic_required)

    def get_required_sections_by_status(self, status):
        """تحديد الأقسام المطلوبة حسب حالة الشحنة"""
        section_requirements = {
            'تحت الطلب': [0],  # فقط القسم الأساسي
            'في الانتظار': [0],  # فقط القسم الأساسي
            'مؤكدة': [0, 1],  # الأساسي + الأصناف
            'تم الشحن': [0, 1, 2, 3, 4, 5],  # الأساسي + الأصناف + الشحن + الحاوية + المالية + المستندات
            'في الطريق': [0, 1, 2, 3, 6],  # الأساسي + الأصناف + الشحن + الحاوية + التتبع
            'وصلت': [0, 1, 2, 3, 4, 6],  # الأساسي + الأصناف + الشحن + الحاوية + المالية + التتبع
            'في الجمارك': [0, 1, 2, 3, 4, 5, 6],  # جميع الأقسام عدا الملاحظات
            'تم التسليم': [0, 1, 2, 3, 4, 5, 6, 7],  # جميع الأقسام
            'ملغية': [0, 7],  # الأساسي + الملاحظات
            'متأخرة': [0, 1, 2, 6, 7]  # الأساسي + الأصناف + الشحن + التتبع + الملاحظات
        }

        return section_requirements.get(status, [0])  # افتراضياً القسم الأساسي فقط

    def validate_form(self):
        """التحقق من صحة النموذج بناءً على حالة الشحنة"""
        errors = []

        # الحصول على حالة الشحنة الحالية
        current_status = self.form_vars.get('status', tk.StringVar()).get().strip()

        # الحصول على الحقول المطلوبة للحالة الحالية
        required_fields = self.get_required_fields_by_status(current_status)

        # التحقق من الحقول المطلوبة
        for field, label in required_fields.items():
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if not value:
                errors.append(f"حقل {label} مطلوب لحالة '{current_status}'")

        # التحقق من صحة التواريخ (فقط للحقول المملوءة)
        date_fields = ['shipment_date', 'expected_arrival_date', 'actual_departure_date', 'actual_arrival_date']
        for field in date_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_date(value):
                errors.append(f"تاريخ {field} غير صحيح (استخدم YYYY-MM-DD)")

        # التحقق من صحة الأرقام (فقط للحقول المملوءة)
        numeric_fields = ['total_value', 'shipping_cost', 'weight', 'volume', 'pieces_count']
        for field in numeric_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_number(value):
                errors.append(f"قيمة {field} يجب أن تكون رقماً")

        # عرض الأخطاء إن وجدت
        if errors:
            error_message = f"يرجى تصحيح الأخطاء التالية لحالة '{current_status}':\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("أخطاء في البيانات", error_message)
            return False

        return True

    def is_valid_date(self, date_string):
        """التحقق من صحة التاريخ"""
        try:
            datetime.strptime(date_string, '%Y-%m-%d')
            return True
        except:
            return False

    def is_valid_number(self, number_string):
        """التحقق من صحة الرقم"""
        try:
            float(number_string)
            return True
        except:
            return False

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        data = {}

        # جمع البيانات من المتغيرات
        for field, var in self.form_vars.items():
            value = var.get().strip()
            data[field] = value if value else None

        # معالجة خاصة للمورد
        supplier_text = self.form_vars.get('supplier_id', tk.StringVar()).get()
        if supplier_text and '(ID:' in supplier_text:
            try:
                supplier_id = int(supplier_text.split('(ID:')[1].split(')')[0].strip())
                data['supplier_id'] = supplier_id
            except:
                data['supplier_id'] = None

        # جمع الملاحظات
        notes = self.notes_text.get('1.0', tk.END).strip()
        data['notes'] = notes if notes else None

        # جمع وصف البضائع
        goods_desc = self.goods_description_text.get('1.0', tk.END).strip()
        data['goods_description'] = goods_desc if goods_desc else None

        # جمع ملاحظات المستندات
        if hasattr(self, 'documents_notes_text'):
            docs_notes = self.documents_notes_text.get('1.0', tk.END).strip()
            data['documents_notes'] = docs_notes if docs_notes else None

        # جمع المرفقات
        attachments = list(self.attachments_list.get(0, tk.END))
        data['attachments'] = json.dumps(attachments) if attachments else None

        # معالجة خاصة لأرقام الحاويات المتعددة
        container_numbers = []
        container_count = int(data.get('container_count', 1) or 1)

        for i in range(1, container_count + 1):
            var_name = f'container_number_{i}' if i > 1 else 'container_number'
            container_num = data.get(var_name, '').strip()
            if container_num and container_num != "XXXX-XXXXXXX-X":
                container_numbers.append(container_num)

        # حفظ أرقام الحاويات كـ JSON
        data['container_numbers_json'] = json.dumps(container_numbers) if container_numbers else None
        # الاحتفاظ بالحقل الأول للتوافق مع النظام الحالي
        data['container_number'] = container_numbers[0] if container_numbers else None

        # إضافة معلومات النظام
        current_user = auth_manager.get_current_user()
        data['created_by'] = current_user.get('username', 'system')
        data['updated_at'] = datetime.now().isoformat()

        if self.mode == 'add' or self.mode == 'duplicate':
            data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.now().isoformat()

        return data

    def create_new_shipment(self, data):
        """إنشاء شحنة جديدة"""
        query = """
            INSERT INTO advanced_shipments (
                shipment_number, customer_name, supplier_name, supplier_invoice_number, origin_port, destination_port,
                shipment_date, expected_arrival, status, priority, total_value, currency,
                container_type, container_number, container_count, container_numbers_json, seal_number,
                weight, volume, items_count, tracking_number, shipping_line, vessel_name, voyage_number,
                bill_of_lading, customs_status, release_status, insurance_value, freight_cost, other_charges,
                notes, documents_json, created_by, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
        """

        params = (
            data.get('shipment_number'),
            data.get('customer_name', ''),  # اسم العميل
            data.get('supplier_name', ''),  # اسم المورد
            data.get('supplier_invoice_number', ''),  # رقم فاتورة المورد
            data.get('departure_port', ''),  # ميناء المنشأ
            data.get('arrival_port', ''),    # ميناء الوصول
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('status', 'قيد التحضير'),
            data.get('priority', 'عادي'),
            data.get('total_value', 0),
            data.get('currency', 'USD'),
            data.get('container_type'),
            data.get('container_number'),
            data.get('container_count', 1),
            data.get('container_numbers_json'),
            data.get('seal_number'),
            data.get('weight', 0),
            data.get('volume', 0),
            data.get('items_count', 0),
            data.get('tracking_number'),
            data.get('shipping_company', ''),  # خط الشحن
            data.get('vessel_name'),
            data.get('voyage_number'),
            data.get('bill_of_lading'),
            data.get('customs_status', 'لم يتم التخليص'),
            data.get('release_status', 'بدون إفراج'),  # حالة الإفراج الجديدة
            data.get('insurance_cost', 0),
            data.get('shipping_cost', 0),
            data.get('additional_fees', 0),
            data.get('notes'),
            data.get('attachments'),  # documents_json
            data.get('created_by'),
            data.get('created_at'),
            data.get('updated_at')
        )

        self.db_manager.execute_query(query, params)

        # حفظ الأصناف
        self.save_shipment_items(data.get('id'))

    def update_existing_shipment(self, data):
        """تحديث شحنة موجودة"""
        query = """
            UPDATE advanced_shipments SET
                shipment_number = ?, customer_name = ?, supplier_name = ?, supplier_invoice_number = ?, origin_port = ?,
                destination_port = ?, shipment_date = ?, expected_arrival = ?, status = ?,
                priority = ?, total_value = ?, currency = ?, container_type = ?,
                container_number = ?, container_count = ?, container_numbers_json = ?, seal_number = ?,
                weight = ?, volume = ?, items_count = ?, tracking_number = ?, shipping_line = ?,
                vessel_name = ?, voyage_number = ?, bill_of_lading = ?, customs_status = ?,
                release_status = ?, insurance_value = ?, freight_cost = ?, other_charges = ?,
                notes = ?, documents_json = ?, updated_at = ?
            WHERE id = ?
        """

        params = (
            data.get('shipment_number'),
            data.get('customer_name', ''),
            data.get('supplier_name', ''),
            data.get('supplier_invoice_number', ''),
            data.get('departure_port', ''),
            data.get('arrival_port', ''),
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('status', 'قيد التحضير'),
            data.get('priority', 'عادي'),
            data.get('total_value', 0),
            data.get('currency', 'USD'),
            data.get('container_type'),
            data.get('container_number'),
            data.get('container_count', 1),
            data.get('container_numbers_json'),
            data.get('seal_number'),
            data.get('weight', 0),
            data.get('volume', 0),
            data.get('items_count', 0),
            data.get('tracking_number'),
            data.get('shipping_company', ''),
            data.get('vessel_name'),
            data.get('voyage_number'),
            data.get('bill_of_lading'),
            data.get('customs_status', 'لم يتم التخليص'),
            data.get('release_status', 'بدون إفراج'),  # حالة الإفراج الجديدة
            data.get('insurance_cost', 0),
            data.get('shipping_cost', 0),
            data.get('additional_fees', 0),
            data.get('notes'),
            data.get('attachments'),
            data.get('updated_at'),
            self.shipment_data.get('id')
        )

        self.db_manager.execute_query(query, params)

        # حفظ الأصناف
        self.save_shipment_items(self.shipment_data.get('id'))

    def save_shipment_items(self, shipment_id):
        """حفظ أصناف الشحنة"""
        try:
            # حذف الأصناف الموجودة
            self.db_manager.execute_query(
                "DELETE FROM shipment_items WHERE shipment_id = ?",
                (shipment_id,)
            )

            # إضافة الأصناف الجديدة
            for item in self.items_data:
                query = """
                    INSERT INTO shipment_items (
                        id, shipment_id, item_code, item_name, description,
                        quantity, unit, unit_price, total_price, weight,
                        volume, hs_code, origin_country, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                params = (
                    item.get('id'),
                    shipment_id,
                    item.get('item_code'),
                    item.get('item_name'),
                    item.get('description'),
                    float(item.get('quantity', 0)),
                    item.get('unit'),
                    float(item.get('unit_price', 0)),
                    float(item.get('total_price', 0)),
                    float(item.get('weight', 0)),
                    float(item.get('volume', 0)),
                    item.get('hs_code'),
                    item.get('origin_country'),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                )

                self.db_manager.execute_query(query, params)

        except Exception as e:
            print(f"خطأ في حفظ أصناف الشحنة: {e}")

    def load_shipment_items(self, shipment_id):
        """تحميل أصناف الشحنة"""
        try:
            query = """
                SELECT * FROM shipment_items
                WHERE shipment_id = ?
                ORDER BY created_at
            """

            items = self.db_manager.fetch_all(query, (shipment_id,))
            self.items_data = []

            for item in items:
                item_data = {
                    'id': item['id'],
                    'item_code': item['item_code'],
                    'item_name': item['item_name'],
                    'description': item['description'],
                    'quantity': str(item['quantity']),
                    'unit': item['unit'],
                    'unit_price': str(item['unit_price']),
                    'total_price': str(item['total_price']),
                    'weight': str(item['weight']),
                    'volume': str(item['volume']),
                    'hs_code': item['hs_code'],
                    'origin_country': item['origin_country']
                }
                self.items_data.append(item_data)

            # تحديث الجدول والملخص
            if hasattr(self, 'items_tree'):
                self.update_items_tree()
                self.update_items_summary()

        except Exception as e:
            print(f"خطأ في تحميل أصناف الشحنة: {e}")

    def save_and_close(self):
        """حفظ وإغلاق"""
        if self.save_shipment():
            self.close_form()

    def close_form(self):
        """إغلاق النموذج"""
        if self.is_modified:
            result = messagebox.askyesnocancel(
                "تأكيد الإغلاق",
                "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟"
            )

            if result is True:  # نعم - احفظ وأغلق
                if not self.save_shipment():
                    return
            elif result is None:  # إلغاء
                return
            # False = لا - أغلق بدون حفظ

        self.root.destroy()

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.close_form()

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        try:
            current_state = self.root.state()
            if current_state == 'zoomed':
                self.root.state('normal')
                self.center_window()
            else:
                self.root.state('zoomed')
        except:
            pass

    def show_help(self):
        """إظهار المساعدة المحسنة مع مفاتيح الاختصار"""
        help_text = """
🚢 نموذج الشحنة - المساعدة الشاملة

⌨️ مفاتيح الاختصار الأساسية:
• F11: تبديل وضع ملء الشاشة
• Ctrl+S: حفظ الشحنة
• Ctrl+Shift+S: حفظ وإغلاق
• Alt+S: حفظ كمسودة
• F5: معاينة الشحنة
• F9: التركيز على بحث الأصناف
• Esc: خروج من ملء الشاشة
• F1: إظهار هذه المساعدة
• F2: معلومات الحالة

التنقل بين الأقسام:
• Ctrl+يمين: القسم التالي
• Ctrl+يسار: القسم السابق
• Page Down: القسم التالي
• Page Up: القسم السابق
• Ctrl+Home: القسم الأول
• Ctrl+End: القسم الأخير
• Ctrl+1-8: الانتقال المباشر للقسم

🛠️ إجراءات إضافية:
• Ctrl+N: مسح النموذج وبدء جديد
• Ctrl+D: نسخ الشحنة الحالية
• Ctrl+P: طباعة الشحنة
• Ctrl+E: تصدير الشحنة

🎯 النظام الديناميكي للحفظ:
• الأقسام المطلوبة تتغير حسب حالة الشحنة
• "تحت الطلب" - يكفي القسم الأساسي فقط
• "مؤكدة" - مطلوب الأساسي + الأصناف
• "تم الشحن" - مطلوب جميع الأقسام الأساسية
• الأقسام غير المطلوبة تظهر باللون الرمادي

📋 الأقسام المتاحة:
1. المعلومات الأساسية: رقم الشحنة، المورد، التاريخ، الحالة
2. الأصناف: قائمة الأصناف والمنتجات مع الكميات والأسعار
3. معلومات الشحن: الموانئ، شركة الشحن، بوليصة الشحن
4. معلومات الحاوية: رقم الحاوية، النوع، الوزن، الحجم
5. المعلومات المالية: القيمة، التكاليف، طريقة الدفع
6. المستندات: الفواتير، الشهادات، التراخيص، حالة المستندات
7. التتبع والحالة: رقم التتبع، الموقع، نسبة التقدم
8. الملاحظات والمرفقات: ملاحظات إضافية، ملفات مرفقة

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+S: حفظ الشحنة
• Ctrl+W / Esc: إغلاق النموذج
• F1: هذه المساعدة
• F2: معلومات الحالة والأقسام المطلوبة
• F11: تبديل وضع ملء الشاشة
• Ctrl+يمين / Page Down: القسم التالي
• Ctrl+يسار / Page Up: القسم السابق
• Ctrl+1-8: الانتقال المباشر للقسم
• F9: البحث في الأصناف (في قسم الأصناف) أو البحث في الموردين (في القسم الأساسي)

🎯 التنقل:
• استخدم الأزرار العلوية للانتقال المباشر
• استخدم أزرار التنقل السفلية للتنقل التسلسلي
• الحقول المطلوبة مميزة باللون الأحمر
• الحسابات التلقائية تحدث فورياً

💡 نصائح:
• يتم إنشاء رقم الشحنة تلقائياً
• نسبة التقدم تحدث حسب الحالة
• الأولوية محسوبة تلقائياً
• يمكن إرفاق ملفات متعددة
• البيانات تحفظ تلقائياً عند التبديل
        """

        messagebox.showinfo("مساعدة - نموذج الشحنة", help_text)

    def run(self):
        """تشغيل النموذج"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل النموذج: {e}")

def show_fullscreen_shipment_form(parent, mode='add', shipment_data=None):
    """إظهار نموذج الشحنة بملء الشاشة"""
    form = FullscreenShipmentForm(parent, mode, shipment_data)
    return form

if __name__ == "__main__":
    # اختبار النموذج
    root = tk.Tk()
    root.withdraw()

    form = FullscreenShipmentForm(root, mode='add')
    root.mainloop()
