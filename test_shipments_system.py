#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الشحنات المتقدم
Test Advanced Shipments Management System
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.advanced_shipments_window import AdvancedShipmentsWindow
from src.shipment_form_window import ShipmentFormWindow

def test_shipments_window():
    """اختبار نافذة إدارة الشحنات"""
    print("🧪 بدء اختبار نظام إدارة الشحنات المتقدم...")
    
    try:
        print("✅ إنشاء نافذة إدارة الشحنات...")
        shipments_window = AdvancedShipmentsWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("📋 الميزات المتاحة:")
        print("   • واجهة RTL متقدمة مع دعم كامل للعربية")
        print("   • بحث وتصفية متقدمة")
        print("   • جدول شحنات مع تلوين حسب الحالة")
        print("   • لوحة تفاصيل تفاعلية")
        print("   • شريط أدوات مع 6 أزرار ملونة")
        print("   • تنقل بين الصفحات")
        print("   • اختصارات لوحة المفاتيح")
        
        print("\n🎮 التحكم في النافذة:")
        print("   • Ctrl+N: شحنة جديدة")
        print("   • F2: تعديل الشحنة المحددة")
        print("   • Delete: حذف الشحنة المحددة")
        print("   • Ctrl+F: التركيز على البحث")
        print("   • F5: تحديث البيانات")
        print("   • F1: مساعدة")
        
        print("\n🎨 ألوان الحالات:")
        print("   • أصفر: في الانتظار/في الجمارك")
        print("   • أزرق: مؤكدة/تم الشحن")
        print("   • أخضر: وصلت/تم التسليم")
        print("   • أحمر: ملغية/متأخرة")
        
        print("\n🚀 تشغيل النافذة...")
        shipments_window.root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_shipment_form():
    """اختبار نموذج الشحنة"""
    print("\n🧪 اختبار نموذج الشحنة...")
    
    try:
        print("✅ إنشاء نموذج إضافة شحنة...")
        form_window = ShipmentFormWindow(mode='add')
        
        print("✅ تم إنشاء النموذج بنجاح")
        print("📋 التبويبات المتاحة:")
        print("   • المعلومات الأساسية: رقم الشحنة، المورد، الحالة")
        print("   • معلومات الشحن: التواريخ، الموانئ، شركة الشحن")
        print("   • المعلومات المالية: القيم، التكاليف، طريقة الدفع")
        print("   • الأصناف: جدول الأصناف مع إدارة كاملة")
        print("   • الملاحظات والمرفقات: معلومات إضافية")
        
        print("\n⌨️ اختصارات النموذج:")
        print("   • Ctrl+S: حفظ النموذج")
        print("   • Esc: إلغاء النموذج")
        print("   • F1: مساعدة النموذج")
        
        print("\n🚀 تشغيل النموذج...")
        form_window.root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def demo_features():
    """عرض توضيحي للميزات"""
    print("\n🎨 عرض توضيحي لميزات نظام إدارة الشحنات المتقدم:")
    print("=" * 70)
    
    features = [
        "🎨 تصميم RTL متقدم مع دعم كامل للغة العربية",
        "🔍 بحث وتصفية متقدمة (نص، حالة، مورد، تاريخ)",
        "📋 جدول شحنات مع 10 أعمدة وتلوين حسب الحالة",
        "📊 لوحة تفاصيل تفاعلية مع 4 أقسام",
        "🛠️ شريط أدوات مع 6 أزرار ملونة",
        "📄 تنقل بين الصفحات (20 عنصر/صفحة)",
        "⌨️ اختصارات لوحة المفاتيح شاملة",
        "📝 نموذج شحنة مع 5 تبويبات",
        "📦 إدارة أصناف الشحنة مع جدول منفصل",
        "💰 معلومات مالية شاملة مع عملات متعددة",
        "🚢 معلومات شحن تفصيلية (موانئ، شركات)",
        "📎 نظام مرفقات وملاحظات",
        "🎨 نظام ألوان متقدم (9 حالات ملونة)",
        "🔄 تحديث تلقائي مع مؤشرات الحالة",
        "🖱️ قوائم سياق بالزر الأيمن",
        "📊 تقارير وإحصائيات متقدمة",
        "🛡️ نظام أمان وصلاحيات",
        "💾 حفظ تلقائي مع تحقق من البيانات",
        "📱 تصميم متجاوب مع تأثيرات بصرية",
        "🌐 دعم عملات ومناطق متعددة"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i:2d}. {feature}")
    
    print("\n" + "=" * 70)
    print("💡 نصائح الاستخدام:")
    print("   • استخدم البحث السريع للعثور على الشحنات")
    print("   • انقر على شحنة لعرض التفاصيل الكاملة")
    print("   • استخدم التصفية المتقدمة لتضييق النتائج")
    print("   • انقر نقراً مزدوجاً لتعديل الشحنة")
    print("   • استخدم الزر الأيمن لقائمة الخيارات")
    print("   • اضغط F1 في أي وقت للحصول على المساعدة")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نظام إدارة الشحنات المتقدم")
    print("=" * 60)
    
    # عرض الميزات
    demo_features()
    
    # اختيار نوع الاختبار
    print("\n📋 اختر نوع الاختبار:")
    print("1. اختبار نافذة إدارة الشحنات")
    print("2. اختبار نموذج الشحنة")
    print("3. عرض الميزات فقط")
    print("0. خروج")
    
    try:
        choice = input("\nأدخل اختيارك (1-3): ").strip()
        
        if choice == "1":
            success = test_shipments_window()
            print(f"\n📊 نتيجة الاختبار: {'نجح' if success else 'فشل'}")
            
        elif choice == "2":
            success = test_shipment_form()
            print(f"\n📊 نتيجة الاختبار: {'نجح' if success else 'فشل'}")
            
        elif choice == "3":
            print("\n✅ تم عرض الميزات بنجاح!")
            
        elif choice == "0":
            print("\n👋 تم الخروج من الاختبار")
            
        else:
            print("\n❌ اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    main()
