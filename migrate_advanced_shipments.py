#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقل بيانات الشحنات المتقدمة إلى قاعدة البيانات الجديدة
Migrate advanced shipments data to new database
"""

import sqlite3
import json
from datetime import datetime

def migrate_advanced_shipments():
    """نقل بيانات الشحنات من shipments.db إلى database/shipment_system.db"""
    
    print("🔄 بدء عملية نقل بيانات الشحنات المتقدمة...")
    
    # الاتصال بقاعدة البيانات القديمة
    old_conn = sqlite3.connect('shipments.db')
    old_conn.row_factory = sqlite3.Row
    old_cursor = old_conn.cursor()
    
    # الاتصال بقاعدة البيانات الجديدة
    new_conn = sqlite3.connect('database/shipment_system.db')
    new_cursor = new_conn.cursor()
    
    try:
        # التحقق من وجود الجدول في قاعدة البيانات القديمة
        old_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='advanced_shipments'")
        if not old_cursor.fetchone():
            print("❌ جدول advanced_shipments غير موجود في قاعدة البيانات القديمة")
            return
        
        # جلب جميع البيانات من قاعدة البيانات القديمة
        old_cursor.execute("SELECT * FROM advanced_shipments")
        shipments = old_cursor.fetchall()
        
        print(f"📊 تم العثور على {len(shipments)} شحنة للنقل")
        
        # حذف البيانات الموجودة في قاعدة البيانات الجديدة (إن وجدت)
        new_cursor.execute("DELETE FROM advanced_shipments")
        
        # نقل البيانات
        migrated_count = 0
        for shipment in shipments:
            try:
                # تحويل Row إلى dict
                shipment_dict = dict(shipment)

                # تحضير البيانات للإدراج (الحقول الأساسية فقط)
                insert_query = '''
                    INSERT INTO advanced_shipments (
                        shipment_number, customer_name, supplier_name,
                        container_count, container_numbers_json, container_number,
                        release_status, status, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                '''

                # تحضير القيم
                values = (
                    shipment_dict.get('shipment_number'),
                    shipment_dict.get('customer_name'),
                    shipment_dict.get('supplier_name'),
                    shipment_dict.get('container_count', 1),
                    shipment_dict.get('container_numbers_json'),
                    shipment_dict.get('container_number'),
                    shipment_dict.get('release_status', 'بدون افراج'),
                    shipment_dict.get('status', 'قيد التحضير'),
                    shipment_dict.get('created_at'),
                    shipment_dict.get('updated_at')
                )

                new_cursor.execute(insert_query, values)
                migrated_count += 1

            except Exception as e:
                print(f"❌ خطأ في نقل الشحنة {shipment_dict.get('shipment_number', 'غير محدد')}: {e}")
        
        # حفظ التغييرات
        new_conn.commit()
        
        print(f"✅ تم نقل {migrated_count} شحنة بنجاح")
        print("🎉 اكتملت عملية النقل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في عملية النقل: {e}")
        new_conn.rollback()
        
    finally:
        old_conn.close()
        new_conn.close()

if __name__ == "__main__":
    migrate_advanced_shipments()
