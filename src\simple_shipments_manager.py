#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الشحنات المبسط مع دعم RTL كامل
Simple Shipments Management System with Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime, date
import threading
import time

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES
from database.database_manager import DatabaseManager
from src.simple_rtl_components import *
from src.auth_manager import auth_manager

class SimpleShipmentsManager:
    """نظام إدارة الشحنات المبسط"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.db_manager = DatabaseManager()
        
        # متغيرات البيانات
        self.shipments_data = []
        self.filtered_data = []
        self.selected_shipment = None
        self.current_page = 1
        self.items_per_page = 20
        
        # متغيرات البحث
        self.search_var = tk.StringVar()
        self.status_filter_var = tk.StringVar()
        self.supplier_filter_var = tk.StringVar()
        
        # إعداد النافذة
        self.setup_window()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
        
        # توسيط النافذة
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🚢 نظام إدارة الشحنات المتقدم")
        self.root.geometry("1400x800")
        self.root.configure(bg=COLORS['background'])
        self.root.state('zoomed')  # ملء الشاشة
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Control-n>', lambda e: self.new_shipment())
        self.root.bind('<Control-r>', lambda e: self.refresh_data())
        self.root.bind('<F5>', lambda e: self.refresh_data())
        self.root.bind('<Escape>', lambda e: self.on_closing())
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        self.main_frame = create_simple_rtl_frame(self.root)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header()
        
        # شريط الأدوات
        self.create_toolbar()
        
        # لوحة البحث والتصفية
        self.create_search_panel()
        
        # منطقة الجدول
        self.create_table_area()
        
        # شريط الحالة
        self.create_status_bar()
        
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = create_simple_rtl_frame(self.main_frame)
        header_frame.configure(bg=COLORS['primary'], height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = create_simple_rtl_label(
            header_frame,
            text="🚢 نظام إدارة الشحنات المتقدم والشامل",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['text_white'],
            bg=COLORS['primary']
        )
        title_label.pack(expand=True, pady=20)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = create_simple_rtl_toolbar(self.main_frame)
        self.toolbar.pack(fill='x', pady=(0, 10))
        
        # أزرار الإجراءات الأساسية
        self.toolbar.add_button("➕ شحنة جديدة", self.new_shipment, "success", "📦")
        self.toolbar.add_button("✏️ تعديل", self.edit_shipment, "warning", "✏️")
        self.toolbar.add_button("📋 نسخ", self.duplicate_shipment, "secondary", "📋")
        self.toolbar.add_button("🗑️ حذف", self.delete_shipment, "danger", "🗑️")
        self.toolbar.add_button("🔄 تحديث", self.refresh_data, "primary", "🔄")
        self.toolbar.add_button("📊 تقرير", self.generate_report, "primary", "📊")
        self.toolbar.add_button("📤 تصدير", self.export_data, "ghost", "📤")
        self.toolbar.add_button("❌ إغلاق", self.on_closing, "danger", "❌")
        
    def create_search_panel(self):
        """إنشاء لوحة البحث والتصفية"""
        search_card = create_simple_rtl_card(
            self.main_frame,
            title="🔍 البحث والتصفية المتقدمة"
        )
        search_card.pack(fill='x', pady=(0, 10))
        
        # محتوى البحث
        content_frame = create_simple_rtl_frame(search_card)
        content_frame.pack(fill='x', padx=20, pady=15)
        
        # الصف الأول - البحث العام
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 10))
        
        # البحث العام
        search_frame = create_simple_rtl_frame(row1)
        search_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        search_label = create_simple_rtl_label(
            search_frame,
            text="🔍 البحث العام:",
            font=('Segoe UI', 12, 'bold')
        )
        search_label.pack(anchor='e', pady=(0, 5))
        
        self.search_entry = create_simple_rtl_entry(
            search_frame,
            textvariable=self.search_var,
            placeholder="ابحث في رقم الشحنة، المورد، الحاوية..."
        )
        self.search_entry.pack(fill='x', ipady=8)
        self.search_var.trace('w', self.on_search_change)
        
        # فلتر الحالة
        status_frame = create_simple_rtl_frame(row1)
        status_frame.pack(side='right', padx=(0, 15))
        
        status_label = create_simple_rtl_label(
            status_frame,
            text="📊 حالة الشحنة:",
            font=('Segoe UI', 12, 'bold')
        )
        status_label.pack(anchor='e', pady=(0, 5))
        
        self.status_combo = create_simple_rtl_combobox(
            status_frame,
            textvariable=self.status_filter_var,
            width=20
        )
        self.status_combo['values'] = ["جميع الحالات"] + list(SHIPMENT_STATUS.values())
        self.status_combo.set("جميع الحالات")
        self.status_combo.pack(ipady=8)
        self.status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # فلتر المورد
        supplier_frame = create_simple_rtl_frame(row1)
        supplier_frame.pack(side='right')
        
        supplier_label = create_simple_rtl_label(
            supplier_frame,
            text="🏢 المورد:",
            font=('Segoe UI', 12, 'bold')
        )
        supplier_label.pack(anchor='e', pady=(0, 5))
        
        self.supplier_combo = create_simple_rtl_combobox(
            supplier_frame,
            textvariable=self.supplier_filter_var,
            width=25
        )
        self.supplier_combo.pack(ipady=8)
        self.supplier_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # الصف الثاني - أزرار التحكم
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(10, 0))
        
        # أزرار البحث
        search_btn = create_simple_rtl_button(
            row2,
            text="🔍 بحث",
            button_type="primary",
            command=self.apply_filters
        )
        search_btn.pack(side='right', padx=5)
        
        clear_btn = create_simple_rtl_button(
            row2,
            text="🗑️ مسح",
            button_type="outline",
            command=self.clear_filters
        )
        clear_btn.pack(side='right', padx=5)
        
    def create_table_area(self):
        """إنشاء منطقة الجدول"""
        table_card = create_simple_rtl_card(
            self.main_frame,
            title="📋 قائمة الشحنات"
        )
        table_card.pack(fill='both', expand=True, pady=(0, 10))
        
        # إطار الجدول
        table_frame = create_simple_rtl_frame(table_card)
        table_frame.pack(fill='both', expand=True, padx=20, pady=15)
        
        # تعريف الأعمدة
        columns = [
            ('shipment_number', 'رقم الشحنة', 120),
            ('supplier_name', 'المورد', 150),
            ('status', 'الحالة', 100),
            ('shipment_date', 'تاريخ الشحن', 100),
            ('expected_arrival', 'الوصول المتوقع', 110),
            ('departure_port', 'ميناء المغادرة', 120),
            ('arrival_port', 'ميناء الوصول', 120),
            ('container_number', 'رقم الحاوية', 110),
            ('total_value', 'القيمة الإجمالية', 120),
            ('currency', 'العملة', 60),
            ('payment_status', 'حالة الدفع', 100)
        ]
        
        # إنشاء Treeview
        self.tree = create_simple_rtl_treeview(
            table_frame,
            columns=[col[0] for col in columns],
            show='tree headings',
            height=15
        )
        
        # تكوين الأعمدة
        for col_id, col_name, col_width in columns:
            self.tree.heading(col_id, text=col_name, anchor='e')
            self.tree.column(col_id, width=col_width, anchor='e', minwidth=50)
        
        # تكوين العمود الرئيسي
        self.tree.column('#0', width=30, minwidth=30, anchor='e')
        self.tree.heading('#0', text='#', anchor='e')
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)

        # إعداد ألوان الصفوف
        self.setup_tree_colors()

    def setup_tree_colors(self):
        """إعداد ألوان الجدول"""
        try:
            # ألوان الحالات
            self.tree.tag_configure('in_transit', background='#FEF3C7')     # في الطريق - أصفر فاتح
            self.tree.tag_configure('arrived', background='#D1FAE5')        # وصلت - أخضر فاتح
            self.tree.tag_configure('delivered', background='#BBF7D0')      # تم التسليم - أخضر
            self.tree.tag_configure('delayed', background='#FECACA')        # متأخرة - أحمر فاتح
            self.tree.tag_configure('cancelled', background='#F3F4F6')      # ملغية - رمادي فاتح

            # ألوان الصفوف المتناوبة
            self.tree.tag_configure('odd_row', background='#F9FAFB')
            self.tree.tag_configure('even_row', background='#FFFFFF')
        except Exception as e:
            print(f"خطأ في إعداد ألوان الجدول: {e}")
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = create_simple_rtl_frame(self.main_frame)
        status_frame.configure(bg=COLORS['light'], height=40)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        # معلومات الحالة
        self.status_label = create_simple_rtl_label(
            status_frame,
            text="جاهز",
            bg=COLORS['light']
        )
        self.status_label.pack(side='right', padx=15, pady=10)
        
        self.count_label = create_simple_rtl_label(
            status_frame,
            text="إجمالي الشحنات: 0",
            bg=COLORS['light']
        )
        self.count_label.pack(side='right', padx=10, pady=10)
        
        # أزرار التنقل
        self.prev_btn = create_simple_rtl_button(
            status_frame,
            text="◀ السابق",
            button_type="ghost",
            command=self.prev_page
        )
        self.prev_btn.pack(side='left', padx=5, pady=5)
        
        self.page_label = create_simple_rtl_label(
            status_frame,
            text="صفحة 1 من 1",
            bg=COLORS['light']
        )
        self.page_label.pack(side='left', padx=10, pady=10)
        
        self.next_btn = create_simple_rtl_button(
            status_frame,
            text="التالي ▶",
            button_type="ghost",
            command=self.next_page
        )
        self.next_btn.pack(side='left', padx=5, pady=5)

    def load_data(self):
        """تحميل البيانات"""
        try:
            # تحميل الشحنات
            query = """
                SELECT s.*, sup.supplier_name
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                ORDER BY s.created_at DESC
            """

            self.shipments_data = self.db_manager.fetch_all(query)
            self.filtered_data = self.shipments_data.copy()

            # تحميل الموردين للتصفية
            self.load_suppliers()

            # تحديث الجدول
            self.update_table()

            # تحديث شريط الحالة
            self.update_status_bar()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all(
                "SELECT id, supplier_name FROM suppliers WHERE is_active = 1"
            )
            supplier_names = ["جميع الموردين"] + [s['supplier_name'] for s in suppliers]
            self.supplier_combo['values'] = supplier_names
            self.supplier_combo.set("جميع الموردين")
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")

    def update_table(self):
        """تحديث الجدول"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        # حساب البيانات للصفحة الحالية
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        page_data = self.filtered_data[start_index:end_index]

        # إضافة البيانات للجدول
        for i, shipment in enumerate(page_data):
            self.add_shipment_to_tree(shipment, i + 1)

    def add_shipment_to_tree(self, shipment, index):
        """إضافة شحنة للجدول"""
        # تنسيق البيانات
        values = [
            shipment.get('shipment_number', ''),
            shipment.get('supplier_name', ''),
            SHIPMENT_STATUS.get(shipment.get('status', ''), shipment.get('status', '')),
            self.format_date(shipment.get('shipment_date')),
            self.format_date(shipment.get('expected_arrival_date')),
            PORTS.get(shipment.get('departure_port', ''), shipment.get('departure_port', '')),
            PORTS.get(shipment.get('arrival_port', ''), shipment.get('arrival_port', '')),
            shipment.get('container_number', ''),
            self.format_currency(shipment.get('total_value', 0)),
            shipment.get('currency', 'USD'),
            shipment.get('payment_status', '')
        ]

        # تحديد لون الصف حسب الحالة
        status = shipment.get('status', '')
        tags = []

        # إضافة لون حسب الحالة
        if status == 'في الطريق':
            tags.append('in_transit')
        elif status == 'وصلت':
            tags.append('arrived')
        elif status == 'تم التسليم':
            tags.append('delivered')
        elif status == 'متأخرة':
            tags.append('delayed')
        elif status == 'ملغية':
            tags.append('cancelled')
        else:
            # إضافة لون متناوب للصفوف العادية
            if index % 2 == 0:
                tags.append('even_row')
            else:
                tags.append('odd_row')

        # إدراج العنصر
        item = self.tree.insert(
            '', 'end',
            text=str(index),
            values=values,
            tags=tags
        )

        # حفظ معرف الشحنة كخاصية للعنصر
        # لا يمكن استخدام set مع العمود #0، لذا سنحفظ المعرف بطريقة أخرى
        self.tree.item(item, text=f"{index} (ID: {shipment.get('id', '')})")

    def format_date(self, date_value):
        """تنسيق التاريخ"""
        if not date_value:
            return ""

        if isinstance(date_value, str):
            try:
                date_obj = datetime.strptime(date_value, '%Y-%m-%d')
                return date_obj.strftime('%Y/%m/%d')
            except:
                return date_value
        elif isinstance(date_value, (date, datetime)):
            return date_value.strftime('%Y/%m/%d')

        return str(date_value)

    def format_currency(self, amount):
        """تنسيق العملة"""
        try:
            return f"{float(amount):,.2f}"
        except:
            return "0.00"

    def on_search_change(self, *args):
        """عند تغيير البحث"""
        if hasattr(self, 'search_timer'):
            self.root.after_cancel(self.search_timer)

        self.search_timer = self.root.after(500, self.apply_filters)

    def on_filter_change(self, event=None):
        """عند تغيير التصفية"""
        self.apply_filters()

    def apply_filters(self):
        """تطبيق المرشحات"""
        try:
            search_text = self.search_var.get().strip().lower()
            status_filter = self.status_filter_var.get()
            supplier_filter = self.supplier_filter_var.get()

            self.filtered_data = []

            for shipment in self.shipments_data:
                # فلتر البحث العام
                if search_text:
                    searchable = f"{shipment.get('shipment_number', '')} {shipment.get('supplier_name', '')} {shipment.get('container_number', '')}".lower()
                    if search_text not in searchable:
                        continue

                # فلتر الحالة
                if status_filter and status_filter != "جميع الحالات":
                    shipment_status = SHIPMENT_STATUS.get(shipment.get('status', ''), shipment.get('status', ''))
                    if shipment_status != status_filter:
                        continue

                # فلتر المورد
                if supplier_filter and supplier_filter != "جميع الموردين":
                    if shipment.get('supplier_name', '') != supplier_filter:
                        continue

                self.filtered_data.append(shipment)

            # إعادة تعيين الصفحة
            self.current_page = 1

            # تحديث الجدول
            self.update_table()
            self.update_status_bar()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تطبيق المرشحات: {str(e)}")

    def clear_filters(self):
        """مسح المرشحات"""
        self.search_var.set("")
        self.status_filter_var.set("جميع الحالات")
        self.supplier_filter_var.set("جميع الموردين")

        self.filtered_data = self.shipments_data.copy()
        self.current_page = 1
        self.update_table()
        self.update_status_bar()

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        total_items = len(self.filtered_data)
        total_pages = max(1, (total_items + self.items_per_page - 1) // self.items_per_page)

        # تحديث عداد العناصر
        self.count_label.configure(text=f"إجمالي الشحنات: {total_items}")

        # تحديث معلومات الصفحة
        self.page_label.configure(text=f"صفحة {self.current_page} من {total_pages}")

        # تحديث حالة النظام
        if total_items == 0:
            self.status_label.configure(text="لا توجد شحنات")
        else:
            start_item = (self.current_page - 1) * self.items_per_page + 1
            end_item = min(self.current_page * self.items_per_page, total_items)
            self.status_label.configure(text=f"عرض {start_item}-{end_item} من {total_items}")

    def prev_page(self):
        """الصفحة السابقة"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_table()
            self.update_status_bar()

    def next_page(self):
        """الصفحة التالية"""
        total_pages = max(1, (len(self.filtered_data) + self.items_per_page - 1) // self.items_per_page)
        if self.current_page < total_pages:
            self.current_page += 1
            self.update_table()
            self.update_status_bar()

    def on_select(self, event):
        """عند اختيار شحنة"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            # استخراج معرف الشحنة من النص
            item_text = self.tree.item(item, 'text')

            # البحث عن معرف الشحنة في النص
            if '(ID:' in item_text:
                try:
                    shipment_id = item_text.split('(ID:')[1].split(')')[0].strip()

                    # البحث عن الشحنة
                    for shipment in self.shipments_data:
                        if str(shipment.get('id', '')) == str(shipment_id):
                            self.selected_shipment = shipment
                            break
                except:
                    # إذا فشل استخراج المعرف، نستخدم الفهرس
                    try:
                        index = int(item_text.split()[0]) - 1
                        if 0 <= index < len(self.filtered_data):
                            start_index = (self.current_page - 1) * self.items_per_page
                            actual_index = start_index + index
                            if actual_index < len(self.filtered_data):
                                self.selected_shipment = self.filtered_data[actual_index]
                    except:
                        self.selected_shipment = None

    def on_double_click(self, event):
        """عند النقر المزدوج"""
        if self.selected_shipment:
            self.edit_shipment()

    def show_context_menu(self, event):
        """إظهار قائمة السياق"""
        context_menu = tk.Menu(self.root, tearoff=0)

        context_menu.add_command(label="✏️ تعديل", command=self.edit_shipment)
        context_menu.add_command(label="📋 نسخ", command=self.duplicate_shipment)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_shipment)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def new_shipment(self):
        """إضافة شحنة جديدة"""
        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm(self.root, mode='add')
            self.root.wait_window(form.root)
            self.refresh_data()
        except ImportError:
            messagebox.showerror("خطأ", "لم يتم العثور على نموذج الشحنة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة شحنة: {str(e)}")

    def edit_shipment(self):
        """تعديل الشحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتعديل")
            return

        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm(self.root, mode='edit', shipment_data=self.selected_shipment)
            self.root.wait_window(form.root)
            self.refresh_data()
        except ImportError:
            messagebox.showerror("خطأ", "لم يتم العثور على نموذج الشحنة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الشحنة: {str(e)}")

    def duplicate_shipment(self):
        """نسخ الشحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للنسخ")
            return

        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm(self.root, mode='duplicate', shipment_data=self.selected_shipment)
            self.root.wait_window(form.root)
            self.refresh_data()
        except ImportError:
            messagebox.showerror("خطأ", "لم يتم العثور على نموذج الشحنة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نسخ الشحنة: {str(e)}")

    def delete_shipment(self):
        """حذف الشحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للحذف")
            return

        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الشحنة رقم {self.selected_shipment.get('shipment_number', '')}؟"
        )

        if result:
            try:
                self.db_manager.execute_query(
                    "DELETE FROM shipments WHERE id = ?",
                    (self.selected_shipment['id'],)
                )

                messagebox.showinfo("نجح", "تم حذف الشحنة بنجاح")
                self.refresh_data()
                self.selected_shipment = None

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الشحنة: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.configure(text="جاري التحديث...")

        def update_thread():
            try:
                time.sleep(0.5)
                self.root.after(0, self.load_data)
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في التحديث: {str(e)}"))

        threading.Thread(target=update_thread, daemon=True).start()

    def generate_report(self):
        """إنشاء تقرير"""
        messagebox.showinfo("قريباً", "ميزة التقارير ستتم إضافتها قريباً")

    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("قريباً", "ميزة التصدير ستتم إضافتها قريباً")

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.root.destroy()

def show_simple_shipments_manager(parent=None):
    """إظهار نظام إدارة الشحنات المبسط"""
    manager = SimpleShipmentsManager(parent)
    return manager

if __name__ == "__main__":
    # اختبار النظام
    app = SimpleShipmentsManager()
    app.root.mainloop()
