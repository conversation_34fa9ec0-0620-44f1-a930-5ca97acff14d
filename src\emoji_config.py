# -*- coding: utf-8 -*-
"""
تكوين الرموز التعبيرية للتطبيق
يمكن تعطيل الرموز التعبيرية من هنا لتجنب مشاكل الترميز
"""

# تفعيل/تعطيل الرموز التعبيرية
USE_EMOJIS = False

# قاموس الرموز التعبيرية
EMOJIS = {
    # رموز عامة
    'check': '✓' if USE_EMOJIS else '[✓]',
    'cross': '✗' if USE_EMOJIS else '[✗]',
    'warning': '⚠' if USE_EMOJIS else '[!]',
    'info': 'ℹ' if USE_EMOJIS else '[i]',
    'success': '✅' if USE_EMOJIS else '[OK]',
    'error': '❌' if USE_EMOJIS else '[ERR]',
    'pending': '⏳' if USE_EMOJIS else '[...]',
    
    # رموز الأقسام
    'basic_info': '📋' if USE_EMOJIS else '[معلومات]',
    'items': '📦' if USE_EMOJIS else '[أصناف]',
    'shipping': '🚢' if USE_EMOJIS else '[شحن]',
    'container': '📦' if USE_EMOJIS else '[حاوية]',
    'financial': '💰' if USE_EMOJIS else '[مالية]',
    'documents': '📄' if USE_EMOJIS else '[مستندات]',
    'tracking': '📊' if USE_EMOJIS else '[تتبع]',
    'notes': '📝' if USE_EMOJIS else '[ملاحظات]',
    
    # رموز الحقول
    'number': '🔢' if USE_EMOJIS else '[رقم]',
    'supplier': '🏢' if USE_EMOJIS else '[مورد]',
    'date': '📅' if USE_EMOJIS else '[تاريخ]',
    'status': '📊' if USE_EMOJIS else '[حالة]',
    'location': '📍' if USE_EMOJIS else '[موقع]',
    'weight': '⚖️' if USE_EMOJIS else '[وزن]',
    'currency': '💱' if USE_EMOJIS else '[عملة]',
    'link': '🔗' if USE_EMOJIS else '[رابط]',
    'file': '📎' if USE_EMOJIS else '[ملف]',
    
    # رموز الإجراءات
    'save': '💾' if USE_EMOJIS else '[حفظ]',
    'edit': '✏️' if USE_EMOJIS else '[تعديل]',
    'delete': '🗑️' if USE_EMOJIS else '[حذف]',
    'copy': '📋' if USE_EMOJIS else '[نسخ]',
    'search': '🔍' if USE_EMOJIS else '[بحث]',
    'add': '➕' if USE_EMOJIS else '[إضافة]',
    'view': '👁️' if USE_EMOJIS else '[عرض]',
    'print': '🖨️' if USE_EMOJIS else '[طباعة]',
    
    # رموز الحالة
    'required': '🔴' if USE_EMOJIS else '[مطلوب]',
    'optional': '🔵' if USE_EMOJIS else '[اختياري]',
    'completed': '✅' if USE_EMOJIS else '[مكتمل]',
    'incomplete': '⏳' if USE_EMOJIS else '[غير مكتمل]',
    
    # رموز المستخدم والوقت
    'user': '👤' if USE_EMOJIS else '[مستخدم]',
    'time': '🕐' if USE_EMOJIS else '[وقت]',
    'connected': '🟢' if USE_EMOJIS else '[متصل]',
    'disconnected': '🔴' if USE_EMOJIS else '[منقطع]',
}

def get_emoji(key, default=''):
    """الحصول على رمز تعبيري أو نص بديل"""
    return EMOJIS.get(key, default)

def format_with_emoji(key, text):
    """تنسيق النص مع الرمز التعبيري"""
    emoji = get_emoji(key)
    if emoji:
        return f"{emoji} {text}"
    return text
