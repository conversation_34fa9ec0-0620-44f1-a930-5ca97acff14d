# إضافة حالة "تحت الطلب" لحالات الشحنة
## Add "On Demand" Status to Shipment Status Options

### 📋 **الوصف**
تم إضافة حالة جديدة "تحت الطلب" إلى خيارات حالة الشحنة في النظام، مع تحديث جميع الشاشات والوظائف ذات الصلة.

### 🎯 **الهدف من التحديث**
- **تتبع أفضل للطلبات**: إضافة حالة خاصة للشحنات التي تحت الطلب
- **مرونة أكبر**: في تصنيف حالات الشحنات
- **تحسين سير العمل**: لمعالجة الطلبات الخاصة
- **دقة أكبر**: في تتبع مراحل الشحنة

### 🔧 **التحديثات المنفذة**

#### **📊 حالات الشحنة المحدثة**

##### **قبل التحديث:**
```python
SHIPMENT_STATUS = {
    'pending': 'في الانتظار',
    'confirmed': 'مؤكدة',
    'shipped': 'تم الشحن',
    'in_transit': 'في الطريق',
    'arrived': 'وصلت',
    'customs': 'في الجمارك',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغية',
    'delayed': 'متأخرة'
}
```

##### **بعد التحديث:**
```python
SHIPMENT_STATUS = {
    'pending': 'في الانتظار',
    'on_demand': 'تحت الطلب',      # ← الحالة الجديدة
    'confirmed': 'مؤكدة',
    'shipped': 'تم الشحن',
    'in_transit': 'في الطريق',
    'arrived': 'وصلت',
    'customs': 'في الجمارك',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغية',
    'delayed': 'متأخرة'
}
```

#### **📈 شريط التقدم المحدث**

##### **خريطة التقدم الجديدة:**
```python
progress_map = {
    'في الانتظار': 10,
    'تحت الطلب': 5,        # ← نسبة تقدم منخفضة للطلبات الخاصة
    'مؤكدة': 25,
    'تم الشحن': 40,
    'في الطريق': 60,
    'وصلت': 80,
    'في الجمارك': 85,
    'تم التسليم': 100,
    'ملغية': 0,
    'متأخرة': 50
}
```

### 🎨 **التأثير على الواجهات**

#### **🔹 شاشة الشحنة الجديدة:**
- ✅ **حقل حالة الشحنة**: يتضمن الآن "تحت الطلب"
- ✅ **شريط التقدم**: يعرض 5% للحالة الجديدة
- ✅ **التحديث التلقائي**: عند تغيير الحالة

#### **🔹 إدارة الشحنات:**
- ✅ **فلتر الحالة**: يتضمن "تحت الطلب" في الخيارات
- ✅ **عرض الجدول**: يعرض الحالة الجديدة بشكل صحيح
- ✅ **البحث والتصفية**: يعمل مع الحالة الجديدة

#### **🔹 شاشة الشحنات المتقدمة:**
- ✅ **فلتر الحالة**: محدث تلقائياً
- ✅ **عرض البيانات**: يتعامل مع الحالة الجديدة
- ✅ **تتبع الشحنات**: يعرض الحالة بشكل صحيح

### 📊 **ترتيب الحالات الجديد**

| الترتيب | الرمز | النص العربي | نسبة التقدم | الوصف |
|---------|-------|-------------|-------------|--------|
| 1 | `on_demand` | **تحت الطلب** | **5%** | **طلب خاص لم يتم تأكيده بعد** |
| 2 | `pending` | في الانتظار | 10% | شحنة جديدة لم تبدأ |
| 3 | `confirmed` | مؤكدة | 25% | تم تأكيد الشحنة |
| 4 | `shipped` | تم الشحن | 40% | غادرت ميناء المغادرة |
| 5 | `in_transit` | في الطريق | 60% | في طريقها للوصول |
| 6 | `arrived` | وصلت | 80% | وصلت لميناء الوصول |
| 7 | `customs` | في الجمارك | 85% | إجراءات جمركية |
| 8 | `delivered` | تم التسليم | 100% | تم التسليم بالكامل |
| 9 | `cancelled` | ملغية | 0% | تم إلغاء الشحنة |
| 10 | `delayed` | متأخرة | 50% | متأخرة عن الموعد |

### 🎯 **استخدام الحالة الجديدة**

#### **📋 متى تستخدم "تحت الطلب":**
- **طلبات خاصة**: من العملاء لم يتم تأكيدها
- **شحنات مشروطة**: تحتاج موافقات إضافية
- **طلبات مخصصة**: تتطلب تحضير خاص
- **شحنات تجريبية**: في مرحلة الاختبار

#### **🔄 سير العمل المقترح:**
1. **تحت الطلب** ← طلب جديد وارد
2. **في الانتظار** ← تم قبول الطلب
3. **مؤكدة** ← تم تأكيد التفاصيل
4. **تم الشحن** ← بدء عملية الشحن
5. **...** ← باقي المراحل

### 🚀 **المزايا المحققة**

#### **📈 تحسين التتبع:**
- ✅ **تصنيف أدق**: للطلبات في مراحلها الأولى
- ✅ **مرونة أكبر**: في إدارة الطلبات الخاصة
- ✅ **وضوح أفضل**: لحالة الطلبات غير المؤكدة
- ✅ **تنظيم محسن**: لسير العمل

#### **💼 فوائد إدارية:**
- ✅ **متابعة أفضل**: للطلبات الخاصة
- ✅ **تقارير دقيقة**: تتضمن جميع أنواع الطلبات
- ✅ **تخطيط محسن**: للموارد والجدولة
- ✅ **خدمة عملاء أفضل**: بتتبع دقيق للطلبات

#### **🎨 تحسين تجربة المستخدم:**
- ✅ **خيارات أكثر**: لتصنيف الشحنات
- ✅ **وضوح أكبر**: في حالة الطلبات
- ✅ **مرونة في الاستخدام**: لحالات مختلفة
- ✅ **تناسق في النظام**: عبر جميع الشاشات

### 🔧 **التحديثات التقنية**

#### **الملفات المحدثة:**

1. **`config/config.py`**
   - ✅ إضافة `'on_demand': 'تحت الطلب'` إلى `SHIPMENT_STATUS`
   - ✅ ترتيب الحالات بشكل منطقي

2. **`src/fullscreen_shipment_form.py`**
   - ✅ تحديث `progress_map` لتتضمن الحالة الجديدة
   - ✅ تعيين نسبة تقدم 5% للحالة الجديدة

#### **الملفات المتأثرة تلقائياً:**
- **`src/advanced_shipments_manager.py`** ← يستخدم `SHIPMENT_STATUS.values()`
- **`src/advanced_shipments_window.py`** ← يستخدم `SHIPMENT_STATUS.values()`
- **جميع الشاشات الأخرى** ← تحديث تلقائي

### ✅ **التحقق من النجاح**

#### **🔍 اختبار الوظائف:**
1. **فتح شاشة شحنة جديدة** ✅
   - التحقق من وجود "تحت الطلب" في قائمة الحالات
2. **اختيار الحالة الجديدة** ✅
   - التحقق من عرض شريط التقدم 5%
3. **فتح إدارة الشحنات** ✅
   - التحقق من وجود "تحت الطلب" في فلتر الحالة
4. **حفظ شحنة بالحالة الجديدة** ✅
   - التحقق من الحفظ والعرض الصحيح

#### **📊 النتائج:**
- **حالة جديدة**: تم إضافتها بنجاح
- **شريط التقدم**: يعمل بشكل صحيح
- **جميع الشاشات**: محدثة تلقائياً
- **الفلاتر والبحث**: تعمل مع الحالة الجديدة

### 🎉 **الخلاصة**
تم بنجاح إضافة حالة "تحت الطلب" إلى نظام حالات الشحنة. هذا التحديث يوفر مرونة أكبر في تصنيف وتتبع الطلبات الخاصة، مع الحفاظ على التناسق عبر جميع شاشات النظام.

**🚀 النتيجة النهائية:**
- ✅ **10 حالات شحنة** بدلاً من 9
- ✅ **حالة جديدة** للطلبات الخاصة
- ✅ **تحديث تلقائي** لجميع الشاشات
- ✅ **تحسين سير العمل** وإدارة الطلبات

**📈 الحالات الآن (بالترتيب):**
1. تحت الطلب (5%)
2. في الانتظار (10%)
3. مؤكدة (25%)
4. تم الشحن (40%)
5. في الطريق (60%)
6. وصلت (80%)
7. في الجمارك (85%)
8. تم التسليم (100%)
9. ملغية (0%)
10. متأخرة (50%)
