#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام متابعة الشحنات
Comprehensive System Testing
"""

import os
import sys
import unittest
import sqlite3
from datetime import datetime, date

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from database.database_manager import DatabaseManager
from src.auth_manager import AuthManager
from src.reports_manager import ReportsManager
from src.backup_manager import BackupManager

class TestDatabaseManager(unittest.TestCase):
    """اختبار مدير قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء مجلد database إذا لم يكن موجوداً
        if not os.path.exists("database"):
            os.makedirs("database")
        self.db_manager = DatabaseManager("database/test_database.db")
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists("database/test_database.db"):
            os.remove("database/test_database.db")
    
    def test_database_creation(self):
        """اختبار إنشاء قاعدة البيانات"""
        self.assertTrue(os.path.exists("database/test_database.db"))
    
    def test_tables_creation(self):
        """اختبار إنشاء الجداول"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول الرئيسية
        tables = [
            'users', 'company_settings', 'suppliers', 'item_categories',
            'items', 'warehouses', 'shipments', 'shipment_items',
            'shipment_status_history', 'inventory', 'inventory_movements'
        ]
        
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            result = cursor.fetchone()
            self.assertIsNotNone(result, f"الجدول {table} غير موجود")
        
        conn.close()
    
    def test_default_admin_creation(self):
        """اختبار إنشاء المدير الافتراضي"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        self.assertIsNotNone(admin_user, "المستخدم الافتراضي admin غير موجود")
        self.assertEqual(admin_user['role'], 'admin')
        self.assertEqual(admin_user['is_active'], 1)
        
        conn.close()

class TestAuthManager(unittest.TestCase):
    """اختبار مدير المصادقة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        if not os.path.exists("database"):
            os.makedirs("database")
        self.db_manager = DatabaseManager("database/test_auth.db")
        self.auth_manager = AuthManager()
        self.auth_manager.db_manager = self.db_manager

    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists("database/test_auth.db"):
            os.remove("database/test_auth.db")
    
    def test_admin_login(self):
        """اختبار تسجيل دخول المدير"""
        result = self.auth_manager.login("admin", "admin123")
        self.assertTrue(result['success'], "فشل في تسجيل دخول المدير")
        self.assertIsNotNone(self.auth_manager.current_user)
    
    def test_wrong_password(self):
        """اختبار كلمة مرور خاطئة"""
        result = self.auth_manager.login("admin", "wrongpassword")
        self.assertFalse(result['success'], "تم قبول كلمة مرور خاطئة")
    
    def test_user_permissions(self):
        """اختبار صلاحيات المستخدم"""
        # تسجيل دخول المدير
        self.auth_manager.login("admin", "admin123")
        
        # المدير يجب أن يملك جميع الصلاحيات
        self.assertTrue(self.auth_manager.has_permission('manage_users'))
        self.assertTrue(self.auth_manager.has_permission('edit_shipments'))
        self.assertTrue(self.auth_manager.has_permission('view_reports'))
    
    def test_logout(self):
        """اختبار تسجيل الخروج"""
        self.auth_manager.login("admin", "admin123")
        self.assertTrue(self.auth_manager.is_logged_in())
        
        self.auth_manager.logout()
        self.assertFalse(self.auth_manager.is_logged_in())

class TestDataOperations(unittest.TestCase):
    """اختبار عمليات البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        if not os.path.exists("database"):
            os.makedirs("database")
        self.db_manager = DatabaseManager("database/test_data.db")

    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists("database/test_data.db"):
            os.remove("database/test_data.db")
    
    def test_supplier_operations(self):
        """اختبار عمليات الموردين"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # إضافة مورد
        cursor.execute('''
            INSERT INTO suppliers (supplier_code, supplier_name, contact_person, email, phone)
            VALUES (?, ?, ?, ?, ?)
        ''', ("SUP001", "مورد تجريبي", "أحمد محمد", "<EMAIL>", "123456789"))
        
        conn.commit()
        
        # التحقق من إضافة المورد
        cursor.execute("SELECT * FROM suppliers WHERE supplier_code = 'SUP001'")
        supplier = cursor.fetchone()
        
        self.assertIsNotNone(supplier, "فشل في إضافة المورد")
        self.assertEqual(supplier['supplier_name'], "مورد تجريبي")
        
        # تحديث المورد
        cursor.execute('''
            UPDATE suppliers SET supplier_name = ? WHERE supplier_code = ?
        ''', ("مورد محدث", "SUP001"))
        
        conn.commit()
        
        # التحقق من التحديث
        cursor.execute("SELECT supplier_name FROM suppliers WHERE supplier_code = 'SUP001'")
        updated_supplier = cursor.fetchone()
        
        self.assertEqual(updated_supplier['supplier_name'], "مورد محدث")
        
        conn.close()
    
    def test_item_operations(self):
        """اختبار عمليات الأصناف"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # إضافة فئة
        cursor.execute('''
            INSERT INTO item_categories (category_name, description)
            VALUES (?, ?)
        ''', ("فئة تجريبية", "وصف الفئة"))
        
        category_id = cursor.lastrowid
        
        # إضافة صنف
        cursor.execute('''
            INSERT INTO items (item_code, item_name, category_id, unit_of_measure, cost_price, selling_price)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ("ITEM001", "صنف تجريبي", category_id, "قطعة", 10.50, 15.75))
        
        conn.commit()
        
        # التحقق من إضافة الصنف
        cursor.execute('''
            SELECT i.*, c.category_name 
            FROM items i 
            LEFT JOIN item_categories c ON i.category_id = c.id 
            WHERE i.item_code = 'ITEM001'
        ''')
        item = cursor.fetchone()
        
        self.assertIsNotNone(item, "فشل في إضافة الصنف")
        self.assertEqual(item['item_name'], "صنف تجريبي")
        self.assertEqual(item['category_name'], "فئة تجريبية")
        
        conn.close()

class TestReportsManager(unittest.TestCase):
    """اختبار مدير التقارير"""
    
    def setUp(self):
        """إعداد الاختبار"""
        if not os.path.exists("database"):
            os.makedirs("database")
        self.db_manager = DatabaseManager("database/test_reports.db")
        self.reports_manager = ReportsManager()
        self.reports_manager.db_manager = self.db_manager

        # إضافة بيانات تجريبية
        self.add_test_data()

    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists("database/test_reports.db"):
            os.remove("database/test_reports.db")
    
    def add_test_data(self):
        """إضافة بيانات تجريبية"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # إضافة مورد
        cursor.execute('''
            INSERT INTO suppliers (supplier_code, supplier_name)
            VALUES (?, ?)
        ''', ("SUP001", "مورد تجريبي"))
        
        supplier_id = cursor.lastrowid
        
        # إضافة شحنة
        cursor.execute('''
            INSERT INTO shipments (shipment_number, supplier_id, shipment_date, status, invoice_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', ("SHIP001", supplier_id, "2024-01-01", "pending", 1000.00))
        
        conn.commit()
        conn.close()
    
    def test_shipments_report_generation(self):
        """اختبار إنشاء تقرير الشحنات"""
        # اختبار إنشاء التقرير كبيانات
        report_data = self.reports_manager.generate_shipments_report(format='data')
        
        self.assertIsNotNone(report_data, "فشل في إنشاء تقرير الشحنات")
        self.assertGreater(len(report_data), 0, "تقرير الشحنات فارغ")
    
    def test_statistics_generation(self):
        """اختبار إنشاء الإحصائيات"""
        stats = self.reports_manager.get_shipment_statistics()
        
        self.assertIsInstance(stats, dict, "الإحصائيات ليست في التنسيق الصحيح")
        self.assertIn('total_shipments', stats, "إحصائية إجمالي الشحنات مفقودة")

class TestSystemIntegration(unittest.TestCase):
    """اختبار تكامل النظام"""
    
    def setUp(self):
        """إعداد الاختبار"""
        if not os.path.exists("database"):
            os.makedirs("database")
        self.db_manager = DatabaseManager("database/test_integration.db")

    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists("database/test_integration.db"):
            os.remove("database/test_integration.db")
    
    def test_complete_workflow(self):
        """اختبار سير العمل الكامل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # 1. إضافة مورد
        cursor.execute('''
            INSERT INTO suppliers (supplier_code, supplier_name, email, phone)
            VALUES (?, ?, ?, ?)
        ''', ("SUP001", "مورد تجريبي", "<EMAIL>", "123456789"))
        
        supplier_id = cursor.lastrowid
        
        # 2. إضافة فئة وصنف
        cursor.execute('''
            INSERT INTO item_categories (category_name)
            VALUES (?)
        ''', ("فئة تجريبية",))
        
        category_id = cursor.lastrowid
        
        cursor.execute('''
            INSERT INTO items (item_code, item_name, category_id, cost_price, selling_price)
            VALUES (?, ?, ?, ?, ?)
        ''', ("ITEM001", "صنف تجريبي", category_id, 10.00, 15.00))
        
        item_id = cursor.lastrowid
        
        # 3. إضافة شحنة
        cursor.execute('''
            INSERT INTO shipments (shipment_number, supplier_id, shipment_date, status, invoice_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', ("SHIP001", supplier_id, "2024-01-01", "pending", 1000.00))
        
        shipment_id = cursor.lastrowid
        
        # 4. إضافة تفاصيل الشحنة
        cursor.execute('''
            INSERT INTO shipment_items (shipment_id, item_id, quantity, unit_price, total_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', (shipment_id, item_id, 10, 10.00, 100.00))
        
        conn.commit()
        
        # التحقق من سير العمل
        cursor.execute('''
            SELECT s.shipment_number, sup.supplier_name, i.item_name, si.quantity
            FROM shipments s
            JOIN suppliers sup ON s.supplier_id = sup.id
            JOIN shipment_items si ON s.id = si.shipment_id
            JOIN items i ON si.item_id = i.id
            WHERE s.shipment_number = 'SHIP001'
        ''')
        
        result = cursor.fetchone()
        
        self.assertIsNotNone(result, "فشل في سير العمل الكامل")
        self.assertEqual(result['shipment_number'], "SHIP001")
        self.assertEqual(result['supplier_name'], "مورد تجريبي")
        self.assertEqual(result['item_name'], "صنف تجريبي")
        self.assertEqual(result['quantity'], 10)
        
        conn.close()

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("بدء اختبار النظام...")
    print("=" * 50)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات قاعدة البيانات
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDatabaseManager))

    # إضافة اختبارات المصادقة
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestAuthManager))

    # إضافة اختبارات عمليات البيانات
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataOperations))

    # إضافة اختبارات التقارير
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestReportsManager))

    # إضافة اختبارات التكامل
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestSystemIntegration))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 50)
    print("نتائج الاختبار:")
    print(f"إجمالي الاختبارات: {result.testsRun}")
    print(f"الاختبارات الناجحة: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"الاختبارات الفاشلة: {len(result.failures)}")
    print(f"الأخطاء: {len(result.errors)}")
    
    if result.failures:
        print("\nالاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nالأخطاء:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # تنظيف ملفات الاختبار
    test_files = [
        "database/test_database.db", "database/test_auth.db", "database/test_data.db",
        "database/test_reports.db", "database/test_integration.db"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
    else:
        print("\n❌ بعض الاختبارات فشلت!")
    
    input("\nاضغط Enter للمتابعة...")
