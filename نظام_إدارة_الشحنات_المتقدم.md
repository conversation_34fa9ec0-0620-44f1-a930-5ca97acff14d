# نظام إدارة الشحنات المتقدم مع دعم RTL

## نظرة عامة

تم تطوير نظام إدارة الشحنات بشكل كامل ومتقدم مع دعم شامل للغة العربية واتجاه RTL. النظام يوفر واجهة احترافية وميزات متقدمة لإدارة جميع جوانب الشحنات.

## 🎨 الميزات الرئيسية

### التصميم والواجهة
- **🎨 تصميم RTL كامل**: محاذاة يمين طبيعية لجميع العناصر
- **🌈 نظام ألوان متقدم**: 50+ لون مع تدرجات متناسقة
- **🔤 خطوط عربية محسنة**: Segoe UI مع بدائل متعددة
- **📱 تصميم متجاوب**: يتكيف مع أحجام مختلفة
- **✨ تأثيرات بصرية**: hover effects وتلوين الصفوف

### البحث والتصفية المتقدمة
- **🔍 بحث عام**: في رقم الشحنة، المورد، رقم الحاوية
- **📊 تصفية الحالة**: حسب حالة الشحنة
- **🏢 تصفية المورد**: حسب المورد
- **📅 تصفية التاريخ**: حسب فترة زمنية محددة
- **🗑️ مسح المرشحات**: إعادة تعيين سريعة

### إدارة البيانات
- **➕ إضافة شحنات جديدة**: نموذج شامل مع تبويبات
- **✏️ تعديل الشحنات**: تحديث جميع البيانات
- **🗑️ حذف الشحنات**: مع تأكيد الأمان
- **📋 عرض التفاصيل**: لوحة تفاصيل تفاعلية
- **📊 تتبع الشحنات**: معلومات الحالة والموقع

### الجدول المتقدم
- **📋 عرض منظم**: 10 أعمدة مع معلومات شاملة
- **🎨 تلوين الصفوف**: حسب حالة الشحنة
- **📄 تقسيم الصفحات**: 20 عنصر لكل صفحة
- **🔄 تحديث تلقائي**: مع مؤشرات الحالة
- **📱 أشرطة تمرير**: عمودية وأفقية

## 🏗️ البنية التقنية

### الملفات الرئيسية
```
src/
├── advanced_shipments_window.py    # النافذة الرئيسية
├── shipment_form_window.py         # نموذج إضافة/تعديل
├── ui_styles.py                    # مدير الأنماط
├── rtl_components.py               # مكونات RTL
└── database/
    └── database_manager.py         # إدارة قاعدة البيانات
```

### قاعدة البيانات
```sql
-- جدول الشحنات الرئيسي
CREATE TABLE shipments (
    id TEXT PRIMARY KEY,
    shipment_number TEXT UNIQUE NOT NULL,
    supplier_id INTEGER,
    status TEXT,
    shipment_date DATE,
    expected_arrival_date DATE,
    departure_port TEXT,
    arrival_port TEXT,
    shipping_company TEXT,
    container_number TEXT,
    bill_of_lading TEXT,
    container_type TEXT,
    weight REAL,
    volume REAL,
    currency TEXT,
    total_value REAL,
    shipping_cost REAL,
    additional_fees REAL,
    insurance_cost REAL,
    customs_fees REAL,
    payment_method TEXT,
    payment_status TEXT,
    notes TEXT,
    created_at DATETIME,
    updated_at DATETIME,
    created_by TEXT
);

-- جدول أصناف الشحنة
CREATE TABLE shipment_items (
    id TEXT PRIMARY KEY,
    shipment_id TEXT,
    item_code TEXT,
    item_name TEXT,
    quantity REAL,
    unit TEXT,
    unit_price REAL,
    total_price REAL,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id)
);
```

## 🎯 أقسام النافذة الرئيسية

### 1. شريط العنوان والأدوات
- **العنوان**: "إدارة الشحنات المتقدمة" مع وصف
- **أزرار الأدوات**: 6 أزرار ملونة مع اختصارات
  - ➕ شحنة جديدة (Ctrl+N)
  - ✏️ تعديل (F2)
  - 🗑️ حذف (Del)
  - 📊 تقرير (Ctrl+R)
  - 🔄 تحديث (F5)
  - ❓ مساعدة (F1)

### 2. لوحة البحث والتصفية
- **البحث العام**: حقل نص مع بحث فوري
- **تصفية الحالة**: قائمة منسدلة بجميع الحالات
- **تصفية المورد**: قائمة منسدلة بالموردين
- **تصفية التاريخ**: حقلين من وإلى
- **أزرار الإجراء**: بحث ومسح

### 3. جدول الشحنات
- **الأعمدة**: 10 أعمدة مع معلومات شاملة
- **التلوين**: حسب حالة الشحنة
- **التفاعل**: نقر واحد للتحديد، مزدوج للتعديل
- **قائمة السياق**: بالزر الأيمن

### 4. لوحة التفاصيل
- **المعلومات الأساسية**: رقم الشحنة، المورد، التاريخ
- **معلومات الشحن**: الموانئ، شركة الشحن، الحاوية
- **المعلومات المالية**: القيم والتكاليف
- **حالة الشحنة**: مع تلوين حسب الحالة
- **أزرار الإجراءات**: تعديل، تتبع، تقرير، حذف

### 5. شريط الحالة والتنقل
- **معلومات الحالة**: عدد الشحنات وحالة النظام
- **أزرار التنقل**: السابق والتالي
- **معلومات الصفحة**: رقم الصفحة الحالية

## 📝 نموذج الشحنة (إضافة/تعديل)

### التبويبات الخمسة

#### 1. المعلومات الأساسية
- **رقم الشحنة**: تلقائي أو يدوي
- **المورد**: قائمة منسدلة من قاعدة البيانات
- **تاريخ الإنشاء**: تلقائي
- **المستخدم المنشئ**: من نظام المصادقة
- **حالة الشحنة**: قائمة منسدلة

#### 2. معلومات الشحن
- **تاريخ الشحن**: مطلوب
- **تاريخ الوصول المتوقع**: اختياري
- **ميناء المغادرة**: قائمة منسدلة
- **ميناء الوصول**: قائمة منسدلة
- **شركة الشحن**: قائمة منسدلة
- **رقم الحاوية**: نص حر
- **رقم بوليصة الشحن**: نص حر
- **نوع الحاوية**: قائمة منسدلة
- **الوزن والحجم**: أرقام

#### 3. المعلومات المالية
- **العملة**: قائمة منسدلة
- **القيمة الإجمالية**: مطلوب
- **تكلفة الشحن**: اختياري
- **رسوم إضافية**: اختياري
- **رسوم التأمين**: اختياري
- **رسوم الجمارك**: اختياري
- **طريقة الدفع**: قائمة منسدلة
- **حالة الدفع**: قائمة منسدلة

#### 4. الأصناف
- **جدول الأصناف**: مع 6 أعمدة
- **إضافة صنف**: نموذج فرعي
- **تعديل صنف**: تحديث البيانات
- **حذف صنف**: مع تأكيد
- **حساب تلقائي**: للإجماليات

#### 5. الملاحظات والمرفقات
- **حقل الملاحظات**: نص متعدد الأسطر
- **قائمة المرفقات**: عرض الملفات
- **إضافة مرفق**: تصفح واختيار ملف

### التحقق من صحة البيانات
- **الحقول المطلوبة**: مميزة بـ *
- **تنسيق التاريخ**: YYYY-MM-DD
- **القيم المالية**: أرقام صحيحة
- **رسائل خطأ**: واضحة ومفصلة

## 🎨 نظام الألوان والحالات

### ألوان حالات الشحنات
```css
في الانتظار: #FEF3C7 (أصفر فاتح)
مؤكدة: #DBEAFE (أزرق فاتح)
تم الشحن: #E0E7FF (بنفسجي فاتح)
في الطريق: #FDE68A (أصفر)
وصلت: #D1FAE5 (أخضر فاتح)
في الجمارك: #FED7AA (برتقالي فاتح)
تم التسليم: #BBF7D0 (أخضر)
ملغية: #FECACA (أحمر فاتح)
متأخرة: #F87171 (أحمر)
```

### ألوان الأزرار
```css
primary: #1E40AF (أزرق)
success: #059669 (أخضر)
warning: #D97706 (برتقالي)
danger: #DC2626 (أحمر)
info: #0891B2 (سماوي)
secondary: #6B7280 (رمادي)
```

## ⌨️ اختصارات لوحة المفاتيح

### في النافذة الرئيسية
- **Ctrl+N**: شحنة جديدة
- **F2**: تعديل الشحنة المحددة
- **Delete**: حذف الشحنة المحددة
- **Ctrl+F**: التركيز على البحث
- **F5**: تحديث البيانات
- **F1**: مساعدة

### في نموذج الشحنة
- **Ctrl+S**: حفظ النموذج
- **Esc**: إلغاء النموذج
- **F1**: مساعدة النموذج

## 🔧 الإعدادات والتخصيص

### إعدادات العرض
```python
items_per_page = 20        # عدد العناصر لكل صفحة
auto_refresh = True        # تحديث تلقائي
show_tooltips = True       # إظهار التلميحات
default_currency = 'USD'   # العملة الافتراضية
```

### قوائم البيانات
```python
SHIPMENT_STATUS = {
    'في الانتظار': 'Pending',
    'مؤكدة': 'Confirmed',
    'تم الشحن': 'Shipped',
    'في الطريق': 'In Transit',
    'وصلت': 'Arrived',
    'في الجمارك': 'In Customs',
    'تم التسليم': 'Delivered',
    'ملغية': 'Cancelled',
    'متأخرة': 'Delayed'
}

PORTS = {
    'جدة': 'Jeddah',
    'الدمام': 'Dammam',
    'الرياض': 'Riyadh',
    'شنغهاي': 'Shanghai',
    'دبي': 'Dubai'
}

SHIPPING_COMPANIES = {
    'أرامكس': 'Aramex',
    'DHL': 'DHL',
    'فيديكس': 'FedEx',
    'البحري السعودي': 'Saudi Maritime'
}
```

## 📊 التقارير والإحصائيات

### تقارير متاحة
- **تقرير الشحنات**: جميع الشحنات مع تصفية
- **تقرير المورد**: شحنات مورد محدد
- **تقرير الحالة**: شحنات حسب الحالة
- **تقرير مالي**: القيم والتكاليف
- **تقرير الأداء**: إحصائيات الشحن

### تصدير البيانات
- **Excel**: جداول بيانات
- **PDF**: تقارير مطبوعة
- **CSV**: بيانات خام
- **JSON**: للتكامل مع أنظمة أخرى

## 🛡️ الأمان والصلاحيات

### مستويات الوصول
- **مدير**: جميع الصلاحيات
- **مشرف**: إضافة وتعديل
- **مستخدم**: عرض فقط
- **ضيف**: عرض محدود

### تسجيل العمليات
- **إضافة شحنة**: تسجيل المستخدم والوقت
- **تعديل شحنة**: تسجيل التغييرات
- **حذف شحنة**: تسجيل مع سبب الحذف
- **تصدير بيانات**: تسجيل العمليات

## 🔄 التكامل والتوسع

### APIs متاحة
- **REST API**: للتكامل مع أنظمة خارجية
- **Webhook**: للإشعارات التلقائية
- **Export API**: لتصدير البيانات
- **Import API**: لاستيراد البيانات

### إضافات مستقبلية
- **تتبع GPS**: موقع الشحنات الحي
- **إشعارات SMS**: تحديثات فورية
- **تكامل جمركي**: ربط مع أنظمة الجمارك
- **ذكاء اصطناعي**: توقع التأخيرات

## 🧪 الاختبار والجودة

### اختبارات الوظائف
- ✅ إضافة شحنة جديدة
- ✅ تعديل شحنة موجودة
- ✅ حذف شحنة
- ✅ البحث والتصفية
- ✅ عرض التفاصيل
- ✅ إدارة الأصناف

### اختبارات الواجهة
- ✅ تصميم RTL
- ✅ الألوان والخطوط
- ✅ التأثيرات البصرية
- ✅ الاستجابة للتفاعل
- ✅ اختصارات لوحة المفاتيح

### اختبارات الأداء
- ✅ تحميل 1000+ شحنة
- ✅ البحث السريع
- ✅ التصفية المتقدمة
- ✅ التنقل بين الصفحات

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **F1**: مساعدة سريعة في النظام
- **دليل المستخدم**: توثيق شامل
- **فيديوهات تعليمية**: شروحات مرئية
- **الدعم الفني**: <EMAIL>

### الموارد المفيدة
- **أمثلة عملية**: حالات استخدام حقيقية
- **أفضل الممارسات**: نصائح للاستخدام الأمثل
- **حل المشاكل**: دليل استكشاف الأخطاء
- **التحديثات**: آخر الميزات والتحسينات

---

## 🏆 الخلاصة

تم تطوير نظام إدارة الشحنات المتقدم ليكون:

- **🎨 جميل ومتقدم**: تصميم RTL احترافي
- **🚀 سريع وفعال**: أداء عالي مع قواعد بيانات محسنة
- **🔧 مرن وقابل للتخصيص**: إعدادات متنوعة
- **🛡️ آمن وموثوق**: حماية البيانات والصلاحيات
- **📱 سهل الاستخدام**: واجهة بديهية ومساعدة شاملة

**النظام جاهز للاستخدام الإنتاجي ويوفر حلاً شاملاً لإدارة الشحنات!** 🚢✨📊
