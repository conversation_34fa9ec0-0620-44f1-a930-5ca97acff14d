#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime

def create_simple_sample():
    """إنشاء بيانات تجريبية بسيطة"""
    
    conn = sqlite3.connect('shipments.db')
    cursor = conn.cursor()
    
    # إنشاء الجدول إذا لم يكن موجوداً
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS advanced_shipments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            shipment_number TEXT UNIQUE NOT NULL,
            customer_name TEXT NOT NULL,
            supplier_name TEXT NOT NULL,
            origin_port TEXT,
            destination_port TEXT,
            shipment_date DATE,
            expected_arrival DATE,
            actual_arrival DATE,
            status TEXT DEFAULT 'قيد التحضير',
            priority TEXT DEFAULT 'عادي',
            total_value REAL DEFAULT 0,
            currency TEXT DEFAULT 'USD',
            container_type TEXT,
            container_number TEXT,
            seal_number TEXT,
            weight REAL,
            volume REAL,
            items_count INTEGER DEFAULT 0,
            tracking_number TEXT,
            shipping_line TEXT,
            vessel_name TEXT,
            voyage_number TEXT,
            bill_of_lading TEXT,
            customs_status TEXT DEFAULT 'لم يتم التخليص',
            insurance_value REAL DEFAULT 0,
            freight_cost REAL DEFAULT 0,
            other_charges REAL DEFAULT 0,
            notes TEXT,
            documents_json TEXT,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إدراج بيانات تجريبية
    sample_data = [
        ('SH20240001', 'شركة التجارة العالمية', 'مصنع شنغهاي للإلكترونيات', 'ميناء جدة', 'ميناء شنغهاي', '2024-01-15', '2024-02-15', None, 'تم الشحن', 'مهم', 150000.00, 'USD', '40 قدم', 'MSKU1234567', 'SL123456', 18500.50, 45.2, 250, 'TRK123456789', 'مسك', 'MV مسك 101', 'V101', 'BL12345678', 'قيد التخليص', 1500.00, 8500.00, 1200.00, 'شحنة إلكترونيات متنوعة', None, 'admin'),
        ('SH20240002', 'مؤسسة الخليج للاستيراد', 'شركة دبي للمنسوجات', 'ميناء الدمام', 'ميناء دبي', '2024-01-20', '2024-02-10', '2024-02-08', 'تم التسليم', 'عادي', 85000.00, 'USD', '20 قدم', 'MSKU2345678', 'SL234567', 12000.00, 28.5, 180, 'TRK234567890', 'الخطوط السعودية', 'MV الخطوط السعودية 202', 'V202', 'BL23456789', 'تم التخليص', 850.00, 4500.00, 800.00, 'منسوجات قطنية عالية الجودة', None, 'admin'),
        ('SH20240003', 'شركة النور للتجارة', 'مصنع إسطنبول للأثاث', 'ميناء الملك عبدالله', 'ميناء إسطنبول', '2024-02-01', '2024-03-01', None, 'في الطريق', 'عاجل', 220000.00, 'USD', '40 قدم عالي', 'MSKU3456789', 'SL345678', 22000.00, 58.3, 120, 'TRK345678901', 'إيفرجرين', 'MV إيفرجرين 303', 'V303', 'BL34567890', 'لم يتم التخليص', 2200.00, 12000.00, 1800.00, 'أثاث مكتبي وأثاث منزلي', None, 'admin'),
        ('SH20240004', 'مجموعة الأمل التجارية', 'شركة مومباي للتوابل', 'ميناء ينبع', 'ميناء مومباي', '2024-02-10', '2024-03-15', None, 'قيد التحضير', 'حرج', 95000.00, 'USD', 'مبرد 20 قدم', 'MSKU4567890', 'SL456789', 8500.00, 22.1, 300, 'TRK456789012', 'مايرسك', 'MV مايرسك 404', 'V404', 'BL45678901', 'لم يتم التخليص', 950.00, 6500.00, 1100.00, 'توابل وبهارات طبيعية', None, 'admin'),
        ('SH20240005', 'شركة البحر الأحمر', 'مصنع بانكوك للأجهزة', 'ميناء جيزان', 'ميناء بانكوك', '2024-02-15', '2024-03-20', None, 'تم الشحن', 'عادي', 175000.00, 'USD', '40 قدم', 'MSKU5678901', 'SL567890', 19500.00, 52.8, 200, 'TRK567890123', 'كوسكو', 'MV كوسكو 505', 'V505', 'BL56789012', 'قيد التخليص', 1750.00, 9500.00, 1400.00, 'أجهزة كهربائية ومنزلية', None, 'admin')
    ]
    
    for data in sample_data:
        try:
            cursor.execute('''
                INSERT INTO advanced_shipments (
                    shipment_number, customer_name, supplier_name, origin_port, destination_port,
                    shipment_date, expected_arrival, actual_arrival, status, priority, total_value, currency,
                    container_type, container_number, seal_number, weight, volume, items_count,
                    tracking_number, shipping_line, vessel_name, voyage_number, bill_of_lading,
                    customs_status, insurance_value, freight_cost, other_charges, notes,
                    documents_json, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', data)
        except sqlite3.IntegrityError:
            print(f"الشحنة {data[0]} موجودة مسبقاً")
    
    conn.commit()
    
    # التحقق من البيانات
    cursor.execute('SELECT COUNT(*) FROM advanced_shipments')
    count = cursor.fetchone()[0]
    print(f"إجمالي الشحنات في قاعدة البيانات: {count}")
    
    conn.close()

if __name__ == "__main__":
    create_simple_sample()
