#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الشحنة المتقدم والشامل مع أنماط CSS متقدمة ودعم RTL كامل
Ultimate Shipment Form with Advanced CSS Styles and Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date, timedelta
import uuid
import json

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES, CONTAINER_TYPES, PAYMENT_METHODS
from database.database_manager import DatabaseManager
from src.advanced_rtl_styles import advanced_style_manager
from src.enhanced_rtl_components import *
from src.auth_manager import auth_manager

class UltimateShipmentForm:
    """نموذج الشحنة المتقدم والشامل"""
    
    def __init__(self, parent, mode='add', shipment_data=None):
        self.parent = parent
        self.mode = mode  # add, edit, duplicate, view
        self.shipment_data = shipment_data or {}
        self.db_manager = DatabaseManager()
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات النموذج
        self.form_vars = {}
        self.validation_errors = []
        self.is_modified = False
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_form_data()
        
        # توسيط النافذة
        self.center_window()
        
        # ربط الأحداث
        self.bind_events()
        
    def setup_window(self):
        """إعداد النافذة"""
        # تحديد العنوان حسب الوضع
        titles = {
            'add': '➕ إضافة شحنة جديدة',
            'edit': '✏️ تعديل الشحنة',
            'duplicate': '📋 نسخ الشحنة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }
        
        self.root.title(titles.get(self.mode, 'نموذج الشحنة'))
        self.root.geometry("1400x900")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        
        # تطبيق أيقونة
        try:
            self.root.iconbitmap('assets/ship_icon.ico')
        except:
            pass
        
        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_interface(self):
        """إنشاء الواجهة المتقدمة"""
        # الإطار الرئيسي
        self.main_frame = create_enhanced_rtl_frame(
            self.root,
            style='container_primary'
        )
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header()
        
        # شريط الأدوات
        self.create_toolbar()
        
        # منطقة النموذج الرئيسية
        self.create_form_area()
        
        # شريط الحالة والأزرار
        self.create_footer()
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = create_enhanced_rtl_frame(
            self.main_frame,
            style='container_elevated'
        )
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.configure(height=70)
        header_frame.pack_propagate(False)
        
        # أيقونة ونص العنوان
        title_section = create_enhanced_rtl_frame(header_frame, style='container_primary')
        title_section.pack(side='right', padx=20, pady=15)
        
        # العنوان الرئيسي
        title_text = {
            'add': '🚢 إضافة شحنة جديدة',
            'edit': '✏️ تعديل بيانات الشحنة',
            'duplicate': '📋 نسخ شحنة موجودة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }
        
        main_title = create_enhanced_rtl_label(
            title_section,
            text=title_text.get(self.mode, 'نموذج الشحنة'),
            style='text_heading_xl'
        )
        main_title.pack(anchor='e')
        
        # معلومات إضافية
        if self.mode in ['edit', 'duplicate', 'view'] and self.shipment_data:
            subtitle = create_enhanced_rtl_label(
                title_section,
                text=f"رقم الشحنة: {self.shipment_data.get('shipment_number', 'غير محدد')}",
                style='text_body_lg'
            )
            subtitle.pack(anchor='e', pady=(5, 0))
        
        # معلومات المستخدم والوقت
        info_section = create_enhanced_rtl_frame(header_frame, style='container_primary')
        info_section.pack(side='left', padx=20, pady=15)
        
        user_info = create_enhanced_rtl_label(
            info_section,
            text=f"👤 المستخدم: {auth_manager.get_current_user().get('username', 'غير محدد')}",
            style='text_body_md'
        )
        user_info.pack(anchor='w')
        
        time_info = create_enhanced_rtl_label(
            info_section,
            text=f"📅 {datetime.now().strftime('%Y/%m/%d - %H:%M')}",
            style='text_body_sm'
        )
        time_info.pack(anchor='w', pady=(5, 0))
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = create_enhanced_rtl_toolbar(self.main_frame)
        self.toolbar.pack(fill='x', pady=(0, 10))
        
        # أزرار الحفظ والإجراءات
        if self.mode != 'view':
            self.toolbar.add_button("💾 حفظ", self.save_shipment, "button_success", "💾")
            self.toolbar.add_button("💾 حفظ ومتابعة", self.save_and_continue, "button_primary", "💾")
        
        # أزرار مشتركة
        self.toolbar.add_button("📋 نسخ البيانات", self.copy_data, "button_secondary", "📋")
        self.toolbar.add_button("📤 تصدير", self.export_data, "button_ghost", "📤")
        self.toolbar.add_button("🖨️ طباعة", self.print_form, "button_ghost", "🖨️")
        
        # فاصل
        separator = tk.Frame(self.toolbar.buttons_frame, width=2, bg=COLORS['border'])
        separator.pack(side='right', fill='y', padx=10)
        
        # أزرار التنقل
        if self.mode != 'view':
            self.toolbar.add_button("🔄 إعادة تعيين", self.reset_form, "button_outline", "🔄")
        
        self.toolbar.add_button("❌ إغلاق", self.close_form, "button_danger", "❌")
        self.toolbar.add_button("❓ مساعدة", self.show_help, "button_ghost", "❓")
    
    def create_form_area(self):
        """إنشاء منطقة النموذج الرئيسية"""
        # إطار قابل للتمرير
        canvas = tk.Canvas(self.main_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=canvas.yview)
        self.form_container = create_enhanced_rtl_frame(canvas, style='container_primary')
        
        self.form_container.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.form_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط منطقة النموذج
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # إنشاء أقسام النموذج
        self.create_form_sections()
    
    def create_form_sections(self):
        """إنشاء أقسام النموذج"""
        # قسم المعلومات الأساسية
        self.create_basic_info_section()
        
        # قسم معلومات الشحن
        self.create_shipping_info_section()
        
        # قسم معلومات الحاوية
        self.create_container_info_section()
        
        # قسم المعلومات المالية
        self.create_financial_info_section()
        
        # قسم التتبع والحالة
        self.create_tracking_section()
        
        # قسم الملاحظات والمرفقات
        self.create_notes_section()
    
    def create_basic_info_section(self):
        """إنشاء قسم المعلومات الأساسية"""
        section = self.create_form_section("📋 المعلومات الأساسية")
        
        # الصف الأول
        row1 = create_enhanced_rtl_frame(section, style='container_primary')
        row1.pack(fill='x', pady=(0, 15))
        
        # رقم الشحنة
        shipment_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        shipment_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        shipment_label = create_enhanced_rtl_label(
            shipment_frame,
            text="🔢 رقم الشحنة *",
            style='text_body_md'
        )
        shipment_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['shipment_number'] = tk.StringVar()
        shipment_entry = create_enhanced_rtl_entry(
            shipment_frame,
            textvariable=self.form_vars['shipment_number'],
            style='input_primary'
        )
        shipment_entry.pack(fill='x', ipady=8)
        
        # إنشاء رقم تلقائي للشحنات الجديدة
        if self.mode == 'add':
            auto_number = self.generate_shipment_number()
            self.form_vars['shipment_number'].set(auto_number)
        
        # المورد
        supplier_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        supplier_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        supplier_label = create_enhanced_rtl_label(
            supplier_frame,
            text="🏢 المورد *",
            style='text_body_md'
        )
        supplier_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['supplier_id'] = tk.StringVar()
        self.supplier_combo = create_enhanced_rtl_combobox(
            supplier_frame,
            textvariable=self.form_vars['supplier_id'],
            style='combobox_primary'
        )
        self.supplier_combo.pack(fill='x', ipady=8)
        
        # تاريخ الشحن
        date_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        date_frame.pack(side='right', fill='x', expand=True)
        
        date_label = create_enhanced_rtl_label(
            date_frame,
            text="📅 تاريخ الشحن *",
            style='text_body_md'
        )
        date_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['shipment_date'] = tk.StringVar()
        date_entry = create_enhanced_rtl_entry(
            date_frame,
            textvariable=self.form_vars['shipment_date'],
            style='input_primary',
            placeholder="YYYY-MM-DD"
        )
        date_entry.pack(fill='x', ipady=8)
        
        # تعيين التاريخ الحالي للشحنات الجديدة
        if self.mode == 'add':
            self.form_vars['shipment_date'].set(date.today().strftime('%Y-%m-%d'))
        
        # الصف الثاني
        row2 = create_enhanced_rtl_frame(section, style='container_primary')
        row2.pack(fill='x', pady=(15, 0))
        
        # الحالة
        status_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        status_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        status_label = create_enhanced_rtl_label(
            status_frame,
            text="📊 حالة الشحنة *",
            style='text_body_md'
        )
        status_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['status'] = tk.StringVar()
        self.status_combo = create_enhanced_rtl_combobox(
            status_frame,
            textvariable=self.form_vars['status'],
            style='combobox_primary'
        )
        self.status_combo['values'] = list(SHIPMENT_STATUS.values())
        self.status_combo.pack(fill='x', ipady=8)
        
        # تعيين الحالة الافتراضية
        if self.mode == 'add':
            self.form_vars['status'].set('في الانتظار')
        
        # تاريخ الوصول المتوقع
        arrival_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        arrival_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        arrival_label = create_enhanced_rtl_label(
            arrival_frame,
            text="🏁 تاريخ الوصول المتوقع",
            style='text_body_md'
        )
        arrival_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['expected_arrival_date'] = tk.StringVar()
        arrival_entry = create_enhanced_rtl_entry(
            arrival_frame,
            textvariable=self.form_vars['expected_arrival_date'],
            style='input_primary',
            placeholder="YYYY-MM-DD"
        )
        arrival_entry.pack(fill='x', ipady=8)
        
        # الأولوية (محسوبة تلقائياً)
        priority_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        priority_frame.pack(side='right', fill='x', expand=True)
        
        priority_label = create_enhanced_rtl_label(
            priority_frame,
            text="⚡ الأولوية (تلقائية)",
            style='text_body_md'
        )
        priority_label.pack(anchor='e', pady=(0, 5))
        
        self.priority_display = create_enhanced_rtl_label(
            priority_frame,
            text="عادي ⚪",
            style='text_body_lg'
        )
        self.priority_display.pack(anchor='e', pady=(8, 0))
    
    def create_form_section(self, title):
        """إنشاء قسم في النموذج"""
        section_card = create_enhanced_rtl_card(
            self.form_container,
            title=title,
            style='container_card'
        )
        section_card.pack(fill='x', padx=10, pady=10)
        
        # إطار المحتوى
        content_frame = create_enhanced_rtl_frame(section_card, style='container_primary')
        content_frame.pack(fill='x', padx=20, pady=15)
        
        return content_frame
    
    def generate_shipment_number(self):
        """إنشاء رقم شحنة تلقائي"""
        try:
            # الحصول على آخر رقم شحنة
            last_shipment = self.db_manager.fetch_one(
                "SELECT shipment_number FROM shipments ORDER BY created_at DESC LIMIT 1"
            )
            
            if last_shipment and last_shipment['shipment_number']:
                # استخراج الرقم من آخر شحنة
                last_number = last_shipment['shipment_number']
                if last_number.startswith('SH-'):
                    try:
                        number_part = int(last_number.split('-')[1])
                        new_number = number_part + 1
                        return f"SH-{new_number:06d}"
                    except:
                        pass
            
            # إذا لم يوجد رقم سابق أو حدث خطأ
            return f"SH-{1:06d}"
            
        except Exception as e:
            # في حالة الخطأ، استخدم التاريخ والوقت
            return f"SH-{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def create_shipping_info_section(self):
        """إنشاء قسم معلومات الشحن"""
        section = self.create_form_section("🚢 معلومات الشحن والنقل")

        # الصف الأول - الموانئ
        row1 = create_enhanced_rtl_frame(section, style='container_primary')
        row1.pack(fill='x', pady=(0, 15))

        # ميناء المغادرة
        dep_port_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        dep_port_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        dep_port_label = create_enhanced_rtl_label(
            dep_port_frame,
            text="🚢 ميناء المغادرة",
            style='text_body_md'
        )
        dep_port_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['departure_port'] = tk.StringVar()
        self.dep_port_combo = create_enhanced_rtl_combobox(
            dep_port_frame,
            textvariable=self.form_vars['departure_port'],
            style='combobox_primary'
        )
        self.dep_port_combo['values'] = list(PORTS.keys())
        self.dep_port_combo.pack(fill='x', ipady=8)

        # ميناء الوصول
        arr_port_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        arr_port_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        arr_port_label = create_enhanced_rtl_label(
            arr_port_frame,
            text="🏁 ميناء الوصول",
            style='text_body_md'
        )
        arr_port_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['arrival_port'] = tk.StringVar()
        self.arr_port_combo = create_enhanced_rtl_combobox(
            arr_port_frame,
            textvariable=self.form_vars['arrival_port'],
            style='combobox_primary'
        )
        self.arr_port_combo['values'] = list(PORTS.keys())
        self.arr_port_combo.pack(fill='x', ipady=8)

        # شركة الشحن
        shipping_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        shipping_frame.pack(side='right', fill='x', expand=True)

        shipping_label = create_enhanced_rtl_label(
            shipping_frame,
            text="🏢 شركة الشحن",
            style='text_body_md'
        )
        shipping_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['shipping_company'] = tk.StringVar()
        self.shipping_combo = create_enhanced_rtl_combobox(
            shipping_frame,
            textvariable=self.form_vars['shipping_company'],
            style='combobox_primary'
        )
        self.shipping_combo['values'] = list(SHIPPING_COMPANIES.keys())
        self.shipping_combo.pack(fill='x', ipady=8)

        # الصف الثاني - تفاصيل إضافية
        row2 = create_enhanced_rtl_frame(section, style='container_primary')
        row2.pack(fill='x', pady=(15, 0))

        # رقم بوليصة الشحن
        bol_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        bol_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        bol_label = create_enhanced_rtl_label(
            bol_frame,
            text="📋 رقم بوليصة الشحن",
            style='text_body_md'
        )
        bol_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['bill_of_lading'] = tk.StringVar()
        bol_entry = create_enhanced_rtl_entry(
            bol_frame,
            textvariable=self.form_vars['bill_of_lading'],
            style='input_primary'
        )
        bol_entry.pack(fill='x', ipady=8)

        # تاريخ المغادرة الفعلي
        actual_dep_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        actual_dep_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        actual_dep_label = create_enhanced_rtl_label(
            actual_dep_frame,
            text="📅 تاريخ المغادرة الفعلي",
            style='text_body_md'
        )
        actual_dep_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['actual_departure_date'] = tk.StringVar()
        actual_dep_entry = create_enhanced_rtl_entry(
            actual_dep_frame,
            textvariable=self.form_vars['actual_departure_date'],
            style='input_primary',
            placeholder="YYYY-MM-DD"
        )
        actual_dep_entry.pack(fill='x', ipady=8)

        # تاريخ الوصول الفعلي
        actual_arr_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        actual_arr_frame.pack(side='right', fill='x', expand=True)

        actual_arr_label = create_enhanced_rtl_label(
            actual_arr_frame,
            text="🏁 تاريخ الوصول الفعلي",
            style='text_body_md'
        )
        actual_arr_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['actual_arrival_date'] = tk.StringVar()
        actual_arr_entry = create_enhanced_rtl_entry(
            actual_arr_frame,
            textvariable=self.form_vars['actual_arrival_date'],
            style='input_primary',
            placeholder="YYYY-MM-DD"
        )
        actual_arr_entry.pack(fill='x', ipady=8)

    def create_container_info_section(self):
        """إنشاء قسم معلومات الحاوية"""
        section = self.create_form_section("📦 معلومات الحاوية والبضائع")

        # الصف الأول
        row1 = create_enhanced_rtl_frame(section, style='container_primary')
        row1.pack(fill='x', pady=(0, 15))

        # رقم الحاوية
        container_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        container_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        container_label = create_enhanced_rtl_label(
            container_frame,
            text="📦 رقم الحاوية",
            style='text_body_md'
        )
        container_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['container_number'] = tk.StringVar()
        container_entry = create_enhanced_rtl_entry(
            container_frame,
            textvariable=self.form_vars['container_number'],
            style='input_primary'
        )
        container_entry.pack(fill='x', ipady=8)

        # نوع الحاوية
        container_type_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        container_type_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        container_type_label = create_enhanced_rtl_label(
            container_type_frame,
            text="📏 نوع الحاوية",
            style='text_body_md'
        )
        container_type_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['container_type'] = tk.StringVar()
        self.container_type_combo = create_enhanced_rtl_combobox(
            container_type_frame,
            textvariable=self.form_vars['container_type'],
            style='combobox_primary'
        )
        self.container_type_combo['values'] = list(CONTAINER_TYPES.keys())
        self.container_type_combo.pack(fill='x', ipady=8)

        # رقم الختم
        seal_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        seal_frame.pack(side='right', fill='x', expand=True)

        seal_label = create_enhanced_rtl_label(
            seal_frame,
            text="🔒 رقم الختم",
            style='text_body_md'
        )
        seal_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['seal_number'] = tk.StringVar()
        seal_entry = create_enhanced_rtl_entry(
            seal_frame,
            textvariable=self.form_vars['seal_number'],
            style='input_primary'
        )
        seal_entry.pack(fill='x', ipady=8)

        # الصف الثاني - الأوزان والأحجام
        row2 = create_enhanced_rtl_frame(section, style='container_primary')
        row2.pack(fill='x', pady=(15, 0))

        # الوزن الإجمالي
        weight_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        weight_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        weight_label = create_enhanced_rtl_label(
            weight_frame,
            text="⚖️ الوزن الإجمالي (كجم)",
            style='text_body_md'
        )
        weight_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['weight'] = tk.StringVar()
        weight_entry = create_enhanced_rtl_entry(
            weight_frame,
            textvariable=self.form_vars['weight'],
            style='input_primary'
        )
        weight_entry.pack(fill='x', ipady=8)

        # الحجم
        volume_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        volume_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        volume_label = create_enhanced_rtl_label(
            volume_frame,
            text="📐 الحجم (م³)",
            style='text_body_md'
        )
        volume_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['volume'] = tk.StringVar()
        volume_entry = create_enhanced_rtl_entry(
            volume_frame,
            textvariable=self.form_vars['volume'],
            style='input_primary'
        )
        volume_entry.pack(fill='x', ipady=8)

        # عدد القطع
        pieces_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        pieces_frame.pack(side='right', fill='x', expand=True)

        pieces_label = create_enhanced_rtl_label(
            pieces_frame,
            text="📊 عدد القطع",
            style='text_body_md'
        )
        pieces_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['pieces_count'] = tk.StringVar()
        pieces_entry = create_enhanced_rtl_entry(
            pieces_frame,
            textvariable=self.form_vars['pieces_count'],
            style='input_primary'
        )
        pieces_entry.pack(fill='x', ipady=8)

    def create_financial_info_section(self):
        """إنشاء قسم المعلومات المالية"""
        section = self.create_form_section("💰 المعلومات المالية والتكاليف")

        # الصف الأول - القيم الأساسية
        row1 = create_enhanced_rtl_frame(section, style='container_primary')
        row1.pack(fill='x', pady=(0, 15))

        # العملة
        currency_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        currency_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        currency_label = create_enhanced_rtl_label(
            currency_frame,
            text="💱 العملة",
            style='text_body_md'
        )
        currency_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['currency'] = tk.StringVar()
        self.currency_combo = create_enhanced_rtl_combobox(
            currency_frame,
            textvariable=self.form_vars['currency'],
            style='combobox_primary'
        )
        self.currency_combo['values'] = list(CURRENCIES.keys())
        self.currency_combo.set('USD')  # افتراضي
        self.currency_combo.pack(fill='x', ipady=8)

        # القيمة الإجمالية
        total_value_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        total_value_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        total_value_label = create_enhanced_rtl_label(
            total_value_frame,
            text="💰 القيمة الإجمالية",
            style='text_body_md'
        )
        total_value_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['total_value'] = tk.StringVar()
        total_value_entry = create_enhanced_rtl_entry(
            total_value_frame,
            textvariable=self.form_vars['total_value'],
            style='input_primary'
        )
        total_value_entry.pack(fill='x', ipady=8)

        # تكلفة الشحن
        shipping_cost_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        shipping_cost_frame.pack(side='right', fill='x', expand=True)

        shipping_cost_label = create_enhanced_rtl_label(
            shipping_cost_frame,
            text="🚢 تكلفة الشحن",
            style='text_body_md'
        )
        shipping_cost_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['shipping_cost'] = tk.StringVar()
        shipping_cost_entry = create_enhanced_rtl_entry(
            shipping_cost_frame,
            textvariable=self.form_vars['shipping_cost'],
            style='input_primary'
        )
        shipping_cost_entry.pack(fill='x', ipady=8)

        # الصف الثاني - التكاليف الإضافية
        row2 = create_enhanced_rtl_frame(section, style='container_primary')
        row2.pack(fill='x', pady=(15, 15))

        # رسوم إضافية
        additional_fees_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        additional_fees_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        additional_fees_label = create_enhanced_rtl_label(
            additional_fees_frame,
            text="💸 رسوم إضافية",
            style='text_body_md'
        )
        additional_fees_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['additional_fees'] = tk.StringVar()
        additional_fees_entry = create_enhanced_rtl_entry(
            additional_fees_frame,
            textvariable=self.form_vars['additional_fees'],
            style='input_primary'
        )
        additional_fees_entry.pack(fill='x', ipady=8)

        # رسوم التأمين
        insurance_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        insurance_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        insurance_label = create_enhanced_rtl_label(
            insurance_frame,
            text="🛡️ رسوم التأمين",
            style='text_body_md'
        )
        insurance_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['insurance_cost'] = tk.StringVar()
        insurance_entry = create_enhanced_rtl_entry(
            insurance_frame,
            textvariable=self.form_vars['insurance_cost'],
            style='input_primary'
        )
        insurance_entry.pack(fill='x', ipady=8)

        # رسوم الجمارك
        customs_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        customs_frame.pack(side='right', fill='x', expand=True)

        customs_label = create_enhanced_rtl_label(
            customs_frame,
            text="🏛️ رسوم الجمارك",
            style='text_body_md'
        )
        customs_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['customs_fees'] = tk.StringVar()
        customs_entry = create_enhanced_rtl_entry(
            customs_frame,
            textvariable=self.form_vars['customs_fees'],
            style='input_primary'
        )
        customs_entry.pack(fill='x', ipady=8)

        # الصف الثالث - معلومات الدفع
        row3 = create_enhanced_rtl_frame(section, style='container_primary')
        row3.pack(fill='x', pady=(15, 0))

        # طريقة الدفع
        payment_method_frame = create_enhanced_rtl_frame(row3, style='container_primary')
        payment_method_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        payment_method_label = create_enhanced_rtl_label(
            payment_method_frame,
            text="💳 طريقة الدفع",
            style='text_body_md'
        )
        payment_method_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['payment_method'] = tk.StringVar()
        self.payment_method_combo = create_enhanced_rtl_combobox(
            payment_method_frame,
            textvariable=self.form_vars['payment_method'],
            style='combobox_primary'
        )
        self.payment_method_combo['values'] = list(PAYMENT_METHODS.keys())
        self.payment_method_combo.pack(fill='x', ipady=8)

        # حالة الدفع
        payment_status_frame = create_enhanced_rtl_frame(row3, style='container_primary')
        payment_status_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        payment_status_label = create_enhanced_rtl_label(
            payment_status_frame,
            text="💰 حالة الدفع",
            style='text_body_md'
        )
        payment_status_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['payment_status'] = tk.StringVar()
        self.payment_status_combo = create_enhanced_rtl_combobox(
            payment_status_frame,
            textvariable=self.form_vars['payment_status'],
            style='combobox_primary'
        )
        self.payment_status_combo['values'] = ['لم يتم الدفع', 'دفع جزئي', 'تم الدفع بالكامل', 'مسترد']
        self.payment_status_combo.set('لم يتم الدفع')
        self.payment_status_combo.pack(fill='x', ipady=8)

        # المبلغ المدفوع
        paid_amount_frame = create_enhanced_rtl_frame(row3, style='container_primary')
        paid_amount_frame.pack(side='right', fill='x', expand=True)

        paid_amount_label = create_enhanced_rtl_label(
            paid_amount_frame,
            text="💵 المبلغ المدفوع",
            style='text_body_md'
        )
        paid_amount_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['paid_amount'] = tk.StringVar()
        paid_amount_entry = create_enhanced_rtl_entry(
            paid_amount_frame,
            textvariable=self.form_vars['paid_amount'],
            style='input_primary'
        )
        paid_amount_entry.pack(fill='x', ipady=8)

    def create_tracking_section(self):
        """إنشاء قسم التتبع والحالة"""
        section = self.create_form_section("📊 التتبع والحالة المتقدمة")

        # الصف الأول - معلومات التتبع
        row1 = create_enhanced_rtl_frame(section, style='container_primary')
        row1.pack(fill='x', pady=(0, 15))

        # رقم التتبع
        tracking_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        tracking_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        tracking_label = create_enhanced_rtl_label(
            tracking_frame,
            text="🔍 رقم التتبع",
            style='text_body_md'
        )
        tracking_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['tracking_number'] = tk.StringVar()
        tracking_entry = create_enhanced_rtl_entry(
            tracking_frame,
            textvariable=self.form_vars['tracking_number'],
            style='input_primary'
        )
        tracking_entry.pack(fill='x', ipady=8)

        # الموقع الحالي
        location_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        location_frame.pack(side='right', fill='x', expand=True, padx=(0, 15))

        location_label = create_enhanced_rtl_label(
            location_frame,
            text="📍 الموقع الحالي",
            style='text_body_md'
        )
        location_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['current_location'] = tk.StringVar()
        location_entry = create_enhanced_rtl_entry(
            location_frame,
            textvariable=self.form_vars['current_location'],
            style='input_primary'
        )
        location_entry.pack(fill='x', ipady=8)

        # آخر تحديث
        last_update_frame = create_enhanced_rtl_frame(row1, style='container_primary')
        last_update_frame.pack(side='right', fill='x', expand=True)

        last_update_label = create_enhanced_rtl_label(
            last_update_frame,
            text="🕐 آخر تحديث",
            style='text_body_md'
        )
        last_update_label.pack(anchor='e', pady=(0, 5))

        self.last_update_display = create_enhanced_rtl_label(
            last_update_frame,
            text=datetime.now().strftime('%Y/%m/%d %H:%M'),
            style='text_body_lg'
        )
        self.last_update_display.pack(anchor='e', pady=(8, 0))

        # الصف الثاني - حالة التقدم
        row2 = create_enhanced_rtl_frame(section, style='container_primary')
        row2.pack(fill='x', pady=(15, 0))

        # شريط التقدم
        progress_frame = create_enhanced_rtl_frame(row2, style='container_primary')
        progress_frame.pack(fill='x', pady=(0, 10))

        progress_label = create_enhanced_rtl_label(
            progress_frame,
            text="📊 نسبة التقدم",
            style='text_body_md'
        )
        progress_label.pack(anchor='e', pady=(0, 5))

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='determinate',
            length=400,
            value=0
        )
        self.progress_bar.pack(anchor='e', pady=(5, 0))

        self.progress_label = create_enhanced_rtl_label(
            progress_frame,
            text="0%",
            style='text_body_md'
        )
        self.progress_label.pack(anchor='e', pady=(5, 0))

    def create_notes_section(self):
        """إنشاء قسم الملاحظات والمرفقات"""
        section = self.create_form_section("📝 الملاحظات والمرفقات")

        # منطقة الملاحظات
        notes_frame = create_enhanced_rtl_frame(section, style='container_primary')
        notes_frame.pack(fill='x', pady=(0, 15))

        notes_label = create_enhanced_rtl_label(
            notes_frame,
            text="📝 ملاحظات إضافية",
            style='text_body_md'
        )
        notes_label.pack(anchor='e', pady=(0, 5))

        self.form_vars['notes'] = tk.StringVar()
        self.notes_text = create_enhanced_rtl_text(
            notes_frame,
            height=6,
            style='input_primary'
        )
        self.notes_text.pack(fill='x', pady=(5, 0))

        # منطقة المرفقات
        attachments_frame = create_enhanced_rtl_frame(section, style='container_primary')
        attachments_frame.pack(fill='x', pady=(15, 0))

        attachments_label = create_enhanced_rtl_label(
            attachments_frame,
            text="📎 المرفقات والوثائق",
            style='text_body_md'
        )
        attachments_label.pack(anchor='e', pady=(0, 10))

        # أزرار المرفقات
        attachments_buttons = create_enhanced_rtl_frame(attachments_frame, style='container_primary')
        attachments_buttons.pack(fill='x')

        add_file_btn = create_enhanced_rtl_button(
            attachments_buttons,
            text="📎 إضافة ملف",
            style='button_secondary',
            command=self.add_attachment
        )
        add_file_btn.pack(side='right', padx=5)

        view_files_btn = create_enhanced_rtl_button(
            attachments_buttons,
            text="👁️ عرض الملفات",
            style='button_ghost',
            command=self.view_attachments
        )
        view_files_btn.pack(side='right', padx=5)

        # قائمة المرفقات
        self.attachments_list = tk.Listbox(
            attachments_frame,
            height=4,
            font=advanced_style_manager.get_font('arabic_body'),
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            selectbackground=COLORS['primary_light']
        )
        self.attachments_list.pack(fill='x', pady=(10, 0))

    def create_footer(self):
        """إنشاء شريط الحالة والأزرار"""
        footer_frame = create_enhanced_rtl_frame(
            self.main_frame,
            style='container_elevated'
        )
        footer_frame.pack(fill='x', side='bottom', pady=(10, 0))
        footer_frame.configure(height=60)
        footer_frame.pack_propagate(False)

        # الجانب الأيمن - أزرار الإجراءات
        actions_frame = create_enhanced_rtl_frame(footer_frame, style='container_primary')
        actions_frame.pack(side='right', padx=20, pady=15)

        if self.mode != 'view':
            # زر الحفظ الرئيسي
            save_btn = create_enhanced_rtl_button(
                actions_frame,
                text="💾 حفظ",
                style='button_success',
                command=self.save_shipment
            )
            save_btn.pack(side='right', padx=5)

            # زر الحفظ والإغلاق
            save_close_btn = create_enhanced_rtl_button(
                actions_frame,
                text="💾 حفظ وإغلاق",
                style='button_primary',
                command=self.save_and_close
            )
            save_close_btn.pack(side='right', padx=5)

        # زر الإغلاق
        close_btn = create_enhanced_rtl_button(
            actions_frame,
            text="❌ إغلاق",
            style='button_danger',
            command=self.close_form
        )
        close_btn.pack(side='right', padx=5)

        # الجانب الأيسر - معلومات الحالة
        status_frame = create_enhanced_rtl_frame(footer_frame, style='container_primary')
        status_frame.pack(side='left', padx=20, pady=15)

        self.status_label = create_enhanced_rtl_label(
            status_frame,
            text="جاهز",
            style='text_body_sm'
        )
        self.status_label.pack(side='left')

        # مؤشر التعديل
        self.modified_indicator = create_enhanced_rtl_label(
            status_frame,
            text="",
            style='text_body_sm'
        )
        self.modified_indicator.pack(side='left', padx=(10, 0))

    def load_form_data(self):
        """تحميل بيانات النموذج"""
        try:
            # تحميل قائمة الموردين
            self.load_suppliers()

            # تحميل بيانات الشحنة إذا كانت موجودة
            if self.shipment_data and self.mode in ['edit', 'duplicate', 'view']:
                self.populate_form_data()

            # تطبيق قيود العرض فقط
            if self.mode == 'view':
                self.make_form_readonly()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all(
                "SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            )

            supplier_values = [f"{s['supplier_name']} (ID: {s['id']})" for s in suppliers]
            self.supplier_combo['values'] = supplier_values

            # حفظ بيانات الموردين للمرجع
            self.suppliers_data = suppliers

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def populate_form_data(self):
        """ملء النموذج بالبيانات"""
        try:
            # ملء جميع الحقول
            for field, var in self.form_vars.items():
                value = self.shipment_data.get(field, '')
                if value:
                    var.set(str(value))

            # معالجة خاصة للمورد
            supplier_id = self.shipment_data.get('supplier_id')
            if supplier_id:
                for supplier in self.suppliers_data:
                    if supplier['id'] == supplier_id:
                        supplier_text = f"{supplier['supplier_name']} (ID: {supplier['id']})"
                        self.form_vars['supplier_id'].set(supplier_text)
                        break

            # ملء منطقة الملاحظات
            notes = self.shipment_data.get('notes', '')
            if notes:
                self.notes_text.delete('1.0', tk.END)
                self.notes_text.insert('1.0', notes)

            # تحديث شريط التقدم
            self.update_progress_display()

            # إذا كان الوضع نسخ، مسح بعض الحقول
            if self.mode == 'duplicate':
                self.form_vars['shipment_number'].set(self.generate_shipment_number())
                self.form_vars['status'].set('في الانتظار')
                self.form_vars['tracking_number'].set('')
                self.form_vars['current_location'].set('')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في ملء البيانات: {str(e)}")

    def make_form_readonly(self):
        """جعل النموذج للقراءة فقط"""
        # تعطيل جميع حقول الإدخال
        for widget in self.form_container.winfo_children():
            self.disable_widget_recursively(widget)

    def disable_widget_recursively(self, widget):
        """تعطيل عنصر وجميع عناصره الفرعية"""
        try:
            if hasattr(widget, 'configure'):
                if isinstance(widget, (tk.Entry, ttk.Combobox)):
                    widget.configure(state='readonly')
                elif isinstance(widget, tk.Text):
                    widget.configure(state='disabled')
                elif isinstance(widget, tk.Button):
                    widget.configure(state='disabled')

            # تطبيق على العناصر الفرعية
            for child in widget.winfo_children():
                self.disable_widget_recursively(child)

        except Exception:
            pass

    def update_progress_display(self):
        """تحديث عرض التقدم"""
        try:
            status = self.form_vars.get('status', tk.StringVar()).get()

            # خريطة التقدم
            progress_map = {
                'في الانتظار': 10,
                'مؤكدة': 25,
                'تم الشحن': 40,
                'في الطريق': 60,
                'وصلت': 80,
                'في الجمارك': 85,
                'تم التسليم': 100,
                'ملغية': 0,
                'متأخرة': 50
            }

            progress = progress_map.get(status, 0)
            self.progress_bar['value'] = progress
            self.progress_label.configure(text=f"{progress}%")

        except Exception:
            pass

    def bind_events(self):
        """ربط الأحداث"""
        # ربط تغيير الحالة بتحديث التقدم
        self.form_vars['status'].trace('w', lambda *args: self.update_progress_display())

        # ربط تغيير البيانات بمؤشر التعديل
        for var in self.form_vars.values():
            var.trace('w', self.on_data_change)

        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Control-s>', lambda e: self.save_shipment())
        self.root.bind('<Escape>', lambda e: self.close_form())
        self.root.bind('<F1>', lambda e: self.show_help())

    def on_data_change(self, *args):
        """عند تغيير البيانات"""
        if not self.is_modified:
            self.is_modified = True
            self.modified_indicator.configure(
                text="● تم التعديل",
                fg=COLORS['warning']
            )

    # وظائف الإجراءات
    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return False

            # جمع البيانات
            shipment_data = self.collect_form_data()

            # حفظ في قاعدة البيانات
            if self.mode == 'add' or self.mode == 'duplicate':
                self.create_new_shipment(shipment_data)
            elif self.mode == 'edit':
                self.update_existing_shipment(shipment_data)

            # تحديث المؤشرات
            self.is_modified = False
            self.modified_indicator.configure(text="✓ تم الحفظ", fg=COLORS['success'])

            messagebox.showinfo("نجح", "تم حفظ الشحنة بنجاح")
            return True

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الشحنة: {str(e)}")
            return False

    def validate_form(self):
        """التحقق من صحة النموذج"""
        self.validation_errors = []

        # التحقق من الحقول المطلوبة
        required_fields = {
            'shipment_number': 'رقم الشحنة',
            'supplier_id': 'المورد',
            'shipment_date': 'تاريخ الشحن',
            'status': 'حالة الشحنة'
        }

        for field, label in required_fields.items():
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if not value:
                self.validation_errors.append(f"حقل {label} مطلوب")

        # التحقق من صحة التواريخ
        date_fields = ['shipment_date', 'expected_arrival_date', 'actual_departure_date', 'actual_arrival_date']
        for field in date_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_date(value):
                self.validation_errors.append(f"تاريخ {field} غير صحيح")

        # التحقق من صحة الأرقام
        numeric_fields = ['total_value', 'shipping_cost', 'weight', 'volume', 'pieces_count']
        for field in numeric_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_number(value):
                self.validation_errors.append(f"قيمة {field} يجب أن تكون رقماً")

        # عرض الأخطاء إن وجدت
        if self.validation_errors:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in self.validation_errors)
            messagebox.showerror("أخطاء في البيانات", error_message)
            return False

        return True

    def is_valid_date(self, date_string):
        """التحقق من صحة التاريخ"""
        try:
            datetime.strptime(date_string, '%Y-%m-%d')
            return True
        except:
            return False

    def is_valid_number(self, number_string):
        """التحقق من صحة الرقم"""
        try:
            float(number_string)
            return True
        except:
            return False

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        data = {}

        # جمع البيانات من المتغيرات
        for field, var in self.form_vars.items():
            value = var.get().strip()
            data[field] = value if value else None

        # معالجة خاصة للمورد
        supplier_text = self.form_vars.get('supplier_id', tk.StringVar()).get()
        if supplier_text and '(ID:' in supplier_text:
            try:
                supplier_id = int(supplier_text.split('(ID:')[1].split(')')[0].strip())
                data['supplier_id'] = supplier_id
            except:
                data['supplier_id'] = None

        # جمع الملاحظات
        notes = self.notes_text.get('1.0', tk.END).strip()
        data['notes'] = notes if notes else None

        # إضافة معلومات النظام
        current_user = auth_manager.get_current_user()
        data['created_by'] = current_user.get('username', 'system')
        data['updated_at'] = datetime.now().isoformat()

        if self.mode == 'add' or self.mode == 'duplicate':
            data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.now().isoformat()

        return data

    def create_new_shipment(self, data):
        """إنشاء شحنة جديدة"""
        query = """
            INSERT INTO shipments (
                id, shipment_number, supplier_id, status, shipment_date, expected_arrival_date,
                departure_port, arrival_port, shipping_company, container_number, bill_of_lading,
                container_type, weight, volume, currency, total_value, shipping_cost,
                additional_fees, insurance_cost, customs_fees, payment_method, payment_status,
                notes, created_by, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
        """

        params = (
            data.get('id'),
            data.get('shipment_number'),
            data.get('supplier_id'),
            data.get('status'),
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('departure_port'),
            data.get('arrival_port'),
            data.get('shipping_company'),
            data.get('container_number'),
            data.get('bill_of_lading'),
            data.get('container_type'),
            data.get('weight'),
            data.get('volume'),
            data.get('currency'),
            data.get('total_value'),
            data.get('shipping_cost'),
            data.get('additional_fees'),
            data.get('insurance_cost'),
            data.get('customs_fees'),
            data.get('payment_method'),
            data.get('payment_status'),
            data.get('notes'),
            data.get('created_by'),
            data.get('created_at'),
            data.get('updated_at')
        )

        self.db_manager.execute_query(query, params)

    def update_existing_shipment(self, data):
        """تحديث شحنة موجودة"""
        query = """
            UPDATE shipments SET
                shipment_number = ?, supplier_id = ?, status = ?, shipment_date = ?,
                expected_arrival_date = ?, departure_port = ?, arrival_port = ?,
                shipping_company = ?, container_number = ?, bill_of_lading = ?,
                container_type = ?, weight = ?, volume = ?, currency = ?,
                total_value = ?, shipping_cost = ?, additional_fees = ?,
                insurance_cost = ?, customs_fees = ?, payment_method = ?,
                payment_status = ?, notes = ?, updated_at = ?
            WHERE id = ?
        """

        params = (
            data.get('shipment_number'),
            data.get('supplier_id'),
            data.get('status'),
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('departure_port'),
            data.get('arrival_port'),
            data.get('shipping_company'),
            data.get('container_number'),
            data.get('bill_of_lading'),
            data.get('container_type'),
            data.get('weight'),
            data.get('volume'),
            data.get('currency'),
            data.get('total_value'),
            data.get('shipping_cost'),
            data.get('additional_fees'),
            data.get('insurance_cost'),
            data.get('customs_fees'),
            data.get('payment_method'),
            data.get('payment_status'),
            data.get('notes'),
            data.get('updated_at'),
            self.shipment_data.get('id')
        )

        self.db_manager.execute_query(query, params)

    def save_and_continue(self):
        """حفظ ومتابعة"""
        if self.save_shipment():
            # إعادة تعيين النموذج لشحنة جديدة
            self.reset_form()
            self.mode = 'add'
            self.shipment_data = {}

            # تحديث العنوان
            self.root.title("➕ إضافة شحنة جديدة")

    def save_and_close(self):
        """حفظ وإغلاق"""
        if self.save_shipment():
            self.close_form()

    def reset_form(self):
        """إعادة تعيين النموذج"""
        # مسح جميع الحقول
        for var in self.form_vars.values():
            var.set('')

        # مسح منطقة الملاحظات
        self.notes_text.delete('1.0', tk.END)

        # إعادة تعيين القيم الافتراضية
        if self.mode == 'add':
            self.form_vars['shipment_number'].set(self.generate_shipment_number())
            self.form_vars['shipment_date'].set(date.today().strftime('%Y-%m-%d'))
            self.form_vars['status'].set('في الانتظار')
            self.form_vars['currency'].set('USD')
            self.form_vars['payment_status'].set('لم يتم الدفع')

        # إعادة تعيين مؤشرات الحالة
        self.is_modified = False
        self.modified_indicator.configure(text="")
        self.update_progress_display()

    def close_form(self):
        """إغلاق النموذج"""
        if self.is_modified:
            result = messagebox.askyesnocancel(
                "تأكيد الإغلاق",
                "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟"
            )

            if result is True:  # نعم - احفظ وأغلق
                if not self.save_shipment():
                    return
            elif result is None:  # إلغاء
                return
            # False = لا - أغلق بدون حفظ

        self.root.destroy()

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.close_form()

    # وظائف إضافية
    def copy_data(self):
        """نسخ البيانات"""
        try:
            data = self.collect_form_data()
            data_json = json.dumps(data, ensure_ascii=False, indent=2)

            self.root.clipboard_clear()
            self.root.clipboard_append(data_json)

            messagebox.showinfo("تم النسخ", "تم نسخ بيانات الشحنة إلى الحافظة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نسخ البيانات: {str(e)}")

    def export_data(self):
        """تصدير البيانات"""
        try:
            filename = filedialog.asksaveasfilename(
                title="تصدير بيانات الشحنة",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                data = self.collect_form_data()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("تم التصدير", f"تم تصدير البيانات إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير البيانات: {str(e)}")

    def print_form(self):
        """طباعة النموذج"""
        messagebox.showinfo("قريباً", "ميزة الطباعة ستتم إضافتها قريباً")

    def add_attachment(self):
        """إضافة مرفق"""
        try:
            filename = filedialog.askopenfilename(
                title="اختيار ملف للإرفاق",
                filetypes=[
                    ("PDF files", "*.pdf"),
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("Document files", "*.doc *.docx *.txt"),
                    ("Excel files", "*.xls *.xlsx"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                # إضافة الملف للقائمة
                file_name = os.path.basename(filename)
                self.attachments_list.insert(tk.END, file_name)

                messagebox.showinfo("تم الإرفاق", f"تم إرفاق الملف: {file_name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إرفاق الملف: {str(e)}")

    def view_attachments(self):
        """عرض المرفقات"""
        attachments = list(self.attachments_list.get(0, tk.END))

        if not attachments:
            messagebox.showinfo("لا توجد مرفقات", "لا توجد ملفات مرفقة")
            return

        attachments_text = "\n".join(f"• {att}" for att in attachments)
        messagebox.showinfo("المرفقات", f"الملفات المرفقة:\n\n{attachments_text}")

    def show_help(self):
        """إظهار المساعدة"""
        help_text = """
🚢 نموذج الشحنة المتقدم - المساعدة

📋 الأقسام المتاحة:
• المعلومات الأساسية: رقم الشحنة، المورد، التاريخ، الحالة
• معلومات الشحن: الموانئ، شركة الشحن، بوليصة الشحن
• معلومات الحاوية: رقم الحاوية، النوع، الوزن، الحجم
• المعلومات المالية: القيمة، التكاليف، طريقة الدفع
• التتبع والحالة: رقم التتبع، الموقع، نسبة التقدم
• الملاحظات والمرفقات: ملاحظات إضافية، ملفات مرفقة

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+S: حفظ الشحنة
• Esc: إغلاق النموذج
• F1: هذه المساعدة

✅ التحقق من البيانات:
• الحقول المطلوبة مميزة بعلامة *
• التواريخ يجب أن تكون بصيغة YYYY-MM-DD
• القيم المالية يجب أن تكون أرقام
• رقم الشحنة يجب أن يكون فريد

💡 نصائح:
• يتم إنشاء رقم الشحنة تلقائياً للشحنات الجديدة
• نسبة التقدم تحدث تلقائياً حسب الحالة
• يمكن إرفاق ملفات متعددة مع الشحنة
• البيانات تحفظ تلقائياً عند التبديل بين الحقول
        """

        messagebox.showinfo("مساعدة - نموذج الشحنة", help_text)

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

def show_ultimate_shipment_form(parent, mode='add', shipment_data=None):
    """إظهار نموذج الشحنة المتقدم"""
    form = UltimateShipmentForm(parent, mode, shipment_data)
    return form

if __name__ == "__main__":
    # اختبار النموذج
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    form = UltimateShipmentForm(root, mode='add')
    root.mainloop()
