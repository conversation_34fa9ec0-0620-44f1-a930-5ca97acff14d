# نظام متابعة شحنات الموردين - الإصدار المتقدم
## Advanced Supplier Shipment Tracking System

نظام متكامل لإدارة ومتابعة شحنات الموردين مع واجهة مستخدم عربية متقدمة وأنماط CSS حديثة ودعم كامل لاتجاه RTL.

## 🎨 الميزات الجديدة - واجهة RTL متقدمة

### تصميم CSS متقدم
- **نظام ألوان شامل**: 50+ لون مع تدرجات متناسقة وألوان خاصة بالعربية
- **خطوط عربية محسنة**: Segoe UI مع بدائل متعددة (Tahoma, Arial Unicode MS)
- **تأثيرات تفاعلية**: ظلال وانتقالات سلسة مع hover effects
- **تخطيط RTL طبيعي**: محاذاة يمين كاملة في جميع العناصر

### مكونات RTL متخصصة
- **RTLFrame**: إطارات مع دعم RTL تلقائي
- **RTLButton**: أزرار ملونة مع تأثيرات تفاعلية (6 أنماط)
- **RTLLabel**: تسميات محاذاة يمين مع أنماط متعددة
- **RTLEntry**: حقول إدخال عربية مع تنسيق RTL
- **RTLText**: مناطق نص مع دعم RTL كامل
- **RTLCombobox**: قوائم منسدلة محسنة للعربية
- **RTLTreeview**: جداول مع رؤوس عربية
- **RTLCard**: بطاقات بتصميم متقدم

### نظام أنماط ذكي
- **مدير الأنماط**: RTLStyleManager للتحكم الكامل في التصميم
- **ألوان ديناميكية**: تغيير تلقائي حسب الحالة والتفاعل
- **خطوط متدرجة**: 8 أنواع خطوط بأحجام وسماكات مختلفة
- **مسافات منتظمة**: نظام spacing موحد (xs, sm, md, lg, xl, xxl)
- **تلميحات تفاعلية**: tooltips عربية عند التمرير

## المميزات الرئيسية

### 🚢 إدارة الشحنات المتقدمة
- تتبع شامل للشحنات من المغادرة حتى الوصول
- إدارة 9 حالات مختلفة للشحنة مع تاريخ كامل
- تسجيل تفاصيل الموانئ وشركات الشحن (23 ميناء، 10 شركات)
- متابعة الحاويات وبوالص الشحن مع أرقام التتبع
- نظام إشعارات للشحنات المتأخرة
- تكامل مع APIs شركات الشحن (مخطط)

### 🏢 إدارة الموردين الشاملة
- قاعدة بيانات شاملة مع 20+ حقل لكل مورد
- إدارة معلومات الاتصال والعناوين المتعددة
- تتبع شروط الدفع والحدود الائتمانية
- تقييم أداء الموردين مع مؤشرات KPI
- نظام بحث وتصفية متقدم
- تاريخ كامل للتعاملات مع كل مورد

### 📦 إدارة المخزون والأصناف
- تصنيف الأصناف حسب الفئات الهرمية (غير محدود المستويات)
- تتبع مستويات المخزون والحد الأدنى مع تنبيهات
- إدارة المخازن المتعددة مع تحويلات
- تسجيل حركات المخزون التفصيلية مع أسباب
- إدارة وحدات القياس والأسعار (11 وحدة)
- باركود ورموز QR للأصناف (مخطط)

### 📊 التقارير والإحصائيات المتقدمة
- تقارير شاملة للشحنات والموردين والمخزون
- إحصائيات تفصيلية مع رسوم بيانية تفاعلية
- تصدير التقارير بصيغة Excel و PDF مع تنسيق عربي
- لوحة معلومات تفاعلية مع 12+ مؤشر أداء
- تقارير قابلة للتخصيص مع فلاتر متقدمة
- جدولة التقارير التلقائية (مخطط)

### 🔐 الأمان والصلاحيات المتقدم
- نظام تسجيل دخول آمن مع تشفير SHA-256
- إدارة 5 مستويات صلاحيات (مدير، محاسب، مبيعات، موظف)
- حماية الجلسات مع انتهاء صلاحية تلقائي (60 دقيقة)
- تسجيل العمليات والأنشطة الحساسة
- حماية من هجمات SQL Injection
- نظام قفل الحسابات بعد محاولات فاشلة

### 🌐 دعم اللغة العربية المتقدم
- واجهة مستخدم عربية كاملة مع RTL في كل عنصر
- خطوط عربية محسنة مع fallback fonts
- تخطيط مناسب للغة العربية مع مسافات صحيحة
- دعم الأرقام العربية والتواريخ الهجرية
- قوائم وجداول بمحاذاة يمين طبيعية
- رسائل خطأ ونجاح باللغة العربية

## التقنيات المستخدمة

### البرمجة والتطوير
- **Python 3.8+**: لغة البرمجة الأساسية
- **Tkinter**: واجهة المستخدم الرسومية
- **SQLite**: قاعدة البيانات المدمجة
- **Pandas**: معالجة البيانات والتقارير
- **ReportLab**: إنشاء تقارير PDF
- **OpenPyXL**: تصدير Excel

### التصميم والواجهة
- **CSS-like Styling**: نظام أنماط متقدم
- **RTL Layout Engine**: محرك تخطيط RTL مخصص
- **Color System**: نظام ألوان شامل (50+ لون)
- **Typography System**: نظام خطوط متدرج
- **Component Library**: مكتبة مكونات RTL

### الأمان والأداء
- **SHA-256 Encryption**: تشفير كلمات المرور
- **Session Management**: إدارة الجلسات الآمنة
- **Database Indexing**: فهرسة قاعدة البيانات
- **Memory Optimization**: تحسين استخدام الذاكرة
- **Error Handling**: معالجة شاملة للأخطاء

## هيكل المشروع

```
ship_mh/
├── main.py                 # الملف الرئيسي
├── config/
│   └── config.py          # إعدادات النظام والألوان والخطوط
├── src/
│   ├── auth_manager.py    # إدارة المصادقة والصلاحيات
│   ├── main_window.py     # النافذة الرئيسية المحسنة
│   ├── ui_styles.py       # مدير الأنماط RTL
│   ├── rtl_components.py  # مكونات RTL متخصصة
│   ├── suppliers_window.py # إدارة الموردين
│   ├── items_window.py    # إدارة الأصناف
│   ├── shipments_window.py # إدارة الشحنات
│   ├── reports_manager.py # مدير التقارير
│   ├── reports_window.py  # نافذة التقارير
│   ├── settings_window.py # الإعدادات
│   ├── backup_manager.py  # النسخ الاحتياطي
│   └── users_window.py    # إدارة المستخدمين
├── database/
│   ├── database_manager.py # مدير قاعدة البيانات
│   └── shipment_system.db # قاعدة البيانات
├── reports/               # مجلد التقارير
├── backups/              # النسخ الاحتياطية
├── logs/                 # ملفات السجلات
├── assets/               # الموارد والأيقونات
├── test_system.py        # اختبارات النظام
├── optimize_system.py    # تحسين الأداء
├── demo_rtl_interface.py # عرض توضيحي RTL
└── requirements.txt      # المتطلبات
```

## التثبيت والتشغيل

### المتطلبات
- Windows 10 أو أحدث
- Python 3.8 أو أحدث
- 4 GB RAM كحد أدنى
- 500 MB مساحة قرص

### خطوات التثبيت
1. **تحميل المشروع**:
   ```bash
   git clone [repository-url]
   cd ship_mh
   ```

2. **تثبيت المتطلبات**:
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل النظام**:
   ```bash
   python main.py
   ```
   أو انقر نقراً مزدوجاً على `run.bat`

### بيانات الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## الاختبار والتحسين

### تشغيل الاختبارات
```bash
python test_system.py
```

### تحسين الأداء
```bash
python optimize_system.py
```

### العرض التوضيحي RTL
```bash
python demo_rtl_interface.py
```

## الوثائق

- **دليل المستخدم**: `دليل_المستخدم.md`
- **تطوير واجهة RTL**: `تطوير_واجهة_RTL.md`
- **تحسينات الواجهة**: `تحسينات_الواجهة.md`
- **سجل التغييرات**: `CHANGELOG.md`

## الدعم والمساهمة

### الإبلاغ عن المشاكل
يرجى تضمين:
- وصف مفصل للمشكلة
- خطوات إعادة الإنتاج
- رسائل الخطأ
- ملف السجلات

### المساهمة
نرحب بالمساهمات في:
- تحسين الواجهة العربية
- إضافة ميزات جديدة
- تحسين الأداء
- إصلاح الأخطاء

## الترخيص

جميع الحقوق محفوظة © 2024

---

**تم تطوير هذا النظام خصيصاً للشركات العربية مع التركيز على تجربة مستخدم متميزة وتصميم RTL أصيل** 🚀✨
