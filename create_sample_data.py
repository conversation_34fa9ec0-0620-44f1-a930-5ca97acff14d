#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية لنظام إدارة الشحنات
Create Sample Data for Shipments Management System
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_sample_shipments():
    """إنشاء شحنات تجريبية"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('shipments.db')
    cursor = conn.cursor()
    
    # بيانات تجريبية
    customers = [
        'شركة التجارة العالمية',
        'مؤسسة الخليج للاستيراد',
        'شركة النور للتجارة',
        'مجموعة الأمل التجارية',
        'شركة البحر الأحمر',
        'مؤسسة الشرق الأوسط',
        'شركة الصحراء للتجارة',
        'مجموعة النخيل التجارية'
    ]
    
    suppliers = [
        'مصنع شنغهاي للإلكترونيات',
        'شركة دبي للمنسوجات',
        'مصنع إسطنبول للأثاث',
        'شركة مومباي للتوابل',
        'مصنع بانكوك للأجهزة',
        'شركة سنغافورة للمعدات',
        'مصنع هونغ كونغ للألعاب',
        'شركة كوالالمبور للأدوات'
    ]
    
    ports = [
        ('ميناء جدة', 'ميناء شنغهاي'),
        ('ميناء الدمام', 'ميناء دبي'),
        ('ميناء الملك عبدالله', 'ميناء إسطنبول'),
        ('ميناء ينبع', 'ميناء مومباي'),
        ('ميناء جيزان', 'ميناء بانكوك'),
        ('ميناء الجبيل', 'ميناء سنغافورة'),
        ('ميناء رابغ', 'ميناء هونغ كونغ'),
        ('ميناء الخفجي', 'ميناء كوالالمبور')
    ]
    
    statuses = ['قيد التحضير', 'تم الشحن', 'في الطريق', 'وصل', 'تم التسليم']
    priorities = ['عادي', 'مهم', 'عاجل', 'حرج']
    container_types = ['20 قدم', '40 قدم', '40 قدم عالي', 'مبرد 20 قدم', 'مبرد 40 قدم']
    shipping_lines = ['مسك', 'الخطوط السعودية', 'إيفرجرين', 'مايرسك', 'كوسكو']
    
    # إنشاء 50 شحنة تجريبية
    for i in range(1, 51):
        # تاريخ عشوائي في آخر 6 أشهر
        base_date = datetime.now() - timedelta(days=random.randint(0, 180))
        shipment_date = base_date.strftime('%Y-%m-%d')
        expected_arrival = (base_date + timedelta(days=random.randint(15, 45))).strftime('%Y-%m-%d')
        
        # بيانات الشحنة
        customer = random.choice(customers)
        supplier = random.choice(suppliers)
        origin_port, destination_port = random.choice(ports)
        status = random.choice(statuses)
        priority = random.choice(priorities)
        container_type = random.choice(container_types)
        shipping_line = random.choice(shipping_lines)
        
        # القيم المالية
        total_value = round(random.uniform(10000, 500000), 2)
        freight_cost = round(random.uniform(2000, 15000), 2)
        insurance_value = round(total_value * 0.01, 2)
        
        # معلومات أخرى
        weight = round(random.uniform(5000, 25000), 2)
        volume = round(random.uniform(20, 67), 2)
        items_count = random.randint(10, 500)
        
        # إدراج البيانات
        cursor.execute('''
            INSERT INTO advanced_shipments (
                shipment_number, customer_name, supplier_name, origin_port, destination_port,
                shipment_date, expected_arrival, status, priority, total_value, currency,
                container_type, container_number, seal_number, weight, volume, items_count,
                tracking_number, shipping_line, vessel_name, voyage_number, bill_of_lading,
                customs_status, insurance_value, freight_cost, other_charges, notes,
                created_by, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            f'SH{2024}{i:04d}',  # رقم الشحنة
            customer,
            supplier,
            origin_port,
            destination_port,
            shipment_date,
            expected_arrival,
            status,
            priority,
            total_value,
            'USD',
            container_type,
            f'MSKU{random.randint(1000000, 9999999)}',  # رقم الحاوية
            f'SL{random.randint(100000, 999999)}',  # رقم الختم
            weight,
            volume,
            items_count,
            f'TRK{random.randint(100000000, 999999999)}',  # رقم التتبع
            shipping_line,
            f'MV {shipping_line} {random.randint(100, 999)}',  # اسم السفينة
            f'V{random.randint(100, 999)}',  # رقم الرحلة
            f'BL{random.randint(10000000, 99999999)}',  # بوليصة الشحن
            random.choice(['لم يتم التخليص', 'قيد التخليص', 'تم التخليص']),
            insurance_value,
            freight_cost,
            round(random.uniform(500, 3000), 2),  # رسوم أخرى
            f'شحنة رقم {i} - بيانات تجريبية',
            'admin',
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print(f"تم إنشاء 50 شحنة تجريبية بنجاح!")

if __name__ == "__main__":
    create_sample_shipments()
