#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط الواجهة المتقدمة مع دعم RTL
Advanced UI Styles with RTL Support
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG

class RTLStyleManager:
    """مدير الأنماط مع دعم RTL"""
    
    def __init__(self):
        self.colors = COLORS
        self.fonts = FONTS
        self.rtl_config = RTL_CONFIG
        self.style_config = STYLE_CONFIG
        
    def get_font(self, font_type='arabic', size=None, weight=None):
        """الحصول على خط مع دعم RTL"""
        font_config = self.fonts.get(font_type, self.fonts['arabic'])
        
        # تحديد الخط الأساسي مع البدائل
        font_family = font_config['family']
        if 'fallback' in font_config:
            # جرب الخطوط البديلة إذا لم يكن الخط الأساسي متاحاً
            for fallback_font in [font_family] + font_config['fallback']:
                try:
                    test_font = tk.font.Font(family=fallback_font, size=10)
                    font_family = fallback_font
                    break
                except:
                    continue
        
        # تحديد الحجم
        if size is None:
            size = font_config.get('size', 12)
        
        # تحديد السماكة
        if weight is None:
            weight = font_config.get('weight', 'normal')
        
        return (font_family, size, weight)
    
    def create_styled_frame(self, parent, style='default', **kwargs):
        """إنشاء إطار مع تصميم متقدم"""
        styles = {
            'default': {
                'bg': self.colors['surface'],
                'relief': 'flat',
                'bd': 0
            },
            'card': {
                'bg': self.colors['surface'],
                'relief': 'raised',
                'bd': 1,
                'highlightbackground': self.colors['border'],
                'highlightthickness': 1
            },
            'sidebar': {
                'bg': self.colors['sidebar'],
                'relief': 'flat',
                'bd': 0
            },
            'header': {
                'bg': self.colors['header'],
                'relief': 'flat',
                'bd': 0
            },
            'primary': {
                'bg': self.colors['primary'],
                'relief': 'flat',
                'bd': 0
            },
            'light': {
                'bg': self.colors['light'],
                'relief': 'flat',
                'bd': 0
            }
        }
        
        frame_style = styles.get(style, styles['default'])
        frame_style.update(kwargs)
        
        frame = tk.Frame(parent, **frame_style)
        return frame
    
    def create_styled_label(self, parent, text, style='default', **kwargs):
        """إنشاء تسمية مع تصميم متقدم"""
        styles = {
            'default': {
                'font': self.get_font('arabic'),
                'bg': self.colors['surface'],
                'fg': self.colors['text_primary'],
                'anchor': 'e',  # محاذاة لليمين للعربية
                'justify': 'right'
            },
            'title': {
                'font': self.get_font('arabic_title'),
                'bg': self.colors['surface'],
                'fg': self.colors['primary'],
                'anchor': 'e',
                'justify': 'right'
            },
            'header': {
                'font': self.get_font('arabic_header'),
                'bg': self.colors['header'],
                'fg': self.colors['text_white'],
                'anchor': 'e',
                'justify': 'right'
            },
            'subtitle': {
                'font': self.get_font('arabic', size=11),
                'bg': self.colors['surface'],
                'fg': self.colors['text_secondary'],
                'anchor': 'e',
                'justify': 'right'
            },
            'muted': {
                'font': self.get_font('arabic_small'),
                'bg': self.colors['surface'],
                'fg': self.colors['text_muted'],
                'anchor': 'e',
                'justify': 'right'
            },
            'success': {
                'font': self.get_font('arabic'),
                'bg': self.colors['surface'],
                'fg': self.colors['success'],
                'anchor': 'e',
                'justify': 'right'
            },
            'danger': {
                'font': self.get_font('arabic'),
                'bg': self.colors['surface'],
                'fg': self.colors['danger'],
                'anchor': 'e',
                'justify': 'right'
            },
            'warning': {
                'font': self.get_font('arabic'),
                'bg': self.colors['surface'],
                'fg': self.colors['warning'],
                'anchor': 'e',
                'justify': 'right'
            }
        }
        
        label_style = styles.get(style, styles['default'])
        label_style.update(kwargs)
        
        label = tk.Label(parent, text=text, **label_style)
        return label
    
    def create_styled_button(self, parent, text, command=None, style='primary', **kwargs):
        """إنشاء زر مع تصميم متقدم"""
        styles = {
            'primary': {
                'font': self.get_font('arabic_button'),
                'bg': self.colors['primary'],
                'fg': self.colors['text_white'],
                'activebackground': self.colors['primary_light'],
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'cursor': 'hand2',
                'pady': 8,
                'padx': 20
            },
            'secondary': {
                'font': self.get_font('arabic_button'),
                'bg': self.colors['secondary'],
                'fg': self.colors['text_white'],
                'activebackground': self.colors['secondary_light'],
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'cursor': 'hand2',
                'pady': 8,
                'padx': 20
            },
            'success': {
                'font': self.get_font('arabic_button'),
                'bg': self.colors['success'],
                'fg': self.colors['text_white'],
                'activebackground': self.colors['success_light'],
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'cursor': 'hand2',
                'pady': 8,
                'padx': 20
            },
            'danger': {
                'font': self.get_font('arabic_button'),
                'bg': self.colors['danger'],
                'fg': self.colors['text_white'],
                'activebackground': self.colors['danger_light'],
                'activeforeground': self.colors['text_white'],
                'relief': 'flat',
                'bd': 0,
                'cursor': 'hand2',
                'pady': 8,
                'padx': 20
            },
            'outline': {
                'font': self.get_font('arabic_button'),
                'bg': self.colors['surface'],
                'fg': self.colors['primary'],
                'activebackground': self.colors['hover'],
                'activeforeground': self.colors['primary'],
                'relief': 'solid',
                'bd': 1,
                'highlightbackground': self.colors['primary'],
                'cursor': 'hand2',
                'pady': 8,
                'padx': 20
            },
            'ghost': {
                'font': self.get_font('arabic_button'),
                'bg': self.colors['surface'],
                'fg': self.colors['text_primary'],
                'activebackground': self.colors['hover'],
                'activeforeground': self.colors['text_primary'],
                'relief': 'flat',
                'bd': 0,
                'cursor': 'hand2',
                'pady': 8,
                'padx': 20
            }
        }
        
        button_style = styles.get(style, styles['primary'])
        button_style.update(kwargs)
        
        if command:
            button_style['command'] = command
        
        button = tk.Button(parent, text=text, **button_style)
        
        # إضافة تأثيرات التفاعل
        self.add_hover_effects(button, style)
        
        return button
    
    def add_hover_effects(self, widget, style='primary'):
        """إضافة تأثيرات التمرير للعناصر"""
        original_bg = widget.cget('bg')
        
        hover_colors = {
            'primary': self.colors['primary_light'],
            'secondary': self.colors['secondary_light'],
            'success': self.colors['success_light'],
            'danger': self.colors['danger_light'],
            'warning': self.colors['warning_light'],
            'info': self.colors['info_light'],
            'outline': self.colors['hover'],
            'ghost': self.colors['hover']
        }
        
        hover_color = hover_colors.get(style, self.colors['hover'])
        
        def on_enter(e):
            widget.config(bg=hover_color)
        
        def on_leave(e):
            widget.config(bg=original_bg)
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    def create_styled_entry(self, parent, **kwargs):
        """إنشاء حقل إدخال مع تصميم متقدم"""
        entry_style = {
            'font': self.get_font('arabic'),
            'bg': self.colors['surface'],
            'fg': self.colors['text_primary'],
            'insertbackground': self.colors['primary'],
            'selectbackground': self.colors['primary_light'],
            'selectforeground': self.colors['text_white'],
            'relief': 'solid',
            'bd': 1,
            'highlightbackground': self.colors['border'],
            'highlightcolor': self.colors['primary'],
            'highlightthickness': 1,
            'justify': 'right'  # محاذاة النص لليمين
        }
        
        entry_style.update(kwargs)
        entry = tk.Entry(parent, **entry_style)
        
        return entry
    
    def create_styled_text(self, parent, **kwargs):
        """إنشاء منطقة نص مع تصميم متقدم"""
        text_style = {
            'font': self.get_font('arabic'),
            'bg': self.colors['surface'],
            'fg': self.colors['text_primary'],
            'insertbackground': self.colors['primary'],
            'selectbackground': self.colors['primary_light'],
            'selectforeground': self.colors['text_white'],
            'relief': 'solid',
            'bd': 1,
            'highlightbackground': self.colors['border'],
            'highlightcolor': self.colors['primary'],
            'highlightthickness': 1,
            'wrap': 'word'
        }
        
        text_style.update(kwargs)
        text_widget = tk.Text(parent, **text_style)
        
        # تكوين اتجاه النص للعربية
        text_widget.tag_configure("rtl", justify='right')
        text_widget.tag_add("rtl", "1.0", "end")
        
        return text_widget
    
    def apply_rtl_layout(self, widget):
        """تطبيق تخطيط RTL على العنصر"""
        if hasattr(widget, 'pack_configure'):
            widget.pack_configure(side='right')
        elif hasattr(widget, 'grid_configure'):
            # سيتم تطبيق RTL في grid لاحقاً
            pass

# إنشاء مثيل عام للاستخدام
style_manager = RTLStyleManager()

def get_style_manager():
    """الحصول على مدير الأنماط"""
    return style_manager
