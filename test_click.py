#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النقر على زر المرفق
Test Attachment Button Click
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_button_click():
    """اختبار النقر على الزر"""
    
    print("🧪 اختبار النقر على زر المرفق...")
    
    try:
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار زر المرفق")
        root.geometry("400x300")
        
        # تهيئة مدير المصادقة
        from src.auth_manager import auth_manager
        auth_manager.current_user = {
            'username': 'test_user',
            'role': 'admin',
            'permissions': ['all']
        }
        
        # إنشاء كلاس وهمي لاختبار الدالة
        class TestForm:
            def __init__(self):
                self.document_attachments = {}
                print("📎 تم إنشاء كلاس الاختبار")
            
            def add_attachment(self, var_name, document_name):
                """دالة اختبار إضافة المرفق"""
                print(f"🔍 تم استدعاء add_attachment مع: var_name='{var_name}', document_name='{document_name}'")
                
                try:
                    from tkinter import filedialog
                    
                    # فتح نافذة اختيار الملف
                    file_path = filedialog.askopenfilename(
                        title=f"اختيار مرفق لـ {document_name}",
                        filetypes=[
                            ("جميع الملفات", "*.*"),
                            ("ملفات PDF", "*.pdf"),
                            ("ملفات الصور", "*.jpg *.png *.gif"),
                        ]
                    )
                    
                    if file_path:
                        print(f"✅ تم اختيار الملف: {file_path}")
                        messagebox.showinfo("نجح!", f"تم اختيار الملف:\n{file_path}")
                    else:
                        print("❌ لم يتم اختيار ملف")
                        messagebox.showinfo("إلغاء", "تم إلغاء اختيار الملف")
                        
                except Exception as e:
                    print(f"❌ خطأ: {e}")
                    messagebox.showerror("خطأ", f"خطأ في اختيار الملف:\n{str(e)}")
        
        # إنشاء الكلاس
        test_form = TestForm()
        
        # إنشاء زر الاختبار
        test_btn = tk.Button(
            root,
            text="📎 اختبار زر المرفق",
            font=('Arial', 14),
            bg='#2563eb',
            fg='white',
            padx=20,
            pady=10,
            command=lambda: test_form.add_attachment('test_document', 'مستند اختبار')
        )
        test_btn.pack(expand=True)
        
        # تعليمات
        instructions = tk.Label(
            root,
            text="انقر على الزر لاختبار وظيفة إضافة المرفق",
            font=('Arial', 12),
            wraplength=350
        )
        instructions.pack(pady=20)
        
        print("✅ تم إنشاء نافذة الاختبار")
        print("💡 انقر على الزر لاختبار الوظيفة")
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_button_click()
