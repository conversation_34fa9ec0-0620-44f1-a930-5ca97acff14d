#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسين أداء النظام
System Performance Optimization
"""

import os
import sys
import sqlite3
import time
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from database.database_manager import DatabaseManager

class SystemOptimizer:
    def __init__(self):
        self.db_manager = DatabaseManager()
        
    def create_database_indexes(self):
        """إنشاء فهارس قاعدة البيانات لتحسين الأداء"""
        print("إنشاء فهارس قاعدة البيانات...")
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # فهارس جدول المستخدمين
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
                "CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)",
                
                # فهارس جدول الموردين
                "CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(supplier_code)",
                "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(supplier_name)",
                "CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active)",
                
                # فهارس جدول الأصناف
                "CREATE INDEX IF NOT EXISTS idx_items_code ON items(item_code)",
                "CREATE INDEX IF NOT EXISTS idx_items_name ON items(item_name)",
                "CREATE INDEX IF NOT EXISTS idx_items_category ON items(category_id)",
                "CREATE INDEX IF NOT EXISTS idx_items_active ON items(is_active)",
                
                # فهارس جدول الشحنات
                "CREATE INDEX IF NOT EXISTS idx_shipments_number ON shipments(shipment_number)",
                "CREATE INDEX IF NOT EXISTS idx_shipments_supplier ON shipments(supplier_id)",
                "CREATE INDEX IF NOT EXISTS idx_shipments_date ON shipments(shipment_date)",
                "CREATE INDEX IF NOT EXISTS idx_shipments_status ON shipments(status)",
                "CREATE INDEX IF NOT EXISTS idx_shipments_created ON shipments(created_at)",
                
                # فهارس جدول تفاصيل الشحنات
                "CREATE INDEX IF NOT EXISTS idx_shipment_items_shipment ON shipment_items(shipment_id)",
                "CREATE INDEX IF NOT EXISTS idx_shipment_items_item ON shipment_items(item_id)",
                
                # فهارس جدول المخزون
                "CREATE INDEX IF NOT EXISTS idx_inventory_item ON inventory(item_id)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_warehouse ON inventory(warehouse_id)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_item_warehouse ON inventory(item_id, warehouse_id)",
                
                # فهارس جدول حركات المخزون
                "CREATE INDEX IF NOT EXISTS idx_movements_item ON inventory_movements(item_id)",
                "CREATE INDEX IF NOT EXISTS idx_movements_warehouse ON inventory_movements(warehouse_id)",
                "CREATE INDEX IF NOT EXISTS idx_movements_date ON inventory_movements(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_movements_type ON inventory_movements(movement_type)",
                
                # فهارس جدول تاريخ حالات الشحنة
                "CREATE INDEX IF NOT EXISTS idx_status_history_shipment ON shipment_status_history(shipment_id)",
                "CREATE INDEX IF NOT EXISTS idx_status_history_date ON shipment_status_history(status_date)",
            ]
            
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    print(f"✓ تم إنشاء الفهرس: {index_sql.split('idx_')[1].split(' ')[0] if 'idx_' in index_sql else 'فهرس'}")
                except Exception as e:
                    print(f"✗ فشل في إنشاء فهرس: {e}")
            
            conn.commit()
            conn.close()
            print("تم إنشاء جميع الفهارس بنجاح")
            
        except Exception as e:
            print(f"خطأ في إنشاء الفهارس: {e}")
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        print("تحسين قاعدة البيانات...")
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # تحليل الجداول
            print("تحليل الجداول...")
            cursor.execute("ANALYZE")
            
            # إعادة تنظيم قاعدة البيانات
            print("إعادة تنظيم قاعدة البيانات...")
            cursor.execute("VACUUM")
            
            conn.close()
            print("تم تحسين قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"خطأ في تحسين قاعدة البيانات: {e}")
    
    def cleanup_old_data(self, days_to_keep=90):
        """تنظيف البيانات القديمة"""
        print(f"تنظيف البيانات الأقدم من {days_to_keep} يوم...")
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # تنظيف سجلات تاريخ حالات الشحنة القديمة
            cursor.execute('''
                DELETE FROM shipment_status_history 
                WHERE status_date < date('now', '-{} days')
            '''.format(days_to_keep))
            
            deleted_status = cursor.rowcount
            print(f"تم حذف {deleted_status} سجل من تاريخ حالات الشحنة")
            
            # تنظيف حركات المخزون القديمة (الاحتفاظ بآخر سنة فقط)
            cursor.execute('''
                DELETE FROM inventory_movements 
                WHERE created_at < date('now', '-365 days')
                AND movement_type NOT IN ('opening_balance')
            ''')
            
            deleted_movements = cursor.rowcount
            print(f"تم حذف {deleted_movements} حركة مخزون قديمة")
            
            conn.commit()
            conn.close()
            print("تم تنظيف البيانات القديمة بنجاح")
            
        except Exception as e:
            print(f"خطأ في تنظيف البيانات: {e}")
    
    def check_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        print("فحص سلامة قاعدة البيانات...")
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # فحص سلامة قاعدة البيانات
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            if result and result[0] == 'ok':
                print("✓ قاعدة البيانات سليمة")
            else:
                print(f"✗ مشكلة في قاعدة البيانات: {result}")
            
            # فحص الجداول الفارغة
            tables = [
                'users', 'suppliers', 'items', 'shipments', 
                'item_categories', 'warehouses'
            ]
            
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"جدول {table}: {count} سجل")
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في فحص سلامة قاعدة البيانات: {e}")
    
    def generate_performance_report(self):
        """إنشاء تقرير الأداء"""
        print("إنشاء تقرير الأداء...")
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            report = []
            report.append("تقرير أداء النظام")
            report.append("=" * 50)
            report.append(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")
            
            # إحصائيات الجداول
            report.append("إحصائيات الجداول:")
            report.append("-" * 20)
            
            tables = [
                ('users', 'المستخدمين'),
                ('suppliers', 'الموردين'),
                ('items', 'الأصناف'),
                ('shipments', 'الشحنات'),
                ('shipment_items', 'تفاصيل الشحنات'),
                ('inventory_movements', 'حركات المخزون')
            ]
            
            for table_name, arabic_name in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                report.append(f"{arabic_name}: {count:,} سجل")
            
            report.append("")
            
            # حجم قاعدة البيانات
            db_path = self.db_manager.db_path
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path)
                db_size_mb = db_size / (1024 * 1024)
                report.append(f"حجم قاعدة البيانات: {db_size_mb:.2f} ميجابايت")
            
            # إحصائيات الشحنات
            report.append("")
            report.append("إحصائيات الشحنات:")
            report.append("-" * 20)
            
            cursor.execute("SELECT status, COUNT(*) FROM shipments GROUP BY status")
            status_counts = cursor.fetchall()
            
            for status, count in status_counts:
                report.append(f"حالة {status}: {count} شحنة")
            
            # الشحنات الحديثة
            cursor.execute('''
                SELECT COUNT(*) FROM shipments 
                WHERE created_at >= date('now', '-30 days')
            ''')
            recent_shipments = cursor.fetchone()[0]
            report.append(f"الشحنات في آخر 30 يوم: {recent_shipments}")
            
            conn.close()
            
            # حفظ التقرير
            report_content = "\n".join(report)
            
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
            
            report_file = os.path.join(reports_dir, f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"تم إنشاء تقرير الأداء: {report_file}")
            print("\nملخص التقرير:")
            print(report_content)
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير الأداء: {e}")
    
    def run_full_optimization(self):
        """تشغيل التحسين الكامل"""
        print("بدء تحسين النظام الكامل...")
        print("=" * 50)
        
        start_time = time.time()
        
        # 1. فحص سلامة قاعدة البيانات
        self.check_database_integrity()
        print()
        
        # 2. إنشاء الفهارس
        self.create_database_indexes()
        print()
        
        # 3. تحسين قاعدة البيانات
        self.optimize_database()
        print()
        
        # 4. تنظيف البيانات القديمة
        self.cleanup_old_data()
        print()
        
        # 5. إنشاء تقرير الأداء
        self.generate_performance_report()
        print()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("=" * 50)
        print(f"تم الانتهاء من تحسين النظام في {duration:.2f} ثانية")
        print("✅ تم تحسين النظام بنجاح!")

def main():
    """الدالة الرئيسية"""
    print("أداة تحسين نظام متابعة الشحنات")
    print("=" * 50)
    
    optimizer = SystemOptimizer()
    
    while True:
        print("\nاختر العملية المطلوبة:")
        print("1. تحسين كامل للنظام")
        print("2. إنشاء فهارس قاعدة البيانات")
        print("3. تحسين قاعدة البيانات")
        print("4. تنظيف البيانات القديمة")
        print("5. فحص سلامة قاعدة البيانات")
        print("6. إنشاء تقرير الأداء")
        print("0. خروج")
        
        choice = input("\nأدخل اختيارك: ").strip()
        
        if choice == "1":
            optimizer.run_full_optimization()
        elif choice == "2":
            optimizer.create_database_indexes()
        elif choice == "3":
            optimizer.optimize_database()
        elif choice == "4":
            days = input("عدد الأيام للاحتفاظ بالبيانات (افتراضي: 90): ").strip()
            days = int(days) if days.isdigit() else 90
            optimizer.cleanup_old_data(days)
        elif choice == "5":
            optimizer.check_database_integrity()
        elif choice == "6":
            optimizer.generate_performance_report()
        elif choice == "0":
            print("تم الخروج من أداة التحسين")
            break
        else:
            print("اختيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    main()
