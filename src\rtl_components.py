#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات واجهة المستخدم مع دعم RTL متقدم
Advanced RTL UI Components
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG
from src.ui_styles import get_style_manager

class RTLFrame(tk.Frame):
    """إطار مع دعم RTL متقدم"""
    
    def __init__(self, parent, style='default', **kwargs):
        self.style_manager = get_style_manager()
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'bg': COLORS['surface'],
            'relief': 'flat',
            'bd': 0
        }
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)
        
        # تطبيق تخطيط RTL
        self.configure_rtl()
    
    def configure_rtl(self):
        """تكوين RTL للإطار"""
        # تكوين اتجاه التخطيط
        if RTL_CONFIG['layout_direction'] == 'rtl':
            self.pack_configure(side='right')

class RTLLabel(tk.Label):
    """تسمية مع دعم RTL متقدم"""
    
    def __init__(self, parent, text="", style='default', **kwargs):
        self.style_manager = get_style_manager()
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'font': self.style_manager.get_font('arabic'),
            'bg': COLORS['surface'],
            'fg': COLORS['text_primary'],
            'anchor': 'e',
            'justify': 'right'
        }
        
        # أنماط مختلفة
        styles = {
            'title': {
                'font': self.style_manager.get_font('arabic_title'),
                'fg': COLORS['primary']
            },
            'header': {
                'font': self.style_manager.get_font('arabic_header'),
                'fg': COLORS['text_primary']
            },
            'subtitle': {
                'font': self.style_manager.get_font('arabic', size=11),
                'fg': COLORS['text_secondary']
            },
            'muted': {
                'font': self.style_manager.get_font('arabic_small'),
                'fg': COLORS['text_muted']
            }
        }
        
        if style in styles:
            default_config.update(styles[style])
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        super().__init__(parent, text=text, **default_config)

class RTLButton(tk.Button):
    """زر مع دعم RTL متقدم"""
    
    def __init__(self, parent, text="", command=None, style='primary', **kwargs):
        self.style_manager = get_style_manager()
        
        # ألوان الأنماط المختلفة
        style_colors = {
            'primary': {
                'bg': COLORS['primary'],
                'fg': COLORS['text_white'],
                'activebackground': COLORS['primary_light'],
                'activeforeground': COLORS['text_white']
            },
            'secondary': {
                'bg': COLORS['secondary'],
                'fg': COLORS['text_white'],
                'activebackground': COLORS['secondary_light'],
                'activeforeground': COLORS['text_white']
            },
            'success': {
                'bg': COLORS['success'],
                'fg': COLORS['text_white'],
                'activebackground': COLORS['success_light'],
                'activeforeground': COLORS['text_white']
            },
            'danger': {
                'bg': COLORS['danger'],
                'fg': COLORS['text_white'],
                'activebackground': COLORS['danger_light'],
                'activeforeground': COLORS['text_white']
            },
            'warning': {
                'bg': COLORS['warning'],
                'fg': COLORS['text_white'],
                'activebackground': COLORS['warning_light'],
                'activeforeground': COLORS['text_white']
            },
            'info': {
                'bg': COLORS['info'],
                'fg': COLORS['text_white'],
                'activebackground': COLORS['info_light'],
                'activeforeground': COLORS['text_white']
            },
            'outline': {
                'bg': COLORS['surface'],
                'fg': COLORS['primary'],
                'activebackground': COLORS['hover'],
                'activeforeground': COLORS['primary'],
                'relief': 'solid',
                'bd': 1
            }
        }
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'font': self.style_manager.get_font('arabic_button'),
            'relief': 'flat',
            'bd': 0,
            'cursor': 'hand2',
            'pady': STYLE_CONFIG['spacing']['sm'],
            'padx': STYLE_CONFIG['spacing']['md'],
            'anchor': 'center'
        }
        
        # تطبيق ألوان النمط
        if style in style_colors:
            default_config.update(style_colors[style])
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        if command:
            default_config['command'] = command
        
        super().__init__(parent, text=text, **default_config)
        
        # إضافة تأثيرات التفاعل
        self.add_hover_effects(style)
    
    def add_hover_effects(self, style):
        """إضافة تأثيرات التمرير"""
        original_bg = self.cget('bg')
        
        hover_colors = {
            'primary': COLORS['primary_light'],
            'secondary': COLORS['secondary_light'],
            'success': COLORS['success_light'],
            'danger': COLORS['danger_light'],
            'warning': COLORS['warning_light'],
            'info': COLORS['info_light'],
            'outline': COLORS['hover']
        }
        
        hover_color = hover_colors.get(style, COLORS['hover'])
        
        def on_enter(e):
            self.config(bg=hover_color)
        
        def on_leave(e):
            self.config(bg=original_bg)
        
        self.bind("<Enter>", on_enter)
        self.bind("<Leave>", on_leave)

class RTLEntry(tk.Entry):
    """حقل إدخال مع دعم RTL"""
    
    def __init__(self, parent, **kwargs):
        self.style_manager = get_style_manager()
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'font': self.style_manager.get_font('arabic'),
            'bg': COLORS['surface'],
            'fg': COLORS['text_primary'],
            'insertbackground': COLORS['primary'],
            'selectbackground': COLORS['primary_light'],
            'selectforeground': COLORS['text_white'],
            'relief': 'solid',
            'bd': 1,
            'highlightbackground': COLORS['border'],
            'highlightcolor': COLORS['primary'],
            'highlightthickness': 1,
            'justify': 'right'
        }
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)

class RTLText(tk.Text):
    """منطقة نص مع دعم RTL"""
    
    def __init__(self, parent, **kwargs):
        self.style_manager = get_style_manager()
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'font': self.style_manager.get_font('arabic'),
            'bg': COLORS['surface'],
            'fg': COLORS['text_primary'],
            'insertbackground': COLORS['primary'],
            'selectbackground': COLORS['primary_light'],
            'selectforeground': COLORS['text_white'],
            'relief': 'solid',
            'bd': 1,
            'highlightbackground': COLORS['border'],
            'highlightcolor': COLORS['primary'],
            'highlightthickness': 1,
            'wrap': 'word'
        }
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)
        
        # تكوين اتجاه النص للعربية
        self.tag_configure("rtl", justify='right')
        self.tag_add("rtl", "1.0", "end")

class RTLCombobox(ttk.Combobox):
    """قائمة منسدلة مع دعم RTL"""
    
    def __init__(self, parent, **kwargs):
        self.style_manager = get_style_manager()
        
        # إنشاء نمط مخصص للقائمة المنسدلة
        style = ttk.Style()
        style.configure(
            'RTL.TCombobox',
            fieldbackground=COLORS['surface'],
            background=COLORS['surface'],
            foreground=COLORS['text_primary'],
            selectbackground=COLORS['primary'],
            selectforeground=COLORS['text_white'],
            font=self.style_manager.get_font('arabic')
        )
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'style': 'RTL.TCombobox',
            'justify': 'right'
        }
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)

class RTLTreeview(ttk.Treeview):
    """عرض شجري مع دعم RTL"""
    
    def __init__(self, parent, **kwargs):
        self.style_manager = get_style_manager()
        
        # إنشاء نمط مخصص للعرض الشجري
        style = ttk.Style()
        style.configure(
            'RTL.Treeview',
            background=COLORS['surface'],
            foreground=COLORS['text_primary'],
            fieldbackground=COLORS['surface'],
            font=self.style_manager.get_font('table')
        )
        
        style.configure(
            'RTL.Treeview.Heading',
            background=COLORS['primary'],
            foreground=COLORS['text_white'],
            font=self.style_manager.get_font('arabic_header', size=11, weight='bold')
        )
        
        # تطبيق الأنماط الافتراضية
        default_config = {
            'style': 'RTL.Treeview'
        }
        
        # دمج الإعدادات المخصصة
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)

class RTLCard(RTLFrame):
    """بطاقة مع تصميم متقدم"""
    
    def __init__(self, parent, title="", **kwargs):
        # تطبيق تصميم البطاقة
        card_config = {
            'bg': COLORS['surface'],
            'relief': 'raised',
            'bd': 1,
            'highlightbackground': COLORS['border'],
            'highlightthickness': 1
        }
        
        card_config.update(kwargs)
        super().__init__(parent, **card_config)
        
        if title:
            self.create_title(title)
    
    def create_title(self, title):
        """إنشاء عنوان للبطاقة"""
        title_frame = RTLFrame(self, bg=COLORS['primary'])
        title_frame.pack(fill='x')
        
        title_label = RTLLabel(
            title_frame,
            text=title,
            style='header',
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        title_label.pack(pady=STYLE_CONFIG['spacing']['sm'], padx=STYLE_CONFIG['spacing']['md'])

# دوال مساعدة لإنشاء المكونات
def create_rtl_frame(parent, style='default', **kwargs):
    """إنشاء إطار RTL"""
    return RTLFrame(parent, style=style, **kwargs)

def create_rtl_label(parent, text="", style='default', **kwargs):
    """إنشاء تسمية RTL"""
    return RTLLabel(parent, text=text, style=style, **kwargs)

def create_rtl_button(parent, text="", command=None, style='primary', **kwargs):
    """إنشاء زر RTL"""
    return RTLButton(parent, text=text, command=command, style=style, **kwargs)

def create_rtl_entry(parent, **kwargs):
    """إنشاء حقل إدخال RTL"""
    return RTLEntry(parent, **kwargs)

def create_rtl_text(parent, **kwargs):
    """إنشاء منطقة نص RTL"""
    return RTLText(parent, **kwargs)

def create_rtl_combobox(parent, **kwargs):
    """إنشاء قائمة منسدلة RTL"""
    return RTLCombobox(parent, **kwargs)

def create_rtl_treeview(parent, **kwargs):
    """إنشاء عرض شجري RTL"""
    return RTLTreeview(parent, **kwargs)

def create_rtl_card(parent, title="", **kwargs):
    """إنشاء بطاقة RTL"""
    return RTLCard(parent, title=title, **kwargs)
