#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين الأساسي للنظام
System Configuration File
"""

import os
from datetime import datetime

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'path': 'database/shipment_system.db',
    'backup_path': 'backups/',
    'auto_backup': True,
    'backup_interval_hours': 24
}

# إعدادات التطبيق
APP_CONFIG = {
    'name': 'نظام متابعة الشحنات',
    'name_en': 'Shipment Tracking System',
    'version': '1.0.0',
    'author': 'Ship Management System',
    'description': 'نظام متكامل لمتابعة شحنات الموردين',
    'window_title': 'نظام متابعة الشحنات - Ship Management System',
    'icon_path': 'assets/icons/app_icon.ico'
}

# إعدادات الواجهة المحسنة
UI_CONFIG = {
    'theme': 'modern_rtl',
    'language': 'ar',  # ar, en
    'rtl_support': True,
    'font_family': 'Segoe UI',
    'font_size': 12,
    'window_width': 1400,
    'window_height': 900,
    'min_width': 1200,
    'min_height': 700,
    'fullscreen': True,
    'animations': True,
    'shadows': True,
    'rounded_corners': True,
    'gradient_backgrounds': True
}

# إعدادات RTL والعربية
RTL_CONFIG = {
    'text_direction': 'rtl',
    'layout_direction': 'rtl',
    'menu_alignment': 'right',
    'button_alignment': 'right',
    'table_alignment': 'right',
    'form_alignment': 'right',
    'padding_right': 15,
    'padding_left': 10,
    'margin_right': 10,
    'margin_left': 5,
    'arabic_numerals': True,
    'date_format': 'dd/mm/yyyy',
    'time_format': '24h'
}

# إعدادات التصميم المتقدم (CSS-like)
STYLE_CONFIG = {
    # الظلال
    'shadows': {
        'small': '0 1px 3px rgba(0, 0, 0, 0.12)',
        'medium': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'large': '0 10px 15px rgba(0, 0, 0, 0.1)',
        'xl': '0 20px 25px rgba(0, 0, 0, 0.15)'
    },

    # الحدود المدورة
    'border_radius': {
        'small': 4,
        'medium': 8,
        'large': 12,
        'xl': 16,
        'full': 50
    },

    # المسافات
    'spacing': {
        'xs': 4,
        'sm': 8,
        'md': 12,
        'lg': 16,
        'xl': 20,
        'xxl': 24
    },

    # أحجام الخطوط
    'font_sizes': {
        'xs': 10,
        'sm': 11,
        'base': 12,
        'lg': 14,
        'xl': 16,
        'xxl': 18,
        'xxxl': 20
    },

    # سماكة الخطوط
    'font_weights': {
        'light': 300,
        'normal': 400,
        'medium': 500,
        'semibold': 600,
        'bold': 700,
        'extrabold': 800
    },

    # الشفافية
    'opacity': {
        'disabled': 0.5,
        'hover': 0.8,
        'active': 0.9,
        'overlay': 0.7
    },

    # التدرجات اللونية
    'gradients': {
        'primary': 'linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%)',
        'secondary': 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',
        'success': 'linear-gradient(135deg, #059669 0%, #10B981 100%)',
        'warning': 'linear-gradient(135deg, #D97706 0%, #F59E0B 100%)',
        'danger': 'linear-gradient(135deg, #DC2626 0%, #EF4444 100%)',
        'info': 'linear-gradient(135deg, #0891B2 0%, #06B6D4 100%)'
    },

    # الحدود
    'borders': {
        'thin': 1,
        'medium': 2,
        'thick': 3,
        'style': 'solid'
    },

    # الانتقالات والحركات
    'transitions': {
        'fast': '0.15s',
        'normal': '0.3s',
        'slow': '0.5s',
        'ease': 'ease-in-out'
    }
}

# ألوان النظام المحسنة مع دعم RTL
COLORS = {
    # الألوان الأساسية
    'primary': '#1E40AF',           # أزرق داكن أنيق
    'primary_light': '#3B82F6',     # أزرق فاتح
    'primary_dark': '#1E3A8A',      # أزرق أغمق
    'secondary': '#7C3AED',         # بنفسجي
    'secondary_light': '#8B5CF6',   # بنفسجي فاتح
    'secondary_dark': '#6D28D9',    # بنفسجي غامق

    # ألوان الحالة
    'success': '#059669',           # أخضر
    'success_light': '#10B981',     # أخضر فاتح
    'success_dark': '#047857',      # أخضر غامق
    'danger': '#DC2626',            # أحمر
    'danger_light': '#EF4444',      # أحمر فاتح
    'danger_dark': '#B91C1C',       # أحمر غامق
    'warning': '#D97706',           # برتقالي
    'warning_light': '#F59E0B',     # برتقالي فاتح
    'warning_dark': '#B45309',      # برتقالي غامق
    'info': '#0891B2',              # سماوي
    'info_light': '#06B6D4',        # سماوي فاتح
    'info_dark': '#0E7490',         # سماوي غامق

    # ألوان الخلفية والنصوص
    'white': '#FFFFFF',
    'light': '#F9FAFB',             # رمادي فاتح جداً
    'light_gray': '#F3F4F6',        # رمادي فاتح
    'gray': '#E5E7EB',              # رمادي متوسط
    'dark_gray': '#6B7280',         # رمادي داكن
    'dark': '#1F2937',              # رمادي داكن جداً
    'black': '#111827',             # أسود

    # ألوان الواجهة
    'background': '#F8FAFC',        # خلفية رئيسية
    'surface': '#FFFFFF',           # سطح البطاقات
    'header': '#1E3A8A',            # رأس الصفحة
    'sidebar': '#1F2937',           # الشريط الجانبي
    'footer': '#374151',            # تذييل الصفحة

    # ألوان النصوص
    'text_primary': '#111827',      # نص أساسي
    'text_secondary': '#374151',    # نص ثانوي
    'text_muted': '#6B7280',        # نص خافت
    'text_light': '#9CA3AF',        # نص فاتح
    'text_white': '#FFFFFF',        # نص أبيض

    # ألوان التفاعل
    'hover': '#EBF8FF',             # تمرير الماوس
    'active': '#DBEAFE',            # العنصر النشط
    'focus': '#93C5FD',             # التركيز
    'disabled': '#D1D5DB',          # معطل

    # ألوان الحدود والظلال
    'border': '#E5E7EB',            # حدود
    'border_light': '#F3F4F6',      # حدود فاتحة
    'border_dark': '#D1D5DB',       # حدود داكنة

    # ألوان خاصة بالعربية
    'rtl_accent': '#B45309',        # لون مميز للعربية
    'rtl_highlight': '#FEF3C7',     # تمييز النصوص العربية
    'rtl_border': '#D4AF37',        # حدود ذهبية للعربية
}

# الخطوط المحسنة مع دعم RTL
FONTS = {
    # الخطوط العربية الأساسية
    'arabic': {
        'family': 'Segoe UI',       # خط حديث يدعم العربية
        'fallback': ['Tahoma', 'Arial Unicode MS', 'Microsoft Sans Serif'],
        'size': 12,
        'bold_size': 13,
        'direction': 'rtl'
    },

    # خطوط العناوين العربية
    'arabic_title': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Arial Unicode MS'],
        'size': 18,
        'bold_size': 20,
        'weight': 'bold',
        'direction': 'rtl'
    },

    # خطوط الرؤوس العربية
    'arabic_header': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Arial Unicode MS'],
        'size': 15,
        'bold_size': 16,
        'weight': 'bold',
        'direction': 'rtl'
    },

    # خطوط الأزرار العربية
    'arabic_button': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Arial Unicode MS'],
        'size': 11,
        'bold_size': 12,
        'weight': 'normal',
        'direction': 'rtl'
    },

    # خطوط النصوص الصغيرة
    'arabic_small': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Arial Unicode MS'],
        'size': 10,
        'bold_size': 11,
        'direction': 'rtl'
    },

    # خطوط الأرقام والتواريخ
    'arabic_numbers': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Courier New'],
        'size': 11,
        'bold_size': 12,
        'direction': 'ltr'  # الأرقام من اليسار لليمين
    },

    # الخطوط الإنجليزية (للنصوص المختلطة)
    'english': {
        'family': 'Segoe UI',
        'fallback': ['Arial', 'Helvetica'],
        'size': 11,
        'bold_size': 12,
        'direction': 'ltr'
    },

    # خطوط خاصة للجداول
    'table': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Arial Unicode MS'],
        'size': 10,
        'bold_size': 11,
        'direction': 'rtl'
    },

    # خطوط القوائم
    'menu': {
        'family': 'Segoe UI',
        'fallback': ['Tahoma', 'Arial Unicode MS'],
        'size': 11,
        'bold_size': 12,
        'direction': 'rtl'
    },

    # للتوافق مع الكود القديم
    'title': {
        'family': 'Segoe UI',
        'size': 18,
        'weight': 'bold'
    },
    'header': {
        'family': 'Segoe UI',
        'size': 15,
        'weight': 'bold'
    },
    'button': {
        'family': 'Segoe UI',
        'size': 11
    }
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'output_path': 'reports/',
    'temp_path': 'reports/temp/',
    'formats': ['pdf', 'excel'],
    'default_format': 'pdf',
    'page_size': 'A4',
    'orientation': 'portrait',
    'margins': {
        'top': 2.5,
        'bottom': 2.5,
        'left': 2.0,
        'right': 2.0
    }
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'session_timeout_minutes': 60,
    'max_login_attempts': 3,
    'password_min_length': 6,
    'require_password_change': False,
    'password_expiry_days': 90
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',
    'file_path': 'logs/system.log',
    'max_file_size_mb': 10,
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_path': 'backups/',
    'keep_backups_days': 30,
    'backup_on_startup': True,
    'backup_on_exit': True
}

# حالات الشحنة
SHIPMENT_STATUS = {
    'pending': 'في الانتظار',
    'confirmed': 'مؤكدة',
    'shipped': 'تم الشحن',
    'in_transit': 'في الطريق',
    'arrived': 'وصلت',
    'customs': 'في الجمارك',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغية',
    'delayed': 'متأخرة'
}

# أنواع المستخدمين والصلاحيات
USER_ROLES = {
    'admin': {
        'name': 'مدير النظام',
        'permissions': ['all']
    },
    'manager': {
        'name': 'مدير',
        'permissions': [
            'view_all_shipments', 'edit_shipments', 'view_reports',
            'manage_suppliers', 'manage_items', 'view_inventory'
        ]
    },
    'accountant': {
        'name': 'محاسب',
        'permissions': [
            'view_shipments', 'view_reports', 'view_suppliers',
            'view_items', 'view_inventory'
        ]
    },
    'sales': {
        'name': 'موظف مبيعات',
        'permissions': [
            'view_shipments', 'view_suppliers', 'view_items'
        ]
    },
    'employee': {
        'name': 'موظف',
        'permissions': [
            'view_shipments', 'view_items'
        ]
    }
}

# وحدات القياس
UNITS_OF_MEASURE = [
    'قطعة', 'كيلو', 'جرام', 'طن', 'متر', 'سنتيمتر',
    'لتر', 'مليلتر', 'صندوق', 'كرتون', 'باليت'
]

# العملات
CURRENCIES = {
    'SAR': 'ريال سعودي',
    'USD': 'دولار أمريكي',
    'EUR': 'يورو',
    'AED': 'درهم إماراتي',
    'KWD': 'دينار كويتي',
    'GBP': 'جنيه إسترليني',
    'JPY': 'ين ياباني',
    'CNY': 'يوان صيني'
}

# البلدان
COUNTRIES = [
    'المملكة العربية السعودية', 'الإمارات العربية المتحدة', 'الكويت',
    'قطر', 'البحرين', 'عمان', 'الأردن', 'لبنان', 'مصر',
    'الولايات المتحدة', 'المملكة المتحدة', 'ألمانيا', 'فرنسا',
    'إيطاليا', 'إسبانيا', 'الصين', 'اليابان', 'كوريا الجنوبية',
    'الهند', 'تركيا'
]

# المدن السعودية
SAUDI_CITIES = [
    'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام',
    'الخبر', 'الظهران', 'الطائف', 'بريدة', 'تبوك', 'خميس مشيط',
    'حائل', 'الجبيل', 'ينبع', 'الأحساء', 'القطيف', 'نجران',
    'جازان', 'عرعر', 'سكاكا'
]

# الموانئ
PORTS = {
    'الدمام': 'ميناء الملك عبدالعزيز - الدمام',
    'جدة': 'ميناء جدة الإسلامي',
    'ينبع': 'ميناء الملك فهد الصناعي - ينبع',
    'جازان': 'ميناء جازان',
    'ضباء': 'ميناء ضباء',
    'الجبيل': 'ميناء الجبيل التجاري',
    'رأس الخير': 'ميناء رأس الخير',
    'شنغهاي': 'ميناء شنغهاي',
    'هونغ كونغ': 'ميناء هونغ كونغ',
    'سنغافورة': 'ميناء سنغافورة',
    'دبي': 'ميناء دبي',
    'هامبورغ': 'ميناء هامبورغ',
    'روتردام': 'ميناء روتردام',
    'لوس أنجلوس': 'ميناء لوس أنجلوس',
    'نيويورك': 'ميناء نيويورك'
}

# شركات الشحن
SHIPPING_COMPANIES = {
    'البحري السعودي': 'الخطوط السعودية للنقل البحري',
    'البحري': 'شركة البحري',
    'ميرسك': 'Maersk Line',
    'MSC': 'Mediterranean Shipping Company',
    'CMA CGM': 'CMA CGM Group',
    'COSCO': 'COSCO Shipping',
    'Evergreen': 'Evergreen Marine',
    'Hapag-Lloyd': 'Hapag-Lloyd AG',
    'ONE': 'Ocean Network Express',
    'Yang Ming': 'Yang Ming Marine Transport',
    'أرامكس': 'Aramex',
    'DHL': 'DHL Express',
    'فيدكس': 'FedEx',
    'UPS': 'United Parcel Service'
}

# أنواع الحاويات
CONTAINER_TYPES = {
    '20 قدم عادية': 'حاوية 20 قدم عادية',
    '40 قدم عادية': 'حاوية 40 قدم عادية',
    '40 قدم عالية': 'حاوية 40 قدم عالية الارتفاع',
    'مبردة 20 قدم': 'حاوية مبردة 20 قدم',
    'مبردة 40 قدم': 'حاوية مبردة 40 قدم',
    'مفتوحة 20 قدم': 'حاوية مفتوحة 20 قدم',
    'مفتوحة 40 قدم': 'حاوية مفتوحة 40 قدم',
    'صهريج 20 قدم': 'حاوية صهريج 20 قدم',
    'صهريج 40 قدم': 'حاوية صهريج 40 قدم',
    'مسطحة 20 قدم': 'حاوية مسطحة 20 قدم',
    'مسطحة 40 قدم': 'حاوية مسطحة 40 قدم'
}

# طرق الدفع
PAYMENT_METHODS = {
    'نقداً': 'دفع نقدي',
    'تحويل بنكي': 'تحويل بنكي',
    'شيك': 'شيك بنكي',
    'بطاقة ائتمان': 'بطاقة ائتمان',
    'خطاب اعتماد': 'خطاب اعتماد',
    'تحصيل مستندي': 'تحصيل مستندي',
    'دفع عند التسليم': 'دفع عند التسليم',
    'دفع مؤجل': 'دفع مؤجل',
    'تقسيط': 'دفع بالتقسيط'
}

def get_app_path():
    """الحصول على مسار التطبيق"""
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def get_database_path():
    """الحصول على مسار قاعدة البيانات"""
    return os.path.join(get_app_path(), DATABASE_CONFIG['path'])

def get_reports_path():
    """الحصول على مسار التقارير"""
    reports_path = os.path.join(get_app_path(), REPORTS_CONFIG['output_path'])
    if not os.path.exists(reports_path):
        os.makedirs(reports_path)
    return reports_path

def get_backups_path():
    """الحصول على مسار النسخ الاحتياطية"""
    backups_path = os.path.join(get_app_path(), BACKUP_CONFIG['backup_path'])
    if not os.path.exists(backups_path):
        os.makedirs(backups_path)
    return backups_path

def get_logs_path():
    """الحصول على مسار السجلات"""
    logs_path = os.path.dirname(os.path.join(get_app_path(), LOGGING_CONFIG['file_path']))
    if not os.path.exists(logs_path):
        os.makedirs(logs_path)
    return logs_path
