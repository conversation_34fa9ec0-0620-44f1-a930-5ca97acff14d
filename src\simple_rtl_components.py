#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات RTL مبسطة وآمنة
Simple and Safe RTL Components
"""

import tkinter as tk
from tkinter import ttk
import os
import sys

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS

class SimpleRTLFrame(tk.Frame):
    """إطار RTL مبسط"""
    
    def __init__(self, parent, **kwargs):
        # تطبيق الألوان الافتراضية
        default_config = {
            'bg': COLORS.get('surface', '#FFFFFF'),
            'relief': 'flat',
            'bd': 0
        }
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)

class SimpleRTLLabel(tk.Label):
    """تسمية RTL مبسطة"""
    
    def __init__(self, parent, text="", **kwargs):
        # تطبيق الإعدادات الافتراضية
        default_config = {
            'text': text,
            'bg': COLORS.get('surface', '#FFFFFF'),
            'fg': COLORS.get('text_primary', '#000000'),
            'font': ('Segoe UI', 12, 'normal'),
            'anchor': 'e',
            'justify': 'right'
        }
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)

class SimpleRTLButton(tk.Button):
    """زر RTL مبسط"""
    
    def __init__(self, parent, text="", command=None, button_type='primary', **kwargs):
        # تحديد ألوان الزر حسب النوع
        button_colors = {
            'primary': {
                'bg': COLORS.get('primary', '#1E40AF'),
                'fg': COLORS.get('text_white', '#FFFFFF'),
                'activebackground': COLORS.get('primary_dark', '#1E3A8A'),
                'activeforeground': COLORS.get('text_white', '#FFFFFF')
            },
            'secondary': {
                'bg': COLORS.get('secondary', '#7C3AED'),
                'fg': COLORS.get('text_white', '#FFFFFF'),
                'activebackground': COLORS.get('secondary_dark', '#6D28D9'),
                'activeforeground': COLORS.get('text_white', '#FFFFFF')
            },
            'success': {
                'bg': COLORS.get('success', '#059669'),
                'fg': COLORS.get('text_white', '#FFFFFF'),
                'activebackground': COLORS.get('success_dark', '#047857'),
                'activeforeground': COLORS.get('text_white', '#FFFFFF')
            },
            'warning': {
                'bg': COLORS.get('warning', '#D97706'),
                'fg': COLORS.get('text_white', '#FFFFFF'),
                'activebackground': COLORS.get('warning_dark', '#B45309'),
                'activeforeground': COLORS.get('text_white', '#FFFFFF')
            },
            'danger': {
                'bg': COLORS.get('danger', '#DC2626'),
                'fg': COLORS.get('text_white', '#FFFFFF'),
                'activebackground': COLORS.get('danger_dark', '#B91C1C'),
                'activeforeground': COLORS.get('text_white', '#FFFFFF')
            },
            'outline': {
                'bg': COLORS.get('surface', '#FFFFFF'),
                'fg': COLORS.get('primary', '#1E40AF'),
                'relief': 'solid',
                'bd': 2,
                'activebackground': COLORS.get('primary_light', '#3B82F6'),
                'activeforeground': COLORS.get('primary', '#1E40AF')
            },
            'ghost': {
                'bg': COLORS.get('surface', '#FFFFFF'),
                'fg': COLORS.get('text_secondary', '#374151'),
                'relief': 'flat',
                'bd': 0,
                'activebackground': COLORS.get('light', '#F9FAFB'),
                'activeforeground': COLORS.get('text_primary', '#111827')
            }
        }
        
        # تطبيق الإعدادات الافتراضية
        default_config = {
            'text': text,
            'font': ('Segoe UI', 12, 'bold'),
            'relief': 'flat',
            'bd': 0,
            'padx': 20,
            'pady': 10,
            'cursor': 'hand2'
        }
        
        # إضافة ألوان النوع المحدد
        if button_type in button_colors:
            default_config.update(button_colors[button_type])
        
        default_config.update(kwargs)
        
        if command:
            default_config['command'] = command
        
        super().__init__(parent, **default_config)

class SimpleRTLEntry(tk.Entry):
    """حقل إدخال RTL مبسط"""
    
    def __init__(self, parent, placeholder="", **kwargs):
        # تطبيق الإعدادات الافتراضية
        default_config = {
            'font': ('Segoe UI', 12, 'normal'),
            'bg': COLORS.get('surface', '#FFFFFF'),
            'fg': COLORS.get('text_primary', '#000000'),
            'relief': 'solid',
            'bd': 2,
            'highlightthickness': 2,
            'highlightcolor': COLORS.get('primary', '#1E40AF'),
            'highlightbackground': COLORS.get('border', '#E5E7EB'),
            'insertbackground': COLORS.get('primary', '#1E40AF'),
            'selectbackground': COLORS.get('primary_light', '#3B82F6'),
            'selectforeground': COLORS.get('text_primary', '#000000'),
            'justify': 'right'
        }
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)
        
        # إضافة placeholder
        self.placeholder = placeholder
        if placeholder:
            self.add_placeholder()
    
    def add_placeholder(self):
        """إضافة نص placeholder"""
        self.placeholder_active = True
        self.insert(0, self.placeholder)
        self.configure(fg=COLORS.get('text_muted', '#6B7280'))
        
        def on_focus_in(event):
            if self.placeholder_active:
                self.delete(0, tk.END)
                self.configure(fg=COLORS.get('text_primary', '#000000'))
                self.placeholder_active = False
                
        def on_focus_out(event):
            if not self.get():
                self.insert(0, self.placeholder)
                self.configure(fg=COLORS.get('text_muted', '#6B7280'))
                self.placeholder_active = True
        
        self.bind("<FocusIn>", on_focus_in)
        self.bind("<FocusOut>", on_focus_out)

class SimpleRTLCombobox(ttk.Combobox):
    """قائمة منسدلة RTL مبسطة"""

    def __init__(self, parent, **kwargs):
        # فصل الخصائص المدعومة
        combo_kwargs = {}

        # الخصائص المدعومة في ttk.Combobox
        supported_options = [
            'textvariable', 'values', 'state', 'width', 'font', 'justify',
            'style', 'cursor', 'takefocus', 'exportselection'
        ]

        # إعدادات افتراضية
        default_config = {
            'font': ('Segoe UI', 12, 'normal'),
            'justify': 'right',
            'state': 'normal'
        }
        default_config.update(kwargs)

        # فلترة الخصائص المدعومة فقط
        for key, value in default_config.items():
            if key in supported_options:
                combo_kwargs[key] = value

        try:
            super().__init__(parent, **combo_kwargs)

            # تطبيق نمط مخصص
            try:
                style = ttk.Style()
                style.configure('RTL.TCombobox',
                              fieldbackground=COLORS.get('surface', '#FFFFFF'),
                              background=COLORS.get('surface', '#FFFFFF'),
                              foreground=COLORS.get('text_primary', '#000000'))
                self.configure(style='RTL.TCombobox')
            except:
                pass

        except Exception as e:
            # في حالة الخطأ، إنشاء combobox بسيط
            super().__init__(parent, justify='right')
            print(f"تحذير في SimpleRTLCombobox: {e}")

class SimpleRTLText(tk.Text):
    """منطقة نص RTL مبسطة"""

    def __init__(self, parent, **kwargs):
        # فصل الخصائص المدعومة
        text_kwargs = {}

        # الخصائص المدعومة في tk.Text
        supported_options = [
            'height', 'width', 'font', 'bg', 'fg', 'relief', 'bd', 'borderwidth',
            'highlightthickness', 'highlightcolor', 'highlightbackground',
            'insertbackground', 'selectbackground', 'selectforeground',
            'wrap', 'state', 'cursor', 'padx', 'pady', 'yscrollcommand', 'xscrollcommand'
        ]

        # تطبيق الإعدادات الافتراضية
        default_config = {
            'font': ('Segoe UI', 12, 'normal'),
            'bg': COLORS.get('surface', '#FFFFFF'),
            'fg': COLORS.get('text_primary', '#000000'),
            'relief': 'solid',
            'bd': 2,
            'highlightthickness': 2,
            'highlightcolor': COLORS.get('primary', '#1E40AF'),
            'highlightbackground': COLORS.get('border', '#E5E7EB'),
            'insertbackground': COLORS.get('primary', '#1E40AF'),
            'selectbackground': COLORS.get('primary_light', '#3B82F6'),
            'selectforeground': COLORS.get('text_primary', '#000000'),
            'wrap': 'word',
            'height': 4  # ارتفاع افتراضي
        }

        # دمج الإعدادات المرسلة
        default_config.update(kwargs)

        # فلترة الخصائص المدعومة فقط
        for key, value in default_config.items():
            if key in supported_options:
                text_kwargs[key] = value

        try:
            super().__init__(parent, **text_kwargs)

            # تطبيق محاذاة RTL
            self.tag_configure("rtl", justify='right')
            self.tag_add("rtl", "1.0", "end")

        except Exception as e:
            # في حالة الخطأ، إنشاء نص بسيط
            super().__init__(parent, height=kwargs.get('height', 4), width=kwargs.get('width', 40))
            print(f"تحذير في SimpleRTLText: {e}")

class SimpleRTLTreeview(ttk.Treeview):
    """جدول RTL مبسط"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # تطبيق نمط مخصص
        try:
            style = ttk.Style()
            style.configure('RTL.Treeview',
                          background=COLORS.get('surface', '#FFFFFF'),
                          foreground=COLORS.get('text_primary', '#000000'),
                          fieldbackground=COLORS.get('surface', '#FFFFFF'),
                          font=('Segoe UI', 11, 'normal'),
                          rowheight=30)
            
            style.configure('RTL.Treeview.Heading',
                          background=COLORS.get('primary', '#1E40AF'),
                          foreground=COLORS.get('text_white', '#FFFFFF'),
                          font=('Segoe UI', 11, 'bold'),
                          relief='flat')
            
            self.configure(style='RTL.Treeview')
        except:
            pass

class SimpleRTLCard(SimpleRTLFrame):
    """بطاقة RTL مبسطة"""
    
    def __init__(self, parent, title="", **kwargs):
        # إعدادات البطاقة
        card_config = {
            'bg': COLORS.get('surface', '#FFFFFF'),
            'relief': 'solid',
            'bd': 1,
            'highlightthickness': 2,
            'highlightcolor': COLORS.get('light', '#F9FAFB'),
            'highlightbackground': COLORS.get('border', '#E5E7EB')
        }
        card_config.update(kwargs)
        
        super().__init__(parent, **card_config)
        
        # إنشاء العنوان
        if title:
            self.create_title(title)
    
    def create_title(self, title):
        """إنشاء عنوان البطاقة"""
        title_frame = SimpleRTLFrame(self)
        title_frame.pack(fill='x', pady=(0, 10))
        
        title_label = SimpleRTLLabel(
            title_frame,
            text=title,
            font=('Segoe UI', 16, 'bold')
        )
        title_label.pack(anchor='e', padx=10, pady=5)
        
        # خط فاصل
        separator = tk.Frame(
            title_frame,
            height=2,
            bg=COLORS.get('primary', '#1E40AF'),
            relief='flat'
        )
        separator.pack(fill='x', padx=10)

class SimpleRTLToolbar(SimpleRTLFrame):
    """شريط أدوات RTL مبسط"""
    
    def __init__(self, parent, **kwargs):
        toolbar_config = {
            'bg': COLORS.get('light', '#F9FAFB'),
            'relief': 'raised',
            'bd': 2,
            'height': 60
        }
        toolbar_config.update(kwargs)
        
        super().__init__(parent, **toolbar_config)
        self.pack_propagate(False)
        
        # إنشاء حاوية الأزرار
        self.buttons_frame = SimpleRTLFrame(self)
        self.buttons_frame.pack(side='right', padx=15, pady=10)
    
    def add_button(self, text, command=None, button_type='primary', icon=None):
        """إضافة زر للشريط"""
        if icon:
            text = f"{icon} {text}"
        
        button = SimpleRTLButton(
            self.buttons_frame,
            text=text,
            button_type=button_type,
            command=command
        )
        button.pack(side='right', padx=5)
        
        return button

# وظائف مساعدة لإنشاء المكونات
def create_simple_rtl_frame(parent, **kwargs):
    """إنشاء إطار RTL مبسط"""
    return SimpleRTLFrame(parent, **kwargs)

def create_simple_rtl_label(parent, text="", **kwargs):
    """إنشاء تسمية RTL مبسطة"""
    return SimpleRTLLabel(parent, text=text, **kwargs)

def create_simple_rtl_button(parent, text="", button_type='primary', command=None, **kwargs):
    """إنشاء زر RTL مبسط"""
    return SimpleRTLButton(parent, text=text, button_type=button_type, command=command, **kwargs)

def create_simple_rtl_entry(parent, placeholder="", **kwargs):
    """إنشاء حقل إدخال RTL مبسط"""
    return SimpleRTLEntry(parent, placeholder=placeholder, **kwargs)

def create_simple_rtl_combobox(parent, **kwargs):
    """إنشاء قائمة منسدلة RTL مبسطة"""
    return SimpleRTLCombobox(parent, **kwargs)

def create_simple_rtl_text(parent, **kwargs):
    """إنشاء منطقة نص RTL مبسطة"""
    return SimpleRTLText(parent, **kwargs)

def create_simple_rtl_treeview(parent, **kwargs):
    """إنشاء جدول RTL مبسط"""
    return SimpleRTLTreeview(parent, **kwargs)

def create_simple_rtl_card(parent, title="", **kwargs):
    """إنشاء بطاقة RTL مبسطة"""
    return SimpleRTLCard(parent, title=title, **kwargs)

def create_simple_rtl_toolbar(parent, **kwargs):
    """إنشاء شريط أدوات RTL مبسط"""
    return SimpleRTLToolbar(parent, **kwargs)
