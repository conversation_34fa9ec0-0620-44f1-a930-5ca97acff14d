# 📦 قسم الأصناف الجديد في نموذج الشحنة

## 📋 نظرة عامة

تم إضافة **قسم الأصناف** الجديد كالقسم الثاني في نموذج الشحنة بملء الشاشة، ليكون بعد قسم المعلومات الأساسية مباشرة. هذا القسم مخصص لإدارة جميع الأصناف والمنتجات المشحونة مع تفاصيل الكميات والأسعار والمواصفات.

## ✨ الميزات الرئيسية

### 📊 **جدول الأصناف المتقدم**

#### **11 عمود شامل:**
1. **🔢 كود الصنف**: رقم تعريف فريد لكل صنف
2. **📦 اسم الصنف**: الاسم التجاري للمنتج
3. **📝 الوصف**: وصف تفصيلي للصنف
4. **📊 الكمية**: العدد أو الكمية المشحونة
5. **📏 الوحدة**: وحدة القياس (قطعة، كيلو، متر، إلخ)
6. **💰 سعر الوحدة**: السعر لكل وحدة
7. **💯 الإجمالي**: القيمة الإجمالية (محسوبة تلقائياً)
8. **⚖️ الوزن (كجم)**: وزن الصنف بالكيلوجرام
9. **📐 الحجم (م³)**: حجم الصنف بالمتر المكعب
10. **🏷️ كود HS**: كود النظام المنسق للتصنيف الجمركي
11. **🌍 بلد المنشأ**: البلد المصنع أو المنتج للصنف

### 🛠️ **أدوات إدارة الأصناف**

#### **شريط أدوات متقدم:**
- **➕ إضافة صنف**: إضافة صنف جديد للقائمة
- **✏️ تعديل صنف**: تعديل بيانات الصنف المحدد
- **🗑️ حذف صنف**: حذف الصنف مع تأكيد
- **📋 نسخ صنف**: نسخ صنف موجود لإنشاء مشابه

#### **تفاعل متقدم:**
- **نقر مزدوج**: لتعديل الصنف مباشرة
- **قائمة السياق**: بالنقر الأيمن على الصنف
- **اختيار متعدد**: لعمليات مجمعة
- **ترتيب الأعمدة**: حسب أي عمود

### 📊 **ملخص الأصناف التلقائي**

#### **إحصائيات شاملة:**
- **إجمالي الأصناف**: عدد الأصناف المختلفة
- **إجمالي الكمية**: مجموع جميع الكميات
- **إجمالي الوزن**: مجموع الأوزان بالكيلوجرام
- **إجمالي الحجم**: مجموع الأحجام بالمتر المكعب
- **إجمالي القيمة**: مجموع قيم جميع الأصناف
- **متوسط السعر**: متوسط سعر الوحدة

#### **تحديث تلقائي:**
- يتم تحديث الملخص فورياً عند إضافة/تعديل/حذف أي صنف
- ربط تلقائي مع القيمة الإجمالية في القسم المالي
- ألوان ذكية للقيم (أخضر للقيمة الإجمالية)

### 🎨 **ألوان الجدول الذكية**

#### **ألوان الصفوف:**
- **🟦 صفوف متناوبة**: أبيض ورمادي فاتح للوضوح
- **🟨 قيمة عالية**: خلفية صفراء للأصناف عالية القيمة (>10,000)
- **🟥 مخزون منخفض**: خلفية حمراء للكميات المنخفضة
- **🟦 محدد**: خلفية زرقاء للصنف المحدد

### 📝 **نافذة حوار الأصناف**

#### **تصميم RTL متقدم:**
- **حجم مناسب**: 600x500 بكسل
- **تخطيط منظم**: 6 صفوف مع حقول مرتبة
- **محاذاة يمين**: جميع العناصر محاذاة طبيعية للعربية
- **تحقق فوري**: من صحة البيانات أثناء الإدخال

#### **الحقول المتاحة:**

**الصف الأول:**
- 🔢 **كود الصنف** (مطلوب)
- 📦 **اسم الصنف** (مطلوب)

**الصف الثاني:**
- 📝 **وصف الصنف** (اختياري)

**الصف الثالث:**
- 📊 **الكمية** (مطلوب)
- 📏 **الوحدة** (مطلوب) - قائمة منسدلة

**الصف الرابع:**
- 💰 **سعر الوحدة** (مطلوب)
- 💯 **الإجمالي** (محسوب تلقائياً)

**الصف الخامس:**
- ⚖️ **الوزن (كجم)** (اختياري)
- 📐 **الحجم (م³)** (اختياري)

**الصف السادس:**
- 🏷️ **كود HS** (اختياري)
- 🌍 **بلد المنشأ** (اختياري) - قائمة منسدلة

#### **وحدات القياس المدعومة:**
- قطعة، كيلو، متر، لتر، طن، صندوق، كرتون

#### **بلدان المنشأ المدعومة:**
- السعودية، الإمارات، الكويت، قطر، البحرين، عمان
- الصين، الهند، تركيا، ألمانيا، أمريكا، اليابان

### 🧮 **الحسابات التلقائية**

#### **حساب الإجمالي:**
```
الإجمالي = الكمية × سعر الوحدة
```

#### **حساب الملخص:**
```
إجمالي الأصناف = عدد الأصناف في القائمة
إجمالي الكمية = مجموع جميع الكميات
إجمالي الوزن = مجموع جميع الأوزان
إجمالي الحجم = مجموع جميع الأحجام
إجمالي القيمة = مجموع جميع الإجماليات
متوسط السعر = إجمالي القيمة ÷ إجمالي الأصناف
```

## 🛠️ **التقنيات المستخدمة**

### 🗄️ **قاعدة البيانات المحسنة**

#### **جدول shipment_items:**
```sql
CREATE TABLE shipment_items (
    id TEXT PRIMARY KEY,
    shipment_id TEXT NOT NULL,
    item_code TEXT NOT NULL,
    item_name TEXT NOT NULL,
    description TEXT,
    quantity REAL NOT NULL DEFAULT 0,
    unit TEXT NOT NULL,
    unit_price REAL NOT NULL DEFAULT 0,
    total_price REAL NOT NULL DEFAULT 0,
    weight REAL DEFAULT 0,
    volume REAL DEFAULT 0,
    hs_code TEXT,
    origin_country TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shipment_id) REFERENCES shipments (id) ON DELETE CASCADE
);
```

### 🎨 **مكونات RTL مخصصة**

#### **create_simple_rtl_treeview():**
```python
def create_simple_rtl_treeview(parent, columns, show, height):
    """إنشاء جدول RTL مع أشرطة تمرير"""
    - دعم RTL كامل
    - أشرطة تمرير عمودية وأفقية
    - ألوان متناوبة للصفوف
    - تحديد متعدد
```

#### **ItemDialog Class:**
```python
class ItemDialog:
    """نافذة حوار الأصناف مع دعم RTL"""
    - 3 أوضاع: إضافة، تعديل، نسخ
    - تحقق شامل من البيانات
    - حسابات تلقائية
    - تصميم RTL متقدم
```

### 🔄 **الوظائف التفاعلية**

#### **إدارة الأصناف:**
```python
def add_item():        # إضافة صنف جديد
def edit_item():       # تعديل صنف موجود
def delete_item():     # حذف صنف مع تأكيد
def duplicate_item():  # نسخ صنف موجود
```

#### **تحديث البيانات:**
```python
def update_items_tree():     # تحديث جدول الأصناف
def update_items_summary():  # تحديث ملخص الأصناف
def save_shipment_items():   # حفظ الأصناف في قاعدة البيانات
def load_shipment_items():   # تحميل الأصناف من قاعدة البيانات
```

## 🎯 **التكامل مع النظام**

### 📊 **ترقيم الأقسام الجديد:**
1. **📋 المعلومات الأساسية**
2. **📦 الأصناف** ← **جديد!**
3. **🚢 معلومات الشحن**
4. **📦 معلومات الحاوية**
5. **💰 المعلومات المالية**
6. **📄 المستندات**
7. **📊 التتبع والحالة**
8. **📝 الملاحظات والمرفقات**

### ⌨️ **اختصارات لوحة المفاتيح المحدثة:**
- **Ctrl+2**: الانتقال المباشر لقسم الأصناف
- **Ctrl+1-8**: الانتقال المباشر لجميع الأقسام الثمانية

### 🔗 **الربط مع الأقسام الأخرى:**
- **القسم المالي**: تحديث تلقائي للقيمة الإجمالية
- **قسم الحاوية**: ربط الأوزان والأحجام
- **قسم المستندات**: ربط أكواد HS مع الوثائق الجمركية

## 🚀 **كيفية الاستخدام**

### **1. الوصول للقسم:**
```
📦 الأصناف (القسم 2)
- انقر على "📦 الأصناف" في الشريط العلوي
- استخدم Ctrl+2 للانتقال المباشر
- استخدم أزرار "◀ السابق" و "التالي ▶"
```

### **2. إضافة صنف جديد:**
```
1. انقر على "➕ إضافة صنف"
2. املأ البيانات المطلوبة (كود، اسم، كمية، وحدة، سعر)
3. املأ البيانات الاختيارية (وصف، وزن، حجم، كود HS، بلد المنشأ)
4. انقر "💾 حفظ"
```

### **3. تعديل صنف:**
```
1. اختر الصنف من الجدول
2. انقر "✏️ تعديل صنف" أو انقر مرتين على الصنف
3. عدّل البيانات المطلوبة
4. انقر "💾 حفظ"
```

### **4. حذف صنف:**
```
1. اختر الصنف من الجدول
2. انقر "🗑️ حذف صنف"
3. أكد الحذف في النافذة المنبثقة
```

### **5. نسخ صنف:**
```
1. اختر الصنف من الجدول
2. انقر "📋 نسخ صنف"
3. عدّل البيانات حسب الحاجة (سيتم مسح كود الصنف)
4. انقر "💾 حفظ"
```

### **6. مراقبة الملخص:**
```
الملخص يتحدث تلقائياً ويعرض:
- إجمالي الأصناف والكميات
- إجمالي الأوزان والأحجام
- إجمالي القيمة ومتوسط السعر
```

## 🎉 **النتيجة النهائية**

**✅ تم إضافة قسم الأصناف بنجاح!**

### **🌟 الإنجازات المحققة:**
- 📦 **قسم أصناف شامل** مع جدول 11 عمود
- 🛠️ **أدوات إدارة متقدمة** مع 4 عمليات أساسية
- 📊 **ملخص تلقائي** مع 6 إحصائيات مهمة
- 🎨 **تصميم RTL متقدم** مع ألوان ذكية
- 📝 **نافذة حوار متكاملة** مع تحقق شامل
- 🗄️ **قاعدة بيانات محسنة** مع جدول مخصص
- 🔄 **تكامل كامل** مع النظام الموجود
- ⌨️ **اختصارات محدثة** للوصول السريع

### **📊 الإحصائيات:**
- **إجمالي الأقسام**: 8 أقسام (كان 7)
- **إجمالي الأعمدة**: 11 عمود في جدول الأصناف
- **إجمالي الحقول**: 11 حقل في نافذة الحوار
- **إجمالي الوظائف**: 8 وظائف جديدة لإدارة الأصناف

**قسم الأصناف جاهز للاستخدام الإنتاجي مع تجربة مستخدم متقدمة!** 📦✨📊🎨
