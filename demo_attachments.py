#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لأزرار المرفقات في قسم المستندات
Demo for Attachments Buttons in Documents Section
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def create_demo_window():
    """إنشاء نافذة العرض التوضيحي"""
    
    root = tk.Tk()
    root.title("📎 عرض توضيحي - أزرار المرفقات في المستندات")
    root.geometry("900x700")
    root.configure(bg='#f8f9fa')
    
    # العنوان الرئيسي
    title_frame = tk.Frame(root, bg='#2563eb', height=80)
    title_frame.pack(fill='x')
    title_frame.pack_propagate(False)
    
    title_label = tk.Label(
        title_frame,
        text="📎 أزرار المرفقات في قسم المستندات",
        font=('Arial', 18, 'bold'),
        fg='white',
        bg='#2563eb'
    )
    title_label.pack(expand=True)
    
    # المحتوى الرئيسي
    main_frame = tk.Frame(root, bg='#f8f9fa')
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # معلومات الميزة
    info_text = """
🎉 تم إضافة أزرار المرفقات بنجاح!

✨ الميزات الجديدة:
• زر "📎 إضافة مرفق" بجانب كل حقل مستند (9 أزرار)
• دعم أنواع ملفات متعددة: PDF, صور, Word, Excel
• عرض اسم الملف وحجمه تحت كل حقل
• إمكانية عرض تفاصيل جميع المرفقات
• واجهة عربية كاملة مع دعم RTL

📋 المستندات المدعومة:
1. 📋 الفاتورة التجارية + 📎 إضافة مرفق
2. 📦 قائمة التعبئة + 📎 إضافة مرفق
3. 🏭 شهادة المنشأ + 📎 إضافة مرفق
4. 🛡️ بوليصة التأمين + 📎 إضافة مرفق
5. ✅ شهادة الجودة + 📎 إضافة مرفق
6. 🏥 شهادة الصحة + 📎 إضافة مرفق
7. 📸 صور الأصناف + 📎 إضافة مرفق
8. 📜 رخصة الاستيراد + 📎 إضافة مرفق
9. 🔍 شهادة التفتيش + 📎 إضافة مرفق

🚀 كيفية الاستخدام:
1. انتقل لقسم "📄 المستندات" في نموذج الشحنة
2. انقر على زر "📎 إضافة مرفق" بجانب أي مستند
3. اختر الملف من نافذة التصفح
4. سيظهر اسم الملف وحجمه تحت الحقل
5. انقر على اسم الملف لعرض التفاصيل الكاملة

💡 نصائح:
• يمكن إرفاق ملفات متعددة لكل مستند
• الملفات المدعومة: PDF, JPG, PNG, DOC, DOCX, XLS, XLSX
• يتم عرض حجم الملف تلقائياً (بايت/كيلوبايت/ميجابايت)
• جميع المرفقات تحفظ مع بيانات الشحنة
    """
    
    # عرض النص
    text_widget = tk.Text(
        main_frame,
        wrap='word',
        font=('Arial', 11),
        bg='white',
        fg='#374151',
        padx=20,
        pady=20,
        relief='solid',
        bd=1
    )
    text_widget.pack(fill='both', expand=True)
    text_widget.insert('1.0', info_text)
    text_widget.configure(state='disabled')
    
    # شريط التمرير
    scrollbar = ttk.Scrollbar(text_widget, orient='vertical', command=text_widget.yview)
    scrollbar.pack(side='right', fill='y')
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    # أزرار الإجراءات
    buttons_frame = tk.Frame(root, bg='#f8f9fa', height=60)
    buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
    buttons_frame.pack_propagate(False)
    
    # زر فتح النموذج الكامل
    def open_full_form():
        try:
            # تهيئة مدير المصادقة
            from src.auth_manager import auth_manager
            auth_manager.current_user = {
                'username': 'demo_user',
                'role': 'admin',
                'permissions': ['all']
            }
            
            # فتح النموذج
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form_window = tk.Toplevel(root)
            form = FullscreenShipmentForm(form_window, mode='add')
            form.switch_section('documents')  # الانتقال لقسم المستندات
            
            messagebox.showinfo(
                "تم فتح النموذج",
                "تم فتح نموذج الشحنة!\nانتقل لقسم 'المستندات' لرؤية أزرار المرفقات"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح النموذج: {str(e)}")
    
    open_btn = tk.Button(
        buttons_frame,
        text="🚀 فتح نموذج الشحنة مع أزرار المرفقات",
        command=open_full_form,
        font=('Arial', 12, 'bold'),
        bg='#10b981',
        fg='white',
        padx=20,
        pady=10,
        relief='flat',
        cursor='hand2'
    )
    open_btn.pack(side='right', padx=10)
    
    # زر الإغلاق
    close_btn = tk.Button(
        buttons_frame,
        text="❌ إغلاق",
        command=root.destroy,
        font=('Arial', 12),
        bg='#ef4444',
        fg='white',
        padx=20,
        pady=10,
        relief='flat',
        cursor='hand2'
    )
    close_btn.pack(side='right')
    
    return root

def main():
    """الدالة الرئيسية"""
    print("📎 بدء العرض التوضيحي لأزرار المرفقات...")
    print("💡 سيتم فتح نافذة توضيحية مع معلومات الميزة الجديدة")
    print("-" * 60)
    
    try:
        root = create_demo_window()
        
        # إظهار رسالة ترحيب
        messagebox.showinfo(
            "مرحباً بك!",
            """🎉 مرحباً بك في العرض التوضيحي!

تم إضافة أزرار المرفقات بنجاح لجميع حقول المستندات.

📋 ستجد في هذه النافذة:
• شرح مفصل للميزة الجديدة
• قائمة بجميع المستندات المدعومة
• تعليمات الاستخدام
• زر لفتح النموذج الكامل للتجربة

🚀 انقر على "فتح نموذج الشحنة" لتجربة الميزة!"""
        )
        
        root.mainloop()
        print("✅ انتهى العرض التوضيحي بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
