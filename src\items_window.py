#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الأصناف
Items Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, UNITS_OF_MEASURE

class ItemsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_items()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الأصناف - نظام متابعة الشحنات")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="إدارة الأصناف",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار العمليات
        self.add_button = tk.Button(
            buttons_frame,
            text="إضافة صنف جديد",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.add_item
        )
        self.add_button.pack(side='right', padx=5)
        
        self.edit_button = tk.Button(
            buttons_frame,
            text="تعديل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['warning'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.edit_item,
            state='disabled'
        )
        self.edit_button.pack(side='right', padx=5)
        
        self.delete_button = tk.Button(
            buttons_frame,
            text="حذف",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['danger'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.delete_item,
            state='disabled'
        )
        self.delete_button.pack(side='right', padx=5)
        
        self.categories_button = tk.Button(
            buttons_frame,
            text="إدارة الفئات",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.manage_categories
        )
        self.categories_button.pack(side='right', padx=5)
        
        self.refresh_button = tk.Button(
            buttons_frame,
            text="تحديث",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.load_items
        )
        self.refresh_button.pack(side='right', padx=5)
        
        # إطار البحث والتصفية
        filter_frame = tk.Frame(main_frame, bg=COLORS['background'])
        filter_frame.pack(fill='x', pady=(0, 10))
        
        # البحث
        search_label = tk.Label(
            filter_frame,
            text="البحث:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        search_label.pack(side='right', padx=(0, 5))
        
        self.search_entry = tk.Entry(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            justify='right',
            width=20
        )
        self.search_entry.pack(side='right', padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.search_items)
        
        # تصفية حسب الفئة
        category_label = tk.Label(
            filter_frame,
            text="الفئة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        category_label.pack(side='right', padx=(0, 5))
        
        self.category_filter = ttk.Combobox(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            state='readonly',
            width=15
        )
        self.category_filter.pack(side='right', padx=(0, 10))
        self.category_filter.bind('<<ComboboxSelected>>', self.filter_by_category)
        
        # تحميل الفئات
        self.load_categories()
        
        # جدول الأصناف
        self.create_items_table(main_frame)
        
    def create_items_table(self, parent):
        """إنشاء جدول الأصناف"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=COLORS['white'])
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        columns = ('item_code', 'item_name', 'category', 'unit', 'cost_price', 
                  'selling_price', 'current_stock', 'min_stock', 'is_active')
        self.items_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        headers = {
            'item_code': 'كود الصنف',
            'item_name': 'اسم الصنف',
            'category': 'الفئة',
            'unit': 'الوحدة',
            'cost_price': 'سعر التكلفة',
            'selling_price': 'سعر البيع',
            'current_stock': 'المخزون الحالي',
            'min_stock': 'الحد الأدنى',
            'is_active': 'الحالة'
        }
        
        for col in columns:
            self.items_tree.heading(col, text=headers[col])
            if col in ['cost_price', 'selling_price', 'current_stock', 'min_stock']:
                self.items_tree.column(col, width=100, anchor='center')
            else:
                self.items_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.items_tree.bind('<<TreeviewSelect>>', self.on_item_select)
        self.items_tree.bind('<Double-1>', self.edit_item)
    
    def load_categories(self):
        """تحميل الفئات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT category_name FROM item_categories WHERE is_active = 1 ORDER BY category_name')
            categories = cursor.fetchall()
            
            category_list = ['جميع الفئات'] + [cat['category_name'] for cat in categories]
            self.category_filter['values'] = category_list
            self.category_filter.set('جميع الفئات')
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات: {str(e)}")
    
    def load_items(self):
        """تحميل الأصناف من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT i.item_code, i.item_name, c.category_name, i.unit_of_measure,
                       i.cost_price, i.selling_price, i.current_stock, i.min_stock_level, i.is_active
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                ORDER BY i.item_name
            ''')
            
            items = cursor.fetchall()
            
            for item in items:
                status = "نشط" if item['is_active'] else "غير نشط"
                values = (
                    item['item_code'],
                    item['item_name'],
                    item['category_name'] or 'بدون فئة',
                    item['unit_of_measure'] or '',
                    f"{item['cost_price']:.2f}" if item['cost_price'] else '0.00',
                    f"{item['selling_price']:.2f}" if item['selling_price'] else '0.00',
                    str(item['current_stock'] or 0),
                    str(item['min_stock_level'] or 0),
                    status
                )
                self.items_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")
    
    def search_items(self, event=None):
        """البحث في الأصناف"""
        search_term = self.search_entry.get().strip()
        category_filter = self.category_filter.get()
        
        self.filter_items(search_term, category_filter)
    
    def filter_by_category(self, event=None):
        """تصفية حسب الفئة"""
        search_term = self.search_entry.get().strip()
        category_filter = self.category_filter.get()
        
        self.filter_items(search_term, category_filter)
    
    def filter_items(self, search_term="", category_filter="جميع الفئات"):
        """تصفية الأصناف"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            query = '''
                SELECT i.item_code, i.item_name, c.category_name, i.unit_of_measure,
                       i.cost_price, i.selling_price, i.current_stock, i.min_stock_level, i.is_active
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                WHERE 1=1
            '''
            params = []
            
            if search_term:
                query += ' AND (i.item_name LIKE ? OR i.item_code LIKE ?)'
                params.extend([f'%{search_term}%', f'%{search_term}%'])
            
            if category_filter != 'جميع الفئات':
                query += ' AND c.category_name = ?'
                params.append(category_filter)
            
            query += ' ORDER BY i.item_name'
            
            cursor.execute(query, params)
            items = cursor.fetchall()
            
            for item in items:
                status = "نشط" if item['is_active'] else "غير نشط"
                values = (
                    item['item_code'],
                    item['item_name'],
                    item['category_name'] or 'بدون فئة',
                    item['unit_of_measure'] or '',
                    f"{item['cost_price']:.2f}" if item['cost_price'] else '0.00',
                    f"{item['selling_price']:.2f}" if item['selling_price'] else '0.00',
                    str(item['current_stock'] or 0),
                    str(item['min_stock_level'] or 0),
                    status
                )
                self.items_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التصفية: {str(e)}")
    
    def on_item_select(self, event=None):
        """عند اختيار صنف من الجدول"""
        selection = self.items_tree.selection()
        if selection:
            self.edit_button.config(state='normal')
            self.delete_button.config(state='normal')
        else:
            self.edit_button.config(state='disabled')
            self.delete_button.config(state='disabled')
    
    def add_item(self):
        """إضافة صنف جديد"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإضافة الأصناف")
            return
        
        self.open_item_form()
    
    def edit_item(self, event=None):
        """تعديل صنف"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل الأصناف")
            return
        
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return
        
        item = self.items_tree.item(selection[0])
        item_code = item['values'][0]
        self.open_item_form(item_code)
    
    def delete_item(self):
        """حذف صنف"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لحذف الأصناف")
            return
        
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return
        
        item = self.items_tree.item(selection[0])
        item_name = item['values'][1]
        item_code = item['values'][0]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الصنف '{item_name}'؟"):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                
                # التحقق من وجود حركات مخزون مرتبطة بالصنف
                cursor.execute('SELECT COUNT(*) as count FROM inventory_movements WHERE item_id = (SELECT id FROM items WHERE item_code = ?)', (item_code,))
                result = cursor.fetchone()
                
                if result['count'] > 0:
                    messagebox.showerror("خطأ", "لا يمكن حذف هذا الصنف لأنه مرتبط بحركات مخزون")
                    conn.close()
                    return
                
                # حذف الصنف
                cursor.execute('DELETE FROM items WHERE item_code = ?', (item_code,))
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.load_items()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الصنف: {str(e)}")
    
    def manage_categories(self):
        """إدارة فئات الأصناف"""
        try:
            from src.categories_window import CategoriesWindow
            categories_window = CategoriesWindow(self.window, self.load_categories)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة إدارة الفئات قريباً")
    
    def open_item_form(self, item_code=None):
        """فتح نموذج الصنف"""
        try:
            from src.item_form import ItemForm
            ItemForm(self.window, self.db_manager, item_code, self.load_items)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نموذج الصنف قريباً")


if __name__ == "__main__":
    # تشغيل نافذة الأصناف للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    items_window = ItemsWindow()
    items_window.window.mainloop()
