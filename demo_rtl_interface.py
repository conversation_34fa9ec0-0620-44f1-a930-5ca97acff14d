#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي للواجهة RTL المتقدمة
RTL Interface Demo
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG
from src.ui_styles import get_style_manager
from src.rtl_components import *

class RTLDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.style_manager = get_style_manager()
        self.setup_window()
        self.create_demo_interface()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("عرض توضيحي - واجهة RTL متقدمة")
        self.root.geometry("1200x800")
        self.root.configure(bg=COLORS['background'])
        
        # تكوين RTL
        self.root.option_add('*Font', self.style_manager.get_font('arabic'))
    
    def create_demo_interface(self):
        """إنشاء واجهة العرض التوضيحي"""
        # الإطار الرئيسي
        main_frame = create_rtl_frame(self.root, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = create_rtl_label(
            main_frame,
            text="🎨 عرض توضيحي للواجهة RTL المتقدمة",
            style='title'
        )
        title_label.pack(pady=(0, 30))
        
        # إطار المحتوى
        content_frame = create_rtl_frame(main_frame)
        content_frame.pack(fill='both', expand=True)
        
        # الجانب الأيمن - البطاقات
        right_frame = create_rtl_frame(content_frame)
        right_frame.pack(side='right', fill='both', expand=True, padx=(0, 10))
        
        self.create_cards_demo(right_frame)
        
        # الجانب الأيسر - النماذج
        left_frame = create_rtl_frame(content_frame)
        left_frame.pack(side='left', fill='both', expand=True, padx=(10, 0))
        
        self.create_forms_demo(left_frame)
    
    def create_cards_demo(self, parent):
        """إنشاء عرض البطاقات"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="📋 البطاقات والمكونات",
            style='header'
        )
        section_title.pack(pady=(0, 20))
        
        # بطاقة الألوان
        colors_card = create_rtl_card(parent, title="🎨 نظام الألوان")
        colors_card.pack(fill='x', pady=(0, 15))
        
        colors_frame = create_rtl_frame(colors_card)
        colors_frame.pack(fill='x', padx=15, pady=15)
        
        # أزرار الألوان
        color_styles = ['primary', 'secondary', 'success', 'danger', 'warning', 'info']
        color_names = ['أساسي', 'ثانوي', 'نجاح', 'خطر', 'تحذير', 'معلومات']
        
        for i, (style, name) in enumerate(zip(color_styles, color_names)):
            btn = create_rtl_button(
                colors_frame,
                text=f"{name} {style.upper()}",
                style=style,
                command=lambda s=style: self.show_message(f"تم النقر على زر {s}")
            )
            btn.pack(fill='x', pady=2)
        
        # بطاقة الخطوط
        fonts_card = create_rtl_card(parent, title="🔤 الخطوط العربية")
        fonts_card.pack(fill='x', pady=(0, 15))
        
        fonts_frame = create_rtl_frame(fonts_card)
        fonts_frame.pack(fill='x', padx=15, pady=15)
        
        # عينات الخطوط
        font_samples = [
            ("خط العنوان الكبير", 'title'),
            ("خط الرأس المتوسط", 'header'),
            ("خط النص العادي", 'default'),
            ("خط النص الصغير", 'muted')
        ]
        
        for text, style in font_samples:
            label = create_rtl_label(fonts_frame, text=text, style=style)
            label.pack(pady=3, anchor='e')
        
        # بطاقة الإحصائيات
        stats_card = create_rtl_card(parent, title="📊 إحصائيات تجريبية")
        stats_card.pack(fill='x')
        
        stats_frame = create_rtl_frame(stats_card)
        stats_frame.pack(fill='x', padx=15, pady=15)
        
        # إحصائيات وهمية
        stats = [
            ("المستخدمين النشطين", "1,234", COLORS['success']),
            ("الطلبات اليوم", "567", COLORS['primary']),
            ("المبيعات الشهر", "89,012", COLORS['warning']),
            ("التقييم العام", "4.8/5", COLORS['info'])
        ]
        
        for title, value, color in stats:
            stat_frame = create_rtl_frame(stats_frame, bg=color)
            stat_frame.pack(fill='x', pady=2)
            
            value_label = create_rtl_label(
                stat_frame,
                text=value,
                font=self.style_manager.get_font('arabic', size=16, weight='bold'),
                bg=color,
                fg=COLORS['text_white']
            )
            value_label.pack(side='left', padx=10, pady=5)
            
            title_label = create_rtl_label(
                stat_frame,
                text=title,
                bg=color,
                fg=COLORS['text_white']
            )
            title_label.pack(side='right', padx=10, pady=5)
    
    def create_forms_demo(self, parent):
        """إنشاء عرض النماذج"""
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="📝 النماذج والمدخلات",
            style='header'
        )
        section_title.pack(pady=(0, 20))
        
        # نموذج تجريبي
        form_card = create_rtl_card(parent, title="👤 نموذج بيانات المستخدم")
        form_card.pack(fill='x', pady=(0, 15))
        
        form_frame = create_rtl_frame(form_card)
        form_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # حقول النموذج
        fields = [
            ("الاسم الكامل:", "أدخل الاسم الكامل"),
            ("البريد الإلكتروني:", "<EMAIL>"),
            ("رقم الهاتف:", "+966 50 123 4567"),
            ("المدينة:", "اختر المدينة")
        ]
        
        self.form_entries = {}
        
        for label_text, placeholder in fields:
            # إطار الحقل
            field_frame = create_rtl_frame(form_frame)
            field_frame.pack(fill='x', pady=5)
            
            # التسمية
            label = create_rtl_label(field_frame, text=label_text)
            label.pack(anchor='e', pady=(0, 2))
            
            # حقل الإدخال
            if label_text == "المدينة:":
                # قائمة منسدلة
                entry = create_rtl_combobox(
                    field_frame,
                    values=["الرياض", "جدة", "الدمام", "مكة المكرمة", "المدينة المنورة"]
                )
            else:
                # حقل نص
                entry = create_rtl_entry(field_frame)
                entry.insert(0, placeholder)
                entry.bind('<FocusIn>', lambda e: e.widget.delete(0, 'end'))
            
            entry.pack(fill='x', pady=(0, 5))
            self.form_entries[label_text] = entry
        
        # منطقة النص
        notes_label = create_rtl_label(form_frame, text="ملاحظات إضافية:")
        notes_label.pack(anchor='e', pady=(10, 2))
        
        self.notes_text = create_rtl_text(form_frame, height=4)
        self.notes_text.pack(fill='x', pady=(0, 10))
        self.notes_text.insert('1.0', "أدخل أي ملاحظات إضافية هنا...")
        
        # أزرار النموذج
        buttons_frame = create_rtl_frame(form_frame)
        buttons_frame.pack(fill='x', pady=10)
        
        save_btn = create_rtl_button(
            buttons_frame,
            text="💾 حفظ البيانات",
            style='success',
            command=self.save_form_data
        )
        save_btn.pack(side='right', padx=(5, 0))
        
        clear_btn = create_rtl_button(
            buttons_frame,
            text="🗑️ مسح الحقول",
            style='outline',
            command=self.clear_form_data
        )
        clear_btn.pack(side='right')
        
        # جدول تجريبي
        table_card = create_rtl_card(parent, title="📋 جدول البيانات")
        table_card.pack(fill='both', expand=True)
        
        table_frame = create_rtl_frame(table_card)
        table_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # إنشاء الجدول
        columns = ('name', 'email', 'city', 'status')
        column_names = ['الاسم', 'البريد الإلكتروني', 'المدينة', 'الحالة']
        
        self.tree = create_rtl_treeview(table_frame, columns=columns, show='headings', height=6)
        
        # تكوين الأعمدة
        for col, name in zip(columns, column_names):
            self.tree.heading(col, text=name, anchor='e')
            self.tree.column(col, anchor='e', width=150)
        
        # إضافة بيانات تجريبية
        sample_data = [
            ("أحمد محمد", "<EMAIL>", "الرياض", "نشط"),
            ("فاطمة علي", "<EMAIL>", "جدة", "نشط"),
            ("محمد سالم", "<EMAIL>", "الدمام", "غير نشط"),
            ("نورا أحمد", "<EMAIL>", "مكة المكرمة", "نشط")
        ]
        
        for data in sample_data:
            self.tree.insert('', 'end', values=data)
        
        self.tree.pack(fill='both', expand=True)
        
        # شريط التمرير للجدول
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        scrollbar.pack(side='left', fill='y')
        self.tree.configure(yscrollcommand=scrollbar.set)
    
    def save_form_data(self):
        """حفظ بيانات النموذج"""
        data = {}
        for label, entry in self.form_entries.items():
            if hasattr(entry, 'get'):
                data[label] = entry.get()
        
        notes = self.notes_text.get('1.0', 'end-1c')
        data['الملاحظات'] = notes
        
        self.show_message("تم حفظ البيانات بنجاح!")
    
    def clear_form_data(self):
        """مسح بيانات النموذج"""
        for entry in self.form_entries.values():
            if hasattr(entry, 'delete'):
                entry.delete(0, 'end')
            elif hasattr(entry, 'set'):
                entry.set('')
        
        self.notes_text.delete('1.0', 'end')
        self.show_message("تم مسح جميع الحقول")
    
    def show_message(self, message):
        """عرض رسالة"""
        # إنشاء نافذة رسالة مخصصة
        msg_window = tk.Toplevel(self.root)
        msg_window.title("رسالة")
        msg_window.geometry("300x150")
        msg_window.configure(bg=COLORS['surface'])
        msg_window.resizable(False, False)
        
        # توسيط النافذة
        msg_window.transient(self.root)
        msg_window.grab_set()
        
        # محتوى الرسالة
        msg_frame = create_rtl_frame(msg_window, bg=COLORS['surface'])
        msg_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        msg_label = create_rtl_label(
            msg_frame,
            text=message,
            style='default',
            bg=COLORS['surface']
        )
        msg_label.pack(expand=True)
        
        ok_btn = create_rtl_button(
            msg_frame,
            text="موافق",
            style='primary',
            command=msg_window.destroy
        )
        ok_btn.pack(pady=(10, 0))
        
        # تركيز على الزر
        ok_btn.focus_set()
        msg_window.bind('<Return>', lambda e: msg_window.destroy())
    
    def run(self):
        """تشغيل العرض التوضيحي"""
        self.root.mainloop()

if __name__ == "__main__":
    demo = RTLDemo()
    demo.run()
