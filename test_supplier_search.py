#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة البحث في الموردين
Test Supplier Search Dialog
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.supplier_search_dialog import SupplierSearchDialog

def test_supplier_search():
    """اختبار نافذة البحث في الموردين"""
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار البحث في الموردين")
    root.geometry("400x300")
    root.configure(bg='#f0f0f0')
    
    # إخفاء النافذة الرئيسية
    root.withdraw()
    
    def open_search():
        """فتح نافذة البحث"""
        try:
            # إنشاء نافذة البحث
            search_dialog = SupplierSearchDialog(root, None)
            
            # انتظار إغلاق النافذة
            root.wait_window(search_dialog.root)
            
            # عرض النتيجة
            if hasattr(search_dialog, 'selected_supplier') and search_dialog.selected_supplier:
                supplier = search_dialog.selected_supplier
                result_text = f"""
تم اختيار المورد:
🏢 الاسم: {supplier.get('name', 'غير محدد')}
📞 معلومات الاتصال: {supplier.get('contact_info', 'غير محدد')}
🌍 البلد: {supplier.get('country', 'غير محدد')}
⭐ التقييم: {supplier.get('rating', 'غير محدد')}
                """
                
                # إظهار النافذة الرئيسية لعرض النتيجة
                root.deiconify()
                result_label.configure(text=result_text)
                
            else:
                # إظهار النافذة الرئيسية
                root.deiconify()
                result_label.configure(text="لم يتم اختيار أي مورد")
                
        except Exception as e:
            # إظهار النافذة الرئيسية في حالة الخطأ
            root.deiconify()
            result_label.configure(text=f"خطأ: {str(e)}")
    
    # إظهار النافذة الرئيسية مؤقتاً لإنشاء المكونات
    root.deiconify()
    
    # عنوان
    title_label = tk.Label(
        root,
        text="🔍 اختبار البحث في الموردين",
        font=('Segoe UI', 16, 'bold'),
        bg='#f0f0f0',
        fg='#2563eb'
    )
    title_label.pack(pady=20)
    
    # وصف
    desc_label = tk.Label(
        root,
        text="انقر على الزر أدناه لفتح نافذة البحث في الموردين",
        font=('Segoe UI', 12, 'normal'),
        bg='#f0f0f0',
        fg='#374151'
    )
    desc_label.pack(pady=10)
    
    # زر فتح البحث
    search_button = tk.Button(
        root,
        text="🔍 فتح البحث في الموردين (F9)",
        font=('Segoe UI', 14, 'bold'),
        bg='#10b981',
        fg='white',
        activebackground='#059669',
        activeforeground='white',
        relief='flat',
        padx=20,
        pady=10,
        command=open_search
    )
    search_button.pack(pady=20)
    
    # منطقة النتيجة
    result_frame = tk.Frame(root, bg='#f0f0f0')
    result_frame.pack(fill='both', expand=True, padx=20, pady=10)
    
    result_label = tk.Label(
        result_frame,
        text="اضغط على الزر لبدء الاختبار",
        font=('Segoe UI', 11, 'normal'),
        bg='#f9fafb',
        fg='#6b7280',
        relief='solid',
        borderwidth=1,
        padx=10,
        pady=10,
        justify='right',
        anchor='e'
    )
    result_label.pack(fill='both', expand=True)
    
    # زر الإغلاق
    close_button = tk.Button(
        root,
        text="❌ إغلاق",
        font=('Segoe UI', 12, 'normal'),
        bg='#ef4444',
        fg='white',
        activebackground='#dc2626',
        activeforeground='white',
        relief='flat',
        padx=15,
        pady=5,
        command=root.quit
    )
    close_button.pack(pady=10)
    
    # ربط F9 لفتح البحث
    root.bind('<F9>', lambda e: open_search())
    root.bind('<KeyPress-F9>', lambda e: open_search())
    
    # ربط Escape للإغلاق
    root.bind('<Escape>', lambda e: root.quit())
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    print("🔍 بدء اختبار نافذة البحث في الموردين...")
    print("💡 استخدم F9 أو انقر على الزر لفتح البحث")
    print("💡 استخدم Escape للإغلاق")
    print("-" * 50)
    
    try:
        test_supplier_search()
        print("✅ انتهى الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
