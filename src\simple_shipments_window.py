#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الشحنات البسيطة
Simple Shipments Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import APP_CONFIG, COLORS, FONTS, SHIPMENT_STATUS
from database.database_manager import DatabaseManager
from src.auth_manager import auth_manager

class SimpleShipmentsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.db_manager = DatabaseManager()
        self.shipments_data = []
        self.selected_shipment = None
        
        self.setup_window()
        self.create_interface()
        self.load_data()
        self.center_window()

    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🚢 إدارة الشحنات")
        self.root.geometry("1200x700")
        self.root.configure(bg=COLORS.get('background', '#F5F5F5'))
        self.root.state('zoomed')  # ملء الشاشة
        
        # ربط أحداث لوحة المفاتيح
        self.root.bind('<F5>', lambda e: self.refresh_data())
        self.root.bind('<Control-n>', lambda e: self.add_shipment())
        self.root.bind('<Delete>', lambda e: self.delete_shipment())
        self.root.bind('<Escape>', lambda e: self.on_closing())
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg=COLORS.get('background', '#F5F5F5'))
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # الشريط العلوي
        self.create_header(main_frame)
        
        # شريط الأدوات
        self.create_toolbar(main_frame)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg=COLORS.get('background', '#F5F5F5'))
        content_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        # جدول الشحنات
        self.create_shipments_table(content_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)

    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(
            parent,
            bg=COLORS.get('primary', '#2E86AB'),
            height=60,
            relief='raised',
            bd=2
        )
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # عنوان النافذة
        title_label = tk.Label(
            header_frame,
            text="🚢 إدارة الشحنات",
            font=('Tahoma', 16, 'bold'),
            bg=COLORS.get('primary', '#2E86AB'),
            fg='white'
        )
        title_label.pack(side='right', padx=20, pady=15)
        
        # معلومات المستخدم
        current_user = auth_manager.get_current_user()
        user_info = f"👤 {current_user['username'] if current_user else 'مستخدم'}"
        user_label = tk.Label(
            header_frame,
            text=user_info,
            font=('Tahoma', 12),
            bg=COLORS.get('primary', '#2E86AB'),
            fg='white'
        )
        user_label.pack(side='left', padx=20, pady=15)

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(parent, bg=COLORS.get('light', '#E9ECEF'), relief='raised', bd=1)
        toolbar_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار الأدوات
        buttons = [
            ("➕ شحنة جديدة", self.add_shipment, COLORS.get('success', '#28A745')),
            ("✏️ تعديل", self.edit_shipment, COLORS.get('warning', '#FFC107')),
            ("🗑️ حذف", self.delete_shipment, COLORS.get('danger', '#DC3545')),
            ("🔄 تحديث", self.refresh_data, COLORS.get('info', '#17A2B8')),
            ("📊 تقرير", self.generate_report, COLORS.get('secondary', '#6C757D'))
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(
                toolbar_frame,
                text=text,
                font=('Tahoma', 10),
                bg=color,
                fg='white',
                relief='flat',
                cursor='hand2',
                command=command,
                padx=15,
                pady=5
            )
            btn.pack(side='right', padx=5, pady=5)
        
        # حقل البحث
        search_frame = tk.Frame(toolbar_frame, bg=COLORS.get('light', '#E9ECEF'))
        search_frame.pack(side='left', padx=10, pady=5)
        
        tk.Label(
            search_frame,
            text="🔍 البحث:",
            font=('Tahoma', 10),
            bg=COLORS.get('light', '#E9ECEF'),
            fg=COLORS.get('text_primary', '#000000')
        ).pack(side='left', padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        self.search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Tahoma', 10),
            width=20,
            relief='solid',
            bd=1
        )
        self.search_entry.pack(side='left')

    def create_shipments_table(self, parent):
        """إنشاء جدول الشحنات"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg='white', relief='raised', bd=2)
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        columns = ('shipment_number', 'supplier_name', 'status', 'shipment_date', 'departure_port', 'arrival_port', 'total_value')
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        headers = {
            'shipment_number': 'رقم الشحنة',
            'supplier_name': 'المورد',
            'status': 'الحالة',
            'shipment_date': 'تاريخ الشحن',
            'departure_port': 'ميناء المغادرة',
            'arrival_port': 'ميناء الوصول',
            'total_value': 'القيمة الإجمالية'
        }
        
        # تكوين العناوين والأعمدة
        for col in columns:
            self.tree.heading(col, text=headers[col], anchor='center')
            self.tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar_y.pack(side='right', fill='y')
        scrollbar_x.pack(side='bottom', fill='x')
        
        # ربط أحداث الجدول
        self.tree.bind('<<TreeviewSelect>>', self.on_select_shipment)
        self.tree.bind('<Double-1>', self.on_double_click)

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(parent, bg=COLORS.get('light', '#E9ECEF'), relief='sunken', bd=1)
        self.status_frame.pack(side='bottom', fill='x', pady=(10, 0))
        
        # معلومات الحالة
        self.status_label = tk.Label(
            self.status_frame,
            text="🟢 جاهز",
            font=('Tahoma', 10),
            bg=COLORS.get('light', '#E9ECEF'),
            fg=COLORS.get('text_primary', '#000000'),
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=2)
        
        # عدد الشحنات
        self.count_label = tk.Label(
            self.status_frame,
            text="عدد الشحنات: 0",
            font=('Tahoma', 10),
            bg=COLORS.get('light', '#E9ECEF'),
            fg=COLORS.get('text_primary', '#000000'),
            anchor='e'
        )
        self.count_label.pack(side='right', padx=10, pady=2)

    def load_data(self):
        """تحميل بيانات الشحنات"""
        try:
            self.status_label.config(text="🔄 جاري تحميل البيانات...")
            
            # استعلام لجلب الشحنات مع أسماء الموردين
            query = """
                SELECT s.*, sup.supplier_name
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                ORDER BY s.created_at DESC
            """
            
            self.shipments_data = self.db_manager.fetch_all(query)
            self.update_table()
            
            self.status_label.config(text="✅ تم تحميل البيانات بنجاح")
            
        except Exception as e:
            self.status_label.config(text="❌ خطأ في تحميل البيانات")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def update_table(self):
        """تحديث جدول الشحنات"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات الجديدة
        for shipment in self.shipments_data:
            values = (
                shipment.get('shipment_number', ''),
                shipment.get('supplier_name', 'غير محدد'),
                shipment.get('status', ''),
                shipment.get('shipment_date', ''),
                shipment.get('departure_port', ''),
                shipment.get('arrival_port', ''),
                f"{shipment.get('total_value', 0):,.2f} {shipment.get('currency', 'USD')}"
            )
            
            self.tree.insert('', 'end', values=values, tags=(shipment.get('id'),))
        
        # تحديث عدد الشحنات
        self.count_label.config(text=f"عدد الشحنات: {len(self.shipments_data)}")

    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        search_text = self.search_var.get().lower()
        
        if not search_text:
            self.update_table()
            return
        
        # تصفية البيانات
        filtered_data = []
        for shipment in self.shipments_data:
            if (search_text in str(shipment.get('shipment_number', '')).lower() or
                search_text in str(shipment.get('supplier_name', '')).lower() or
                search_text in str(shipment.get('status', '')).lower()):
                filtered_data.append(shipment)
        
        # تحديث الجدول بالبيانات المصفاة
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        for shipment in filtered_data:
            values = (
                shipment.get('shipment_number', ''),
                shipment.get('supplier_name', 'غير محدد'),
                shipment.get('status', ''),
                shipment.get('shipment_date', ''),
                shipment.get('departure_port', ''),
                shipment.get('arrival_port', ''),
                f"{shipment.get('total_value', 0):,.2f} {shipment.get('currency', 'USD')}"
            )
            
            self.tree.insert('', 'end', values=values, tags=(shipment.get('id'),))
        
        self.count_label.config(text=f"عدد الشحنات: {len(filtered_data)} من أصل {len(self.shipments_data)}")

    def on_select_shipment(self, event):
        """عند اختيار شحنة"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            shipment_id = item['tags'][0] if item['tags'] else None
            
            # البحث عن الشحنة في البيانات
            self.selected_shipment = None
            for shipment in self.shipments_data:
                if shipment.get('id') == shipment_id:
                    self.selected_shipment = shipment
                    break

    def on_double_click(self, event):
        """عند النقر المزدوج على شحنة"""
        if self.selected_shipment:
            self.edit_shipment()

    # دوال الإجراءات
    def add_shipment(self):
        """إضافة شحنة جديدة"""
        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm()
            form.run()
            # تحديث البيانات بعد الإضافة
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج الشحنة: {str(e)}")

    def edit_shipment(self):
        """تعديل شحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتعديل")
            return
        
        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm(shipment_id=self.selected_shipment.get('id'))
            form.run()
            # تحديث البيانات بعد التعديل
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج التعديل: {str(e)}")

    def delete_shipment(self):
        """حذف شحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للحذف")
            return
        
        shipment_number = self.selected_shipment.get('shipment_number', '')
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الشحنة رقم {shipment_number}؟"):
            try:
                self.db_manager.execute_query(
                    "DELETE FROM shipments WHERE id = ?",
                    (self.selected_shipment.get('id'),)
                )
                
                messagebox.showinfo("تم الحذف", f"تم حذف الشحنة رقم {shipment_number} بنجاح")
                self.refresh_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الشحنة: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()

    def generate_report(self):
        """إنشاء تقرير"""
        messagebox.showinfo("قريباً", "ميزة التقارير ستكون متاحة قريباً")

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.root.destroy()

    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

def show_simple_shipments_window(parent=None):
    """إظهار نافذة إدارة الشحنات البسيطة"""
    window = SimpleShipmentsWindow(parent)
    return window

if __name__ == "__main__":
    # اختبار النافذة
    window = SimpleShipmentsWindow()
    window.run()
