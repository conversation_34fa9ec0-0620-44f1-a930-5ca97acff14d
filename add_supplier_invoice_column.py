#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود رقم فاتورة المورد إلى جدول advanced_shipments
Add supplier_invoice_number column to advanced_shipments table
"""

import sqlite3
import os

def add_supplier_invoice_column():
    """إضافة عمود رقم فاتورة المورد"""
    
    # قواعد البيانات المحتملة
    databases = [
        'database/shipment_system.db',
        'shipments.db'
    ]
    
    for db_path in databases:
        if os.path.exists(db_path):
            print(f"🔄 معالجة قاعدة البيانات: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # التحقق من وجود الجدول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='advanced_shipments'")
                if not cursor.fetchone():
                    print(f"   ⚠️ جدول advanced_shipments غير موجود في {db_path}")
                    conn.close()
                    continue
                
                # التحقق من وجود العمود
                cursor.execute("PRAGMA table_info(advanced_shipments)")
                columns = [col[1] for col in cursor.fetchall()]
                
                if 'supplier_invoice_number' in columns:
                    print(f"   ✅ عمود supplier_invoice_number موجود مسبقاً في {db_path}")
                else:
                    # إضافة العمود
                    cursor.execute('''
                        ALTER TABLE advanced_shipments 
                        ADD COLUMN supplier_invoice_number TEXT
                    ''')
                    print(f"   ✅ تم إضافة عمود supplier_invoice_number إلى {db_path}")
                
                conn.commit()
                conn.close()
                
            except Exception as e:
                print(f"   ❌ خطأ في معالجة {db_path}: {str(e)}")
        else:
            print(f"   ⚠️ قاعدة البيانات غير موجودة: {db_path}")

if __name__ == "__main__":
    print("🚀 بدء إضافة عمود رقم فاتورة المورد...")
    add_supplier_invoice_column()
    print("✅ تم الانتهاء من التحديث!")
