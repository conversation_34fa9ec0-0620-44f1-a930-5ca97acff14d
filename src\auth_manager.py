#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المصادقة والصلاحيات
Authentication and Authorization Manager
"""

import hashlib
import sqlite3
from datetime import datetime, timedelta
import logging
from config.config import USER_ROLES, SECURITY_CONFIG
from database.database_manager import DatabaseManager

class AuthManager:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.current_user = None
        self.session_start_time = None
        self.failed_attempts = {}
        
    def login(self, username, password):
        """تسجيل دخول المستخدم"""
        try:
            # التحقق من محاولات تسجيل الدخول الفاشلة
            if self._is_account_locked(username):
                return {
                    'success': False,
                    'message': 'تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة'
                }
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # البحث عن المستخدم
            cursor.execute('''
                SELECT id, username, password_hash, full_name, email, phone, 
                       role, is_active, last_login
                FROM users 
                WHERE username = ? AND is_active = 1
            ''', (username,))
            
            user = cursor.fetchone()
            
            if user and self.db_manager.verify_password(password, user['password_hash']):
                # تسجيل دخول ناجح
                self.current_user = dict(user)
                self.session_start_time = datetime.now()
                
                # تحديث آخر تسجيل دخول
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (user['id'],))
                conn.commit()
                
                # إزالة محاولات تسجيل الدخول الفاشلة
                if username in self.failed_attempts:
                    del self.failed_attempts[username]
                
                logging.info(f"تسجيل دخول ناجح للمستخدم: {username}")
                
                return {
                    'success': True,
                    'message': 'تم تسجيل الدخول بنجاح',
                    'user': self.current_user
                }
            else:
                # تسجيل دخول فاشل
                self._record_failed_attempt(username)
                logging.warning(f"محاولة تسجيل دخول فاشلة للمستخدم: {username}")
                
                return {
                    'success': False,
                    'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'
                }
                
        except Exception as e:
            logging.error(f"خطأ في تسجيل الدخول: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
        finally:
            if 'conn' in locals():
                conn.close()
    
    def logout(self):
        """تسجيل خروج المستخدم"""
        if self.current_user:
            logging.info(f"تسجيل خروج للمستخدم: {self.current_user['username']}")
        
        self.current_user = None
        self.session_start_time = None
    
    def is_logged_in(self):
        """التحقق من تسجيل دخول المستخدم"""
        if not self.current_user or not self.session_start_time:
            return False
        
        # التحقق من انتهاء الجلسة
        session_duration = datetime.now() - self.session_start_time
        max_duration = timedelta(minutes=SECURITY_CONFIG['session_timeout_minutes'])
        
        if session_duration > max_duration:
            self.logout()
            return False
        
        return True
    
    def has_permission(self, permission):
        """التحقق من صلاحية المستخدم"""
        if not self.is_logged_in():
            return False
        
        user_role = self.current_user.get('role', 'employee')
        role_permissions = USER_ROLES.get(user_role, {}).get('permissions', [])
        
        # المدير له جميع الصلاحيات
        if 'all' in role_permissions:
            return True
        
        return permission in role_permissions
    
    def get_current_user(self):
        """الحصول على المستخدم الحالي"""
        if self.is_logged_in():
            return self.current_user
        return None
    
    def get_user_role_name(self):
        """الحصول على اسم دور المستخدم"""
        if not self.is_logged_in():
            return None
        
        user_role = self.current_user.get('role', 'employee')
        return USER_ROLES.get(user_role, {}).get('name', 'موظف')
    
    def change_password(self, old_password, new_password):
        """تغيير كلمة المرور"""
        if not self.is_logged_in():
            return {
                'success': False,
                'message': 'يجب تسجيل الدخول أولاً'
            }
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # التحقق من كلمة المرور القديمة
            cursor.execute('''
                SELECT password_hash FROM users WHERE id = ?
            ''', (self.current_user['id'],))
            
            result = cursor.fetchone()
            if not result or not self.db_manager.verify_password(old_password, result['password_hash']):
                return {
                    'success': False,
                    'message': 'كلمة المرور القديمة غير صحيحة'
                }
            
            # التحقق من قوة كلمة المرور الجديدة
            if len(new_password) < SECURITY_CONFIG['password_min_length']:
                return {
                    'success': False,
                    'message': f'كلمة المرور يجب أن تكون {SECURITY_CONFIG["password_min_length"]} أحرف على الأقل'
                }
            
            # تحديث كلمة المرور
            new_password_hash = self.db_manager.hash_password(new_password)
            cursor.execute('''
                UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_password_hash, self.current_user['id']))
            
            conn.commit()
            
            logging.info(f"تم تغيير كلمة المرور للمستخدم: {self.current_user['username']}")
            
            return {
                'success': True,
                'message': 'تم تغيير كلمة المرور بنجاح'
            }
            
        except Exception as e:
            logging.error(f"خطأ في تغيير كلمة المرور: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
        finally:
            if 'conn' in locals():
                conn.close()
    
    def create_user(self, username, password, full_name, email=None, phone=None, role='employee'):
        """إنشاء مستخدم جديد"""
        if not self.has_permission('manage_users'):
            return {
                'success': False,
                'message': 'ليس لديك صلاحية لإنشاء المستخدمين'
            }
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # التحقق من عدم وجود المستخدم
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            if cursor.fetchone():
                return {
                    'success': False,
                    'message': 'اسم المستخدم موجود بالفعل'
                }
            
            # التحقق من قوة كلمة المرور
            if len(password) < SECURITY_CONFIG['password_min_length']:
                return {
                    'success': False,
                    'message': f'كلمة المرور يجب أن تكون {SECURITY_CONFIG["password_min_length"]} أحرف على الأقل'
                }
            
            # إنشاء المستخدم
            password_hash = self.db_manager.hash_password(password)
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, email, phone, role)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, full_name, email, phone, role))
            
            conn.commit()
            
            logging.info(f"تم إنشاء مستخدم جديد: {username}")
            
            return {
                'success': True,
                'message': 'تم إنشاء المستخدم بنجاح'
            }
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء المستخدم: {e}")
            return {
                'success': False,
                'message': 'حدث خطأ في النظام'
            }
        finally:
            if 'conn' in locals():
                conn.close()
    
    def _is_account_locked(self, username):
        """التحقق من قفل الحساب"""
        if username not in self.failed_attempts:
            return False
        
        attempts = self.failed_attempts[username]
        if len(attempts) < SECURITY_CONFIG['max_login_attempts']:
            return False
        
        # التحقق من الوقت المنقضي منذ آخر محاولة
        last_attempt = max(attempts)
        time_passed = datetime.now() - last_attempt
        
        # قفل الحساب لمدة 15 دقيقة
        if time_passed < timedelta(minutes=15):
            return True
        
        # إزالة المحاولات القديمة
        del self.failed_attempts[username]
        return False
    
    def _record_failed_attempt(self, username):
        """تسجيل محاولة تسجيل دخول فاشلة"""
        if username not in self.failed_attempts:
            self.failed_attempts[username] = []
        
        self.failed_attempts[username].append(datetime.now())
        
        # الاحتفاظ بآخر المحاولات فقط
        max_attempts = SECURITY_CONFIG['max_login_attempts']
        if len(self.failed_attempts[username]) > max_attempts:
            self.failed_attempts[username] = self.failed_attempts[username][-max_attempts:]

# إنشاء مثيل من مدير المصادقة
auth_manager = AuthManager()
