#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تعيين قاعدة البيانات
Reset Database
"""

import os
import sys

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    print("🔄 إعادة تعيين قاعدة البيانات...")
    
    # مسار قاعدة البيانات
    db_path = "database/shipment_system.db"
    
    try:
        # حذف قاعدة البيانات القديمة إذا كانت موجودة
        if os.path.exists(db_path):
            os.remove(db_path)
            print("✅ تم حذف قاعدة البيانات القديمة")
        
        # إعادة إنشاء قاعدة البيانات
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        print("✅ تم إنشاء قاعدة البيانات الجديدة")
        print("✅ تم إنشاء الجداول")
        print("✅ تم إنشاء المستخدم الافتراضي (admin/admin123)")
        print("✅ تم إنشاء البيانات التجريبية")
        
        print("\n🎉 تم إعادة تعيين قاعدة البيانات بنجاح!")
        print("\n📋 بيانات الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔒 كلمة المرور: admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    reset_database()
