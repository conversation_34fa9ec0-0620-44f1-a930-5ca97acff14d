# 🔍 البحث F9 في حقل المورد - القسم الأساسي

## 📋 نظرة عامة

تم تفعيل **وظيفة البحث المتقدم F9** في حقل "المورد" داخل القسم الأساسي من نموذج الشحنة الجديدة، مما يتيح للمستخدم البحث في الموردين المحفوظين مسبقاً في قاعدة البيانات واختيار المورد المطلوب مع إمكانية ملء البيانات تلقائياً.

## ✨ الميزات الرئيسية

### 🔍 **البحث في الموردين المحفوظين**

#### **مصدر البيانات:**
- **قاعدة البيانات**: البحث في جدول `shipments` للحصول على الموردين الفريدين
- **موردين فريدين**: عرض الموردين الفريدين فقط (DISTINCT)
- **بيانات شاملة**: اسم المورد، معلومات الاتصال، البلد، التقييم
- **ترتيب ذكي**: ترتيب حسب اسم المورد

#### **استعلام قاعدة البيانات:**
```sql
SELECT DISTINCT supplier_id as name, 
       '' as contact_info,
       '' as country,
       '⭐⭐⭐⭐⭐' as rating
FROM shipments 
WHERE supplier_id IS NOT NULL AND supplier_id != ''
ORDER BY supplier_id
```

#### **موردين افتراضيين:**
إذا لم توجد بيانات في قاعدة البيانات، يتم عرض موردين افتراضيين:
1. **شركة التجارة العالمية** - السعودية ⭐⭐⭐⭐⭐
2. **مؤسسة الشحن السريع** - الإمارات ⭐⭐⭐⭐
3. **شركة اللوجستيات المتقدمة** - مصر ⭐⭐⭐⭐⭐

### 🎯 **واجهة البحث المتقدمة**

#### **نافذة البحث (800x600):**
- **عنوان واضح**: "🔍 البحث في الموردين المحفوظين"
- **وصف مفيد**: "ابحث في الموردين المحفوظين مسبقاً واختر المورد المطلوب"
- **تصميم RTL**: محاذاة يمين طبيعية للعربية
- **نافذة modal**: تركيز حصري على البحث

#### **حقول البحث المتعددة:**

##### **1. البحث العام:**
- **بحث موحد**: في اسم المورد ومعلومات الاتصال معاً
- **بحث فوري**: النتائج تظهر أثناء الكتابة (300ms تأخير)
- **نص توضيحي**: "ابحث في اسم المورد أو معلومات الاتصال..."

##### **2. البحث المتقدم (3 حقول):**
- **🏢 اسم المورد**: البحث في اسم الشركة أو المؤسسة
- **📞 معلومات الاتصال**: البحث في الهاتف، الإيميل، العنوان
- **🌍 البلد**: البحث في بلد المورد

#### **منطق البحث الذكي:**
- **بحث AND**: جميع الحقول المملوءة يجب أن تتطابق
- **بحث غير حساس**: لحالة الأحرف (case-insensitive)
- **بحث جزئي**: يمكن البحث بجزء من النص

### 📊 **جدول النتائج المتقدم**

#### **4 أعمدة شاملة:**
1. **🏢 اسم المورد**: اسم الشركة أو المؤسسة
2. **📞 معلومات الاتصال**: هاتف، إيميل، عنوان
3. **🌍 البلد**: بلد المورد
4. **⭐ التقييم**: تقييم المورد بالنجوم

#### **مميزات الجدول:**
- **أشرطة تمرير**: عمودية وأفقية للتنقل السهل
- **صفوف متناوبة**: ألوان متناوبة للوضوح
- **تحديد مفرد**: اختيار مورد واحد فقط
- **نقر مزدوج**: للاختيار السريع

### 📈 **عداد النتائج الديناميكي**

#### **عرض ذكي:**
- **عند عدم البحث**: "📊 إجمالي الموردين: X"
- **عند البحث**: "📊 النتائج: Y من X"
- **تحديث فوري**: مع كل تغيير في البحث

#### **مثال:**
```
قبل البحث: "📊 إجمالي الموردين: 25"
بعد البحث: "📊 النتائج: 3 من 25"
```

### 🔄 **الملء التلقائي للبيانات**

#### **عند اختيار مورد:**
1. **اسم المورد**: يتم ملؤه في القائمة المنسدلة
2. **معلومات الاتصال**: تظهر مع الاسم إذا كانت متوفرة
3. **تنسيق العرض**: "اسم المورد - معلومات الاتصال"

#### **التركيز التلقائي:**
- بعد الملء، ينتقل التركيز لحقل "تاريخ الشحن"
- يوفر تدفق عمل سلس للمستخدم

## 🛠️ **التقنيات المستخدمة**

### 🎨 **نافذة حوار متخصصة**

#### **SupplierSearchDialog Class:**
```python
class SupplierSearchDialog:
    """نافذة حوار البحث في الموردين"""
    - اتصال مباشر بقاعدة البيانات
    - بحث متعدد المعايير
    - ملء تلقائي للبيانات
    - تصميم RTL متقدم
```

#### **مكونات الواجهة:**
- **منطقة البحث**: حقل بحث عام + 3 حقول متقدمة
- **جدول النتائج**: عرض الموردين مع تمرير
- **شريط التحكم**: أزرار الاختيار والإغلاق
- **شريط المساعدة**: نصائح الاستخدام

### 🔗 **التكامل مع نموذج الشحنة**

#### **ربط F9:**
```python
# ربط F9 بحقل المورد
self.supplier_combo.bind('<F9>', self.show_supplier_search)
self.supplier_combo.bind('<KeyPress-F9>', self.show_supplier_search)
```

#### **وظيفة البحث:**
```python
def show_supplier_search(self, event=None):
    """إظهار نافذة البحث عند F9"""
    # 1. التحقق من القسم الحالي
    # 2. إنشاء نافذة البحث
    # 3. انتظار النتيجة
    # 4. ملء البيانات تلقائياً
    # 5. التركيز على الحقل التالي
```

### 🗄️ **استعلام قاعدة البيانات المحسن**

#### **استعلام الموردين الفريدين:**
```python
def load_suppliers_from_database(self):
    """تحميل الموردين من قاعدة البيانات"""
    query = """
        SELECT DISTINCT supplier_id as name, 
               '' as contact_info,
               '' as country,
               '⭐⭐⭐⭐⭐' as rating
        FROM shipments 
        WHERE supplier_id IS NOT NULL AND supplier_id != ''
        ORDER BY supplier_id
    """
```

#### **مميزات الاستعلام:**
- **DISTINCT**: لتجنب التكرار
- **WHERE**: لتصفية الموردين الفارغين
- **ORDER BY**: للترتيب المنطقي

## ⌨️ **اختصارات لوحة المفاتيح**

### **في نموذج الشحنة (القسم الأساسي):**
- **F9**: فتح نافذة البحث في الموردين (في حقل المورد)
- **Tab**: التنقل للحقل التالي (تاريخ الشحن)
- **Ctrl+S**: حفظ الشحنة
- **Esc**: إغلاق النموذج

### **في نافذة البحث:**
- **Enter**: اختيار المورد المحدد
- **Esc**: إغلاق نافذة البحث
- **F9**: إغلاق نافذة البحث
- **Double-Click**: اختيار سريع للمورد

### **التنقل:**
- **Tab**: التنقل بين حقول البحث
- **↑/↓**: التنقل في نتائج الجدول
- **Ctrl+A**: تحديد الكل في حقل البحث

## 🚀 **كيفية الاستخدام**

### **1. فتح نموذج الشحنة:**
```
1. انقر "شحنة جديدة" من القائمة الرئيسية
2. ستفتح نافذة الشحنة بملء الشاشة
3. ستكون في القسم الأساسي تلقائياً
```

### **2. تفعيل البحث F9:**
```
طرق تفعيل البحث:
✅ انقر على حقل "المورد" ثم اضغط F9
✅ اضغط F9 أثناء التركيز على حقل المورد
✅ انقر على التلميح "(F9 للبحث)" بجانب المورد
```

### **3. البحث في الموردين:**
```
1. استخدم البحث العام:
   - اكتب اسم المورد أو جزء منه
   - أو اكتب معلومات الاتصال

2. أو استخدم البحث المتقدم:
   - 🏢 اسم المورد: "شركة التجارة"
   - 📞 معلومات الاتصال: "966501234567"
   - 🌍 البلد: "السعودية"

3. النتائج تظهر فورياً أثناء الكتابة
4. استخدم "🗑️ مسح البحث" لمسح جميع الحقول
```

### **4. اختيار المورد:**
```
طرق الاختيار:
✅ انقر على المورد ثم "✅ اختيار المورد"
✅ انقر مرتين على المورد
✅ انقر على المورد ثم اضغط Enter
```

### **5. الملء التلقائي:**
```
بعد الاختيار:
1. سيتم ملء حقل المورد تلقائياً
2. سيظهر اسم المورد مع معلومات الاتصال
3. سينتقل التركيز لحقل "تاريخ الشحن"
4. أكمل باقي بيانات الشحنة
```

## 🎯 **حالات الاستخدام**

### **🔍 سيناريوهات الاستخدام الشائعة:**

#### **1. اختيار مورد معتاد:**
```
المشكلة: تريد اختيار مورد تتعامل معه بانتظام
الحل: 
1. اضغط F9 في حقل المورد
2. ابحث عن اسم المورد
3. اختر المورد المطلوب
4. ستمتلئ البيانات تلقائياً
```

#### **2. البحث بمعلومات الاتصال:**
```
المشكلة: تتذكر رقم هاتف المورد وليس الاسم
الحل:
1. اضغط F9
2. اكتب رقم الهاتف في البحث العام
3. أو استخدم حقل "معلومات الاتصال"
4. اختر المورد المطلوب
```

#### **3. البحث بالبلد:**
```
المشكلة: تريد مورد من بلد معين
الحل:
1. اضغط F9
2. اكتب اسم البلد في حقل "البلد"
3. ستظهر جميع الموردين من هذا البلد
4. اختر المورد المناسب
```

#### **4. البحث المتقدم المركب:**
```
المثال: تريد مورد من السعودية واسمه يحتوي على "تجارة"
الحل:
1. اضغط F9
2. اكتب "تجارة" في حقل "اسم المورد"
3. اكتب "السعودية" في حقل "البلد"
4. ستظهر النتائج المطابقة للمعيارين
```

### **⚡ نصائح للاستخدام الأمثل:**

#### **1. البحث السريع:**
- اكتب أول حروف من اسم المورد
- أو اكتب جزء من رقم الهاتف
- النتائج ستظهر فورياً

#### **2. الاستفادة من البحث المتقدم:**
- استخدم حقول متعددة للبحث الدقيق
- امزج بين الاسم والبلد للنتائج المحددة
- استخدم معلومات الاتصال للبحث الفريد

#### **3. إدارة الموردين:**
- احفظ بيانات الموردين بانتظام
- استخدم أسماء واضحة ومميزة
- أضف معلومات اتصال كاملة

## 🎉 **النتيجة النهائية**

**✅ تم تفعيل البحث F9 في حقل المورد بنجاح!**

### **🌟 الإنجازات المحققة:**
- 🔍 **بحث متقدم متعدد المعايير** في الموردين المحفوظين مسبقاً
- 🗄️ **اتصال مباشر بقاعدة البيانات** للموردين الفريدين
- ⚡ **بحث فوري ذكي** مع تأخير 300ms لتحسين الأداء
- 📊 **جدول نتائج متقدم** مع 4 أعمدة شاملة
- 🔄 **ملء تلقائي ذكي** لحقل المورد
- ⌨️ **اختصارات شاملة** للاستخدام السريع والفعال
- 🎯 **تركيز تلقائي** على الحقل التالي لتدفق عمل سلس
- 💡 **تلميح واضح** "(F9 للبحث)" بجانب حقل المورد

### **📊 الإحصائيات:**
- **أعمدة الجدول**: 4 أعمدة شاملة
- **حقول البحث**: 4 حقول (عام + 3 متقدمة)
- **الاختصارات**: 6 اختصارات رئيسية
- **وقت الاستجابة**: 300ms للبحث الفوري
- **حجم النافذة**: 800x600 بكسل
- **موردين افتراضيين**: 3 موردين نموذجيين

### **🔧 التحديثات المطبقة:**
1. **تحديث حقل المورد**: إضافة تلميح "(F9 للبحث)"
2. **ربط F9**: في حقل المورد للبحث المباشر
3. **وظيفة البحث**: `show_supplier_search()` في نموذج الشحنة
4. **نافذة البحث**: `SupplierSearchDialog` كاملة ومتقدمة
5. **تحديث المساعدة**: إضافة F9 للموردين في نص المساعدة

**وظيفة البحث F9 في حقل المورد جاهزة للاستخدام الإنتاجي مع تجربة مستخدم متقدمة ومتكاملة!** 🔍✨📊🎯🏢
