#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    """فحص قاعدة البيانات"""
    
    print("فحص قاعدة البيانات...")
    
    # التحقق من وجود الملف
    if os.path.exists('shipments.db'):
        print("✅ ملف قاعدة البيانات موجود")
    else:
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect('shipments.db')
        cursor = conn.cursor()
        
        # التحقق من الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"الجداول الموجودة: {tables}")
        
        # التحقق من جدول advanced_shipments
        if ('advanced_shipments',) in tables:
            print("✅ جدول advanced_shipments موجود")
            
            # عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM advanced_shipments")
            count = cursor.fetchone()[0]
            print(f"عدد الشحنات: {count}")
            
            # عرض أول 3 سجلات
            cursor.execute("SELECT shipment_number, customer_name, status FROM advanced_shipments LIMIT 3")
            rows = cursor.fetchall()
            print("أول 3 شحنات:")
            for row in rows:
                print(f"  - {row[0]}: {row[1]} ({row[2]})")
                
        else:
            print("❌ جدول advanced_shipments غير موجود")
            
        conn.close()
        
    except Exception as e:
        print(f"خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    check_database()
