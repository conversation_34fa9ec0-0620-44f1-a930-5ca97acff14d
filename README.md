# نظام متابعة شحنات الموردين
## Supplier Shipment Tracking System

نظام متكامل لإدارة ومتابعة شحنات الموردين مطور بلغة Python مع دعم كامل للغة العربية ونظام Windows.

## المميزات الرئيسية

### 1. إدارة المخزون والأصناف
- إضافة وتعديل وحذف الأصناف
- تصنيف الأصناف حسب الفئات
- إدارة المخازن والتحويلات المخزنية
- تتبع مستويات المخزون والحد الأدنى

### 2. إدارة الموردين
- قاعدة بيانات شاملة للموردين
- معلومات الاتصال والعناوين
- شروط الدفع والحدود الائتمانية
- تتبع حالة الموردين

### 3. نظام متابعة الشحنات
- إدخال تفاصيل الشحنات
- تتبع حالة الشحنات في الوقت الفعلي
- معلومات الموانئ وشركات الشحن
- إدارة الحاويات وبوالص الشحن

### 4. نظام التقارير
- تقارير الشحنات والموردين
- تقارير المخزون والحركات
- تصدير التقارير بصيغة Excel و PDF
- إمكانية التصفية والطباعة

### 5. نظام المستخدمين والصلاحيات
- تسجيل دخول آمن
- صلاحيات متعددة (مدير، محاسب، موظف مبيعات، إلخ)
- تتبع نشاط المستخدمين

### 6. الإعدادات العامة
- إعدادات الشركة والشعار
- إعداد السنة المالية
- إعدادات الضرائب والعملات
- دعم تعدد اللغات

## المتطلبات التقنية

### متطلبات النظام
- نظام التشغيل: Windows 10 أو أحدث
- Python 3.8 أو أحدث
- ذاكرة: 4 GB RAM كحد أدنى
- مساحة القرص: 500 MB

### المكتبات المطلوبة
```
tkinter (مدمجة مع Python)
sqlite3 (مدمجة مع Python)
pandas>=1.5.0
openpyxl>=3.0.0
xlsxwriter>=3.0.0
reportlab>=3.6.0
Pillow>=9.0.0
python-bidi>=0.4.2
arabic-reshaper>=2.1.0
tkcalendar>=1.6.0
matplotlib>=3.5.0
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd ship_mh
```

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

### 4. تسجيل الدخول الافتراضي
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## هيكل المشروع

```
ship_mh/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # ملف التوثيق
├── config/                # ملفات التكوين
│   └── config.py
├── database/              # قاعدة البيانات
│   ├── database_manager.py
│   └── shipment_system.db
├── src/                   # الكود المصدري
│   ├── auth_manager.py    # إدارة المصادقة
│   ├── login_window.py    # نافذة تسجيل الدخول
│   ├── main_window.py     # النافذة الرئيسية
│   ├── suppliers_window.py # إدارة الموردين
│   ├── items_window.py    # إدارة الأصناف
│   └── shipments_window.py # إدارة الشحنات
├── assets/                # الموارد
│   ├── icons/
│   ├── images/
│   └── fonts/
├── reports/               # التقارير المُنتجة
├── backups/              # النسخ الاحتياطية
└── logs/                 # ملفات السجلات
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- `users` - المستخدمين والصلاحيات
- `company_settings` - إعدادات الشركة
- `suppliers` - بيانات الموردين
- `item_categories` - فئات الأصناف
- `items` - الأصناف والمنتجات
- `warehouses` - المخازن
- `shipments` - الشحنات
- `shipment_items` - تفاصيل الشحنات
- `shipment_status_history` - تاريخ حالات الشحنة
- `inventory` - المخزون
- `inventory_movements` - حركات المخزون

## الاستخدام

### إدارة الموردين
1. من القائمة الرئيسية، اختر "الموردين" > "إدارة الموردين"
2. انقر على "إضافة مورد جديد" لإضافة مورد
3. املأ البيانات المطلوبة واحفظ
4. يمكن البحث والتصفية في قائمة الموردين

### إدارة الأصناف
1. اختر "المخزون" > "إدارة الأصناف"
2. انقر على "إضافة صنف جديد"
3. حدد الفئة ووحدة القياس
4. أدخل أسعار التكلفة والبيع

### إدارة الشحنات
1. اختر "الشحنات" > "إدارة الشحنات"
2. انقر على "شحنة جديدة"
3. اختر المورد وأدخل تفاصيل الشحنة
4. تتبع حالة الشحنة من خلال نافذة التتبع

## الأمان والنسخ الاحتياطي

- كلمات المرور مشفرة باستخدام SHA-256
- نسخ احتياطية تلقائية لقاعدة البيانات
- تسجيل جميع العمليات في ملفات السجلات
- انتهاء صلاحية الجلسات تلقائياً

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567

## الترخيص

هذا البرنامج مطور خصيصاً لإدارة شحنات الموردين. جميع الحقوق محفوظة.

## إصدارات المستقبل

### الإصدار 1.1 (قريباً)
- تكامل مع APIs شركات الشحن
- تتبع GPS للشحنات
- تطبيق جوال مصاحب

### الإصدار 1.2
- تكامل مع أنظمة ERP
- تقارير متقدمة مع الرسوم البيانية
- إشعارات تلقائية

## المطورون

تم تطوير هذا النظام بواسطة فريق متخصص في أنظمة إدارة الشحنات والمخازن.

---

**ملاحظة**: هذا النظام مصمم خصيصاً للشركات التي تتعامل مع شحنات الموردين وتحتاج إلى نظام متكامل لإدارة ومتابعة هذه الشحنات بكفاءة عالية.
