#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def read_excel_file():
    try:
        # Try to read the Excel file
        filename = "‫‫shipment_schedule.xlsx"
        if os.path.exists(filename):
            print(f"File exists: {filename}")
            
            # Try different engines
            engines = ['openpyxl', 'xlrd']
            
            for engine in engines:
                try:
                    print(f"Trying engine: {engine}")
                    df = pd.read_excel(filename, engine=engine)
                    print("Success!")
                    print(f"Columns: {df.columns.tolist()}")
                    print(f"Shape: {df.shape}")
                    print("First few rows:")
                    print(df.head())
                    return df
                except Exception as e:
                    print(f"Engine {engine} failed: {e}")
                    continue
            
            print("All engines failed. Trying to read as CSV...")
            # Try to convert to CSV first
            
        else:
            print(f"File not found: {filename}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    read_excel_file()
