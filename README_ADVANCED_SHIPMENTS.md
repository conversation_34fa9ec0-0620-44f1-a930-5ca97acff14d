# نظام إدارة الشحنات المتقدم
## Advanced Shipments Management System

### نظرة عامة
نظام إدارة الشحنات المتقدم هو تطبيق شامل لإدارة ومتابعة الشحنات التجارية مع دعم كامل للغة العربية وتخطيط RTL.

### الميزات الرئيسية

#### 🚢 إدارة الشحنات
- **إضافة شحنات جديدة**: نموذج شامل لإدخال تفاصيل الشحنة
- **تعديل الشحنات**: تحديث معلومات الشحنات الموجودة
- **حذف الشحنات**: إزالة الشحنات مع تأكيد الأمان
- **عرض التفاصيل**: نافذة مفصلة لعرض جميع معلومات الشحنة

#### 🔍 البحث والتصفية
- **البحث السريع**: بحث فوري في جميع حقول الشحنات
- **تصفية بالحالة**: فلترة الشحنات حسب الحالة (قيد التحضير، تم الشحن، في الطريق، وصل، تم التسليم)
- **تصفية بالأولوية**: فلترة حسب الأولوية (عادي، مهم، عاجل، حرج)
- **تصفية بالتاريخ**: فلترة حسب فترات زمنية محددة

#### 📊 عرض البيانات
- **جدول متقدم**: عرض الشحنات في جدول قابل للترتيب
- **ألوان تمييزية**: ألوان مختلفة حسب الحالة والأولوية
- **ترتيب الأعمدة**: ترتيب البيانات بالنقر على رؤوس الأعمدة
- **تحديد متعدد**: إمكانية تحديد عدة شحنات

#### 🎨 واجهة المستخدم المتقدمة
- **دعم RTL كامل**: تخطيط صحيح للغة العربية
- **تصميم عصري**: واجهة حديثة مع ألوان متناسقة
- **مكونات متقدمة**: أزرار وحقول إدخال مخصصة
- **ظلال وتأثيرات**: تأثيرات بصرية جذابة

#### ⌨️ اختصارات لوحة المفاتيح
- **Ctrl+N**: شحنة جديدة
- **Ctrl+E**: تعديل الشحنة المحددة
- **Ctrl+F**: التركيز على حقل البحث
- **F5**: تحديث البيانات
- **Delete**: حذف الشحنة المحددة

### الملفات الرئيسية

#### `src/advanced_shipments_manager.py`
الملف الرئيسي لنظام إدارة الشحنات يحتوي على:
- فئة `AdvancedShipmentsManager`: الفئة الرئيسية للنظام
- إدارة قاعدة البيانات
- واجهة المستخدم الرئيسية
- وظائف البحث والتصفية

#### `src/advanced_rtl_components.py`
مكونات واجهة المستخدم المتقدمة:
- `AdvancedRTLFrame`: إطارات RTL متقدمة
- `AdvancedRTLLabel`: تسميات RTL
- `AdvancedRTLButton`: أزرار RTL متقدمة
- `AdvancedRTLEntry`: حقول إدخال RTL
- `AdvancedRTLCombobox`: قوائم منسدلة RTL
- `AdvancedRTLText`: مناطق نص RTL
- `AdvancedRTLTreeview`: جداول RTL متقدمة

#### `src/advanced_arabic_styles.py`
أنماط وألوان النظام:
- `ADVANCED_COLORS`: مجموعة ألوان متقدمة
- `ADVANCED_FONTS`: خطوط عربية محسنة
- `ADVANCED_BUTTON_STYLES`: أنماط الأزرار
- `ADVANCED_INPUT_STYLES`: أنماط حقول الإدخال

### قاعدة البيانات

#### جدول `advanced_shipments`
يحتوي على الحقول التالية:
- **معلومات أساسية**: رقم الشحنة، العميل، المورد
- **الموانئ**: ميناء المنشأ، ميناء الوصول
- **التواريخ**: تاريخ الشحن، الوصول المتوقع، الوصول الفعلي
- **الحالة**: حالة الشحنة وأولويتها
- **المعلومات المالية**: القيمة الإجمالية، تكلفة الشحن، التأمين
- **معلومات الحاوية**: نوع الحاوية، رقم الحاوية، رقم الختم
- **الشحن**: خط الشحن، اسم السفينة، رقم الرحلة
- **التتبع**: رقم التتبع، بوليصة الشحن
- **الجمارك**: حالة التخليص الجمركي
- **إضافية**: الوزن، الحجم، عدد الأصناف، الملاحظات

### التشغيل

#### متطلبات النظام
- Python 3.7 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

#### تشغيل النظام
```bash
# تشغيل نظام إدارة الشحنات المتقدم
python test_advanced_shipments.py

# أو من خلال النافذة الرئيسية
python main.py
```

#### إنشاء بيانات تجريبية
```bash
# إنشاء بيانات تجريبية للاختبار
python simple_sample_data.py
```

### الاستخدام

#### إضافة شحنة جديدة
1. انقر على زر "➕ شحنة جديدة"
2. املأ النموذج بتفاصيل الشحنة
3. احفظ الشحنة

#### البحث والتصفية
1. استخدم حقل البحث السريع للبحث في جميع الحقول
2. استخدم المرشحات السريعة للتصفية حسب الحالة والأولوية والتاريخ
3. انقر على رؤوس الأعمدة للترتيب

#### تعديل شحنة
1. حدد الشحنة من الجدول
2. انقر على زر "✏️ تعديل" أو اضغط Ctrl+E
3. عدل المعلومات واحفظ

#### عرض التفاصيل
1. حدد الشحنة من الجدول
2. انقر بالزر الأيمن واختر "📊 عرض التفاصيل"
3. أو اضغط مرتين على الشحنة

### الألوان والحالات

#### ألوان الشحنات في الجدول
- **أحمر فاتح**: الشحنات الملغية
- **أخضر فاتح**: الشحنات المكتملة (تم التسليم)
- **برتقالي فاتح**: الشحنات العاجلة
- **وردي فاتح**: الشحنات الحرجة

#### حالات الشحنة
- **قيد التحضير**: الشحنة في مرحلة التحضير
- **تم الشحن**: تم شحن البضاعة
- **في الطريق**: الشحنة في الطريق
- **وصل**: وصلت الشحنة للميناء
- **تم التسليم**: تم تسليم الشحنة للعميل
- **ملغي**: تم إلغاء الشحنة

#### مستويات الأولوية
- **عادي**: أولوية عادية
- **مهم**: أولوية مهمة
- **عاجل**: أولوية عاجلة
- **حرج**: أولوية حرجة

### التطوير المستقبلي
- تصدير البيانات إلى Excel/PDF
- طباعة تقارير الشحنات
- إشعارات تلقائية
- تتبع GPS للشحنات
- تكامل مع أنظمة الجمارك
- تقارير تحليلية متقدمة

### الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
