#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة المستخدمين
Users Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, USER_ROLES

class UsersWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_users()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المستخدمين - نظام متابعة الشحنات")
        self.window.geometry("900x600")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="إدارة المستخدمين",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار العمليات
        self.add_button = tk.Button(
            buttons_frame,
            text="إضافة مستخدم جديد",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.add_user
        )
        self.add_button.pack(side='right', padx=5)
        
        self.edit_button = tk.Button(
            buttons_frame,
            text="تعديل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['warning'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.edit_user,
            state='disabled'
        )
        self.edit_button.pack(side='right', padx=5)
        
        self.reset_password_button = tk.Button(
            buttons_frame,
            text="إعادة تعيين كلمة المرور",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.reset_password,
            state='disabled'
        )
        self.reset_password_button.pack(side='right', padx=5)
        
        self.toggle_status_button = tk.Button(
            buttons_frame,
            text="تفعيل/إلغاء تفعيل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.toggle_user_status,
            state='disabled'
        )
        self.toggle_status_button.pack(side='right', padx=5)
        
        self.refresh_button = tk.Button(
            buttons_frame,
            text="تحديث",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.load_users
        )
        self.refresh_button.pack(side='right', padx=5)
        
        # جدول المستخدمين
        self.create_users_table(main_frame)
        
    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=COLORS['white'])
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        columns = ('username', 'full_name', 'email', 'phone', 'role', 'is_active', 'last_login', 'created_at')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        headers = {
            'username': 'اسم المستخدم',
            'full_name': 'الاسم الكامل',
            'email': 'البريد الإلكتروني',
            'phone': 'الهاتف',
            'role': 'الدور',
            'is_active': 'الحالة',
            'last_login': 'آخر تسجيل دخول',
            'created_at': 'تاريخ الإنشاء'
        }
        
        for col in columns:
            self.users_tree.heading(col, text=headers[col])
            if col in ['last_login', 'created_at']:
                self.users_tree.column(col, width=120, anchor='center')
            elif col in ['email', 'phone']:
                self.users_tree.column(col, width=150, anchor='center')
            else:
                self.users_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.users_tree.bind('<<TreeviewSelect>>', self.on_user_select)
        self.users_tree.bind('<Double-1>', self.edit_user)
    
    def load_users(self):
        """تحميل المستخدمين من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT username, full_name, email, phone, role, is_active, 
                       last_login, created_at
                FROM users
                ORDER BY created_at DESC
            ''')
            
            users = cursor.fetchall()
            
            for user in users:
                # تنسيق البيانات
                role_name = USER_ROLES.get(user['role'], {}).get('name', user['role'])
                status = "نشط" if user['is_active'] else "غير نشط"
                last_login = user['last_login'] if user['last_login'] else 'لم يسجل دخول'
                created_at = user['created_at'] if user['created_at'] else ''
                
                values = (
                    user['username'],
                    user['full_name'],
                    user['email'] or '',
                    user['phone'] or '',
                    role_name,
                    status,
                    last_login,
                    created_at
                )
                self.users_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")
    
    def on_user_select(self, event=None):
        """عند اختيار مستخدم من الجدول"""
        selection = self.users_tree.selection()
        if selection:
            # التحقق من أن المستخدم الحالي ليس هو المحدد (لا يمكن تعديل نفسه)
            item = self.users_tree.item(selection[0])
            selected_username = item['values'][0]
            current_user = auth_manager.get_current_user()
            
            if selected_username == current_user['username']:
                self.edit_button.config(state='disabled')
                self.reset_password_button.config(state='disabled')
                self.toggle_status_button.config(state='disabled')
            else:
                self.edit_button.config(state='normal')
                self.reset_password_button.config(state='normal')
                self.toggle_status_button.config(state='normal')
        else:
            self.edit_button.config(state='disabled')
            self.reset_password_button.config(state='disabled')
            self.toggle_status_button.config(state='disabled')
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        if not auth_manager.has_permission('manage_users'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المستخدمين")
            return
        
        self.open_user_form()
    
    def edit_user(self, event=None):
        """تعديل مستخدم"""
        if not auth_manager.has_permission('manage_users'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المستخدمين")
            return
        
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        item = self.users_tree.item(selection[0])
        username = item['values'][0]
        
        # التحقق من أن المستخدم لا يحاول تعديل نفسه
        current_user = auth_manager.get_current_user()
        if username == current_user['username']:
            messagebox.showwarning("تحذير", "لا يمكنك تعديل بياناتك الخاصة من هنا")
            return
        
        self.open_user_form(username)
    
    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        if not auth_manager.has_permission('manage_users'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المستخدمين")
            return
        
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لإعادة تعيين كلمة المرور")
            return
        
        item = self.users_tree.item(selection[0])
        username = item['values'][0]
        full_name = item['values'][1]
        
        if messagebox.askyesno("تأكيد", f"هل تريد إعادة تعيين كلمة المرور للمستخدم '{full_name}'؟"):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                
                # إعادة تعيين كلمة المرور إلى "123456"
                new_password = "123456"
                password_hash = self.db_manager.hash_password(new_password)
                
                cursor.execute('''
                    UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE username = ?
                ''', (password_hash, username))
                
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", f"تم إعادة تعيين كلمة المرور بنجاح\nكلمة المرور الجديدة: {new_password}")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في إعادة تعيين كلمة المرور: {str(e)}")
    
    def toggle_user_status(self):
        """تفعيل/إلغاء تفعيل المستخدم"""
        if not auth_manager.has_permission('manage_users'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإدارة المستخدمين")
            return
        
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتغيير حالته")
            return
        
        item = self.users_tree.item(selection[0])
        username = item['values'][0]
        full_name = item['values'][1]
        current_status = item['values'][5]
        
        new_status = "غير نشط" if current_status == "نشط" else "نشط"
        action = "تفعيل" if new_status == "نشط" else "إلغاء تفعيل"
        
        if messagebox.askyesno("تأكيد", f"هل تريد {action} المستخدم '{full_name}'؟"):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                
                is_active = 1 if new_status == "نشط" else 0
                cursor.execute('''
                    UPDATE users SET is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE username = ?
                ''', (is_active, username))
                
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", f"تم {action} المستخدم بنجاح")
                self.load_users()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في تغيير حالة المستخدم: {str(e)}")
    
    def open_user_form(self, username=None):
        """فتح نموذج المستخدم"""
        UserForm(self.window, self.db_manager, username, self.load_users)


class UserForm:
    def __init__(self, parent, db_manager, username=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.username = username
        self.callback = callback
        self.is_edit_mode = username is not None
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        
        if self.is_edit_mode:
            self.load_user_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.window.title(title)
        self.window.geometry("500x600")
        self.window.configure(bg=COLORS['background'])
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر النموذج"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['white'], relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title = "تعديل بيانات المستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        title_label = tk.Label(
            main_frame,
            text=title,
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(20, 30))
        
        # إطار الحقول
        fields_frame = tk.Frame(main_frame, bg=COLORS['white'])
        fields_frame.pack(fill='both', expand=True, padx=40)
        
        # اسم المستخدم
        self.create_field(fields_frame, "اسم المستخدم:", "username_entry", required=True)
        if self.is_edit_mode:
            self.username_entry.config(state='readonly')
        
        # كلمة المرور (للمستخدمين الجدد فقط)
        if not self.is_edit_mode:
            self.create_field(fields_frame, "كلمة المرور:", "password_entry", required=True, password=True)
            self.create_field(fields_frame, "تأكيد كلمة المرور:", "confirm_password_entry", required=True, password=True)
        
        # الاسم الكامل
        self.create_field(fields_frame, "الاسم الكامل:", "full_name_entry", required=True)
        
        # البريد الإلكتروني
        self.create_field(fields_frame, "البريد الإلكتروني:", "email_entry")
        
        # الهاتف
        self.create_field(fields_frame, "الهاتف:", "phone_entry")
        
        # الدور
        role_frame = tk.Frame(fields_frame, bg=COLORS['white'])
        role_frame.pack(fill='x', pady=10)
        
        role_label = tk.Label(
            role_frame,
            text="الدور: *",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['danger']
        )
        role_label.pack(anchor='e')
        
        role_values = [f"{role} - {info['name']}" for role, info in USER_ROLES.items()]
        self.role_combo = ttk.Combobox(
            role_frame,
            values=role_values,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            state='readonly'
        )
        self.role_combo.pack(fill='x', pady=5)
        
        # الحالة
        status_frame = tk.Frame(fields_frame, bg=COLORS['white'])
        status_frame.pack(fill='x', pady=10)
        
        status_label = tk.Label(
            status_frame,
            text="الحالة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        status_label.pack(anchor='e')
        
        self.is_active_var = tk.BooleanVar(value=True)
        self.is_active_check = tk.Checkbutton(
            status_frame,
            text="نشط",
            variable=self.is_active_var,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        self.is_active_check.pack(anchor='e', pady=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame, bg=COLORS['white'])
        buttons_frame.pack(fill='x', pady=20, padx=40)
        
        self.save_button = tk.Button(
            buttons_frame,
            text="حفظ",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.save_user,
            width=15
        )
        self.save_button.pack(side='right', padx=5)
        
        cancel_button = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.window.destroy,
            width=15
        )
        cancel_button.pack(side='right', padx=5)
    
    def create_field(self, parent, label_text, entry_name, required=False, password=False):
        """إنشاء حقل إدخال"""
        field_frame = tk.Frame(parent, bg=COLORS['white'])
        field_frame.pack(fill='x', pady=10)
        
        label_text_with_star = label_text + " *" if required else label_text
        label = tk.Label(
            field_frame,
            text=label_text_with_star,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['danger'] if required else COLORS['dark']
        )
        label.pack(anchor='e')
        
        entry = tk.Entry(
            field_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            relief='solid',
            bd=1,
            show="*" if password else ""
        )
        entry.pack(fill='x', pady=5)
        setattr(self, entry_name, entry)
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM users WHERE username = ?
            ''', (self.username,))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                self.username_entry.insert(0, user['username'])
                self.full_name_entry.insert(0, user['full_name'])
                
                if user['email']:
                    self.email_entry.insert(0, user['email'])
                
                if user['phone']:
                    self.phone_entry.insert(0, user['phone'])
                
                # تحديد الدور
                for i, (role, info) in enumerate(USER_ROLES.items()):
                    if role == user['role']:
                        self.role_combo.current(i)
                        break
                
                self.is_active_var.set(user['is_active'])
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات المستخدم: {str(e)}")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        if not self.username_entry.get().strip():
            messagebox.showerror("خطأ", "اسم المستخدم مطلوب")
            self.username_entry.focus()
            return False
        
        if not self.full_name_entry.get().strip():
            messagebox.showerror("خطأ", "الاسم الكامل مطلوب")
            self.full_name_entry.focus()
            return False
        
        if not self.role_combo.get():
            messagebox.showerror("خطأ", "يرجى اختيار الدور")
            self.role_combo.focus()
            return False
        
        # التحقق من كلمة المرور للمستخدمين الجدد
        if not self.is_edit_mode:
            password = self.password_entry.get()
            confirm_password = self.confirm_password_entry.get()
            
            if not password:
                messagebox.showerror("خطأ", "كلمة المرور مطلوبة")
                self.password_entry.focus()
                return False
            
            if len(password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                self.password_entry.focus()
                return False
            
            if password != confirm_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                self.confirm_password_entry.focus()
                return False
        
        # التحقق من البريد الإلكتروني
        email = self.email_entry.get().strip()
        if email and '@' not in email:
            messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
            self.email_entry.focus()
            return False
        
        return True
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        if not self.validate_data():
            return
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # جمع البيانات
            username = self.username_entry.get().strip()
            full_name = self.full_name_entry.get().strip()
            email = self.email_entry.get().strip() or None
            phone = self.phone_entry.get().strip() or None
            role = self.role_combo.get().split(' - ')[0]
            is_active = self.is_active_var.get()
            
            if self.is_edit_mode:
                # تحديث المستخدم
                cursor.execute('''
                    UPDATE users SET
                        full_name = ?, email = ?, phone = ?, role = ?, is_active = ?
                    WHERE username = ?
                ''', (full_name, email, phone, role, is_active, username))
                
                message = "تم تحديث بيانات المستخدم بنجاح"
            else:
                # التحقق من عدم تكرار اسم المستخدم
                cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
                if cursor.fetchone():
                    messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                    self.username_entry.focus()
                    conn.close()
                    return
                
                # إنشاء مستخدم جديد
                password = self.password_entry.get()
                password_hash = self.db_manager.hash_password(password)
                
                cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, email, phone, role, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (username, password_hash, full_name, email, phone, role, is_active))
                
                message = "تم إضافة المستخدم بنجاح"
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", message)
            
            # تحديث الجدول في النافذة الرئيسية
            if self.callback:
                self.callback()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة المستخدمين للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    users_window = UsersWindow()
    users_window.window.mainloop()
