#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الشحنات المتقدم والشامل مع أنماط CSS متقدمة ودعم RTL كامل
Ultimate Shipments Management System with Advanced CSS Styles and Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, timedelta, date
import json
import uuid
import threading
import time

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES
from database.database_manager import DatabaseManager
from src.advanced_rtl_styles import advanced_style_manager
from src.enhanced_rtl_components import *
from src.auth_manager import auth_manager

class UltimateShipmentsManager:
    """نظام إدارة الشحنات المتقدم والشامل"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.db_manager = DatabaseManager()
        
        # متغيرات البيانات
        self.shipments_data = []
        self.filtered_data = []
        self.selected_shipment = None
        self.current_page = 1
        self.items_per_page = 25
        
        # متغيرات البحث والتصفية المتقدمة
        self.search_vars = {
            'general_search': tk.StringVar(),
            'shipment_number': tk.StringVar(),
            'supplier_filter': tk.StringVar(),
            'status_filter': tk.StringVar(),
            'priority_filter': tk.StringVar(),
            'date_from': tk.StringVar(),
            'date_to': tk.StringVar(),
            'port_from': tk.StringVar(),
            'port_to': tk.StringVar(),
            'shipping_company': tk.StringVar(),
            'container_type': tk.StringVar(),
            'payment_status': tk.StringVar()
        }
        
        # متغيرات العرض
        self.view_mode = tk.StringVar(value='table')  # table, cards, timeline
        self.sort_column = tk.StringVar(value='created_at')
        self.sort_direction = tk.StringVar(value='desc')
        
        # إعداد النافذة
        self.setup_ultimate_window()
        
        # إنشاء الواجهة المتقدمة
        self.create_ultimate_interface()
        
        # تحميل البيانات
        self.load_shipments_data()
        
        # توسيط النافذة
        self.center_window()
        
        # بدء التحديث التلقائي
        self.start_auto_refresh()
        
    def setup_ultimate_window(self):
        """إعداد النافذة المتقدمة"""
        self.root.title("🚢 نظام إدارة الشحنات المتقدم والشامل")
        self.root.geometry("1600x1000")
        self.root.configure(bg=COLORS['background'])
        self.root.state('zoomed')  # ملء الشاشة
        
        # تطبيق أيقونة مخصصة
        try:
            self.root.iconbitmap('assets/ship_icon.ico')
        except:
            pass
        
        # ربط أحداث لوحة المفاتيح المتقدمة
        self.bind_advanced_shortcuts()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def bind_advanced_shortcuts(self):
        """ربط اختصارات لوحة المفاتيح المتقدمة"""
        shortcuts = {
            '<Control-n>': self.new_shipment,
            '<Control-e>': self.edit_shipment,
            '<Control-d>': self.duplicate_shipment,
            '<Control-f>': self.focus_search,
            '<Control-r>': self.refresh_data,
            '<Control-p>': self.print_report,
            '<Control-s>': self.save_current_view,
            '<Control-o>': self.open_shipment_file,
            '<Control-t>': self.track_shipment,
            '<Control-h>': self.show_help,
            '<F1>': self.show_help,
            '<F2>': self.edit_shipment,
            '<F3>': self.find_next,
            '<F4>': self.toggle_view_mode,
            '<F5>': self.refresh_data,
            '<F11>': self.toggle_fullscreen,
            '<Delete>': self.delete_shipment,
            '<Escape>': self.clear_selection,
            '<Control-a>': self.select_all,
            '<Control-z>': self.undo_action,
            '<Control-y>': self.redo_action
        }
        
        for shortcut, command in shortcuts.items():
            self.root.bind(shortcut, lambda e, cmd=command: cmd())
    
    def create_ultimate_interface(self):
        """إنشاء الواجهة المتقدمة والشاملة"""
        # الإطار الرئيسي مع تدرج لوني
        self.main_frame = create_enhanced_rtl_frame(
            self.root,
            style='container_primary'
        )
        self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # شريط العنوان المتقدم
        self.create_advanced_header()
        
        # شريط الأدوات المتقدم
        self.create_advanced_toolbar()
        
        # لوحة البحث والتصفية المتقدمة
        self.create_advanced_search_panel()
        
        # منطقة المحتوى الرئيسية
        self.create_main_content_area()
        
        # شريط الحالة والإحصائيات المتقدم
        self.create_advanced_status_bar()
        
    def create_advanced_header(self):
        """إنشاء شريط العنوان المتقدم"""
        header_frame = create_enhanced_rtl_frame(
            self.main_frame,
            style='container_elevated'
        )
        header_frame.pack(fill='x', pady=(0, 5))
        header_frame.configure(height=80)
        header_frame.pack_propagate(False)
        
        # الجانب الأيمن - العنوان والوصف
        title_section = create_enhanced_rtl_frame(header_frame, style='container_primary')
        title_section.pack(side='right', padx=20, pady=15)
        
        # العنوان الرئيسي مع أيقونة متحركة
        main_title = create_enhanced_rtl_label(
            title_section,
            text="🚢 نظام إدارة الشحنات المتقدم",
            style='text_heading_xl'
        )
        main_title.pack(anchor='e')
        
        # الوصف التفصيلي
        subtitle = create_enhanced_rtl_label(
            title_section,
            text="إدارة شاملة ومتقدمة لجميع عمليات الشحن والتتبع مع تحليلات ذكية",
            style='text_body_lg'
        )
        subtitle.pack(anchor='e', pady=(5, 0))
        
        # الجانب الأيسر - معلومات المستخدم والوقت
        info_section = create_enhanced_rtl_frame(header_frame, style='container_primary')
        info_section.pack(side='left', padx=20, pady=15)
        
        # معلومات المستخدم
        user_info = create_enhanced_rtl_label(
            info_section,
            text=f"👤 المستخدم: {auth_manager.get_current_user().get('username', 'غير محدد')}",
            style='text_body_md'
        )
        user_info.pack(anchor='w')
        
        # الوقت الحالي (يتحدث تلقائياً)
        self.time_label = create_enhanced_rtl_label(
            info_section,
            text="",
            style='text_body_sm'
        )
        self.time_label.pack(anchor='w', pady=(5, 0))
        self.update_time()
        
    def create_advanced_toolbar(self):
        """إنشاء شريط الأدوات المتقدم"""
        self.toolbar = create_enhanced_rtl_toolbar(self.main_frame)
        self.toolbar.pack(fill='x', pady=(0, 5))
        
        # مجموعة أزرار الإجراءات الأساسية
        basic_actions = [
            ("➕ شحنة جديدة", self.new_shipment, "button_success", "📦"),
            ("✏️ تعديل", self.edit_shipment, "button_warning", "✏️"),
            ("📋 نسخ", self.duplicate_shipment, "button_secondary", "📋"),
            ("🗑️ حذف", self.delete_shipment, "button_danger", "🗑️")
        ]
        
        for text, command, style, icon in basic_actions:
            self.toolbar.add_button(f"{icon} {text}", command, style)
        
        # فاصل
        separator1 = tk.Frame(self.toolbar.buttons_frame, width=2, bg=COLORS['border'])
        separator1.pack(side='right', fill='y', padx=10)
        
        # مجموعة أزرار العرض والتصفية
        view_actions = [
            ("🔍 بحث متقدم", self.toggle_advanced_search, "button_primary", "🔍"),
            ("📊 عرض بطاقات", self.toggle_cards_view, "button_secondary", "📊"),
            ("📈 عرض زمني", self.toggle_timeline_view, "button_secondary", "📈"),
            ("🔄 تحديث", self.refresh_data, "button_primary", "🔄")
        ]
        
        for text, command, style, icon in view_actions:
            self.toolbar.add_button(f"{icon} {text}", command, style)
        
        # فاصل
        separator2 = tk.Frame(self.toolbar.buttons_frame, width=2, bg=COLORS['border'])
        separator2.pack(side='right', fill='y', padx=10)
        
        # مجموعة أزرار التقارير والتصدير
        export_actions = [
            ("📊 تقرير شامل", self.generate_comprehensive_report, "button_primary", "📊"),
            ("📤 تصدير Excel", self.export_to_excel, "button_success", "📤"),
            ("🖨️ طباعة", self.print_report, "button_secondary", "🖨️"),
            ("❓ مساعدة", self.show_help, "button_ghost", "❓")
        ]
        
        for text, command, style, icon in export_actions:
            self.toolbar.add_button(f"{icon} {text}", command, style)
    
    def create_advanced_search_panel(self):
        """إنشاء لوحة البحث والتصفية المتقدمة"""
        self.search_panel = create_enhanced_rtl_search_panel(self.main_frame)
        self.search_panel.pack(fill='x', pady=(0, 5))
        
        # الحصول على إطار المحتوى
        content_frame = self.search_panel.winfo_children()[-1]  # آخر child هو content_frame
        
        # الصف الأول - البحث العام والفلاتر الأساسية
        row1 = create_enhanced_rtl_frame(content_frame, style='container_primary')
        row1.pack(fill='x', pady=(0, 10))
        
        # البحث العام
        search_section = create_enhanced_rtl_frame(row1, style='container_primary')
        search_section.pack(side='right', fill='x', expand=True, padx=(0, 15))
        
        search_label = create_enhanced_rtl_label(
            search_section,
            text="🔍 البحث العام:",
            style='text_body_md'
        )
        search_label.pack(anchor='e', pady=(0, 5))
        
        self.general_search_entry = create_enhanced_rtl_entry(
            search_section,
            style='input_search',
            placeholder="ابحث في رقم الشحنة، المورد، الحاوية..."
        )
        self.general_search_entry.pack(fill='x', ipady=8)
        self.general_search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر الحالة
        status_section = create_enhanced_rtl_frame(row1, style='container_primary')
        status_section.pack(side='right', padx=(0, 15))
        
        status_label = create_enhanced_rtl_label(
            status_section,
            text="📊 حالة الشحنة:",
            style='text_body_md'
        )
        status_label.pack(anchor='e', pady=(0, 5))
        
        self.status_combo = create_enhanced_rtl_combobox(
            status_section,
            style='combobox_primary'
        )
        self.status_combo['values'] = ["جميع الحالات"] + list(SHIPMENT_STATUS.keys())
        self.status_combo.set("جميع الحالات")
        self.status_combo.pack(ipady=8)
        self.status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # فلتر المورد
        supplier_section = create_enhanced_rtl_frame(row1, style='container_primary')
        supplier_section.pack(side='right', padx=(0, 15))
        
        supplier_label = create_enhanced_rtl_label(
            supplier_section,
            text="🏢 المورد:",
            style='text_body_md'
        )
        supplier_label.pack(anchor='e', pady=(0, 5))
        
        self.supplier_combo = create_enhanced_rtl_combobox(
            supplier_section,
            style='combobox_primary'
        )
        self.supplier_combo.pack(ipady=8)
        self.supplier_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # فلتر الأولوية
        priority_section = create_enhanced_rtl_frame(row1, style='container_primary')
        priority_section.pack(side='right')
        
        priority_label = create_enhanced_rtl_label(
            priority_section,
            text="⚡ الأولوية:",
            style='text_body_md'
        )
        priority_label.pack(anchor='e', pady=(0, 5))
        
        self.priority_combo = create_enhanced_rtl_combobox(
            priority_section,
            style='combobox_primary'
        )
        self.priority_combo['values'] = ["جميع الأولويات", "عاجل", "عالي", "متوسط", "منخفض", "عادي"]
        self.priority_combo.set("جميع الأولويات")
        self.priority_combo.pack(ipady=8)
        self.priority_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # الصف الثاني - فلاتر التاريخ والموانئ
        row2 = create_enhanced_rtl_frame(content_frame, style='container_primary')
        row2.pack(fill='x', pady=(10, 0))
        
        # تاريخ من
        date_from_section = create_enhanced_rtl_frame(row2, style='container_primary')
        date_from_section.pack(side='right', padx=(0, 15))
        
        date_from_label = create_enhanced_rtl_label(
            date_from_section,
            text="📅 من تاريخ:",
            style='text_body_md'
        )
        date_from_label.pack(anchor='e', pady=(0, 5))
        
        self.date_from_entry = create_enhanced_rtl_entry(
            date_from_section,
            style='input_primary',
            placeholder="YYYY-MM-DD"
        )
        self.date_from_entry.pack(ipady=8)
        self.date_from_entry.bind('<KeyRelease>', self.on_filter_change)
        
        # تاريخ إلى
        date_to_section = create_enhanced_rtl_frame(row2, style='container_primary')
        date_to_section.pack(side='right', padx=(0, 15))
        
        date_to_label = create_enhanced_rtl_label(
            date_to_section,
            text="📅 إلى تاريخ:",
            style='text_body_md'
        )
        date_to_label.pack(anchor='e', pady=(0, 5))
        
        self.date_to_entry = create_enhanced_rtl_entry(
            date_to_section,
            style='input_primary',
            placeholder="YYYY-MM-DD"
        )
        self.date_to_entry.pack(ipady=8)
        self.date_to_entry.bind('<KeyRelease>', self.on_filter_change)
        
        # ميناء المغادرة
        port_from_section = create_enhanced_rtl_frame(row2, style='container_primary')
        port_from_section.pack(side='right', padx=(0, 15))
        
        port_from_label = create_enhanced_rtl_label(
            port_from_section,
            text="🚢 من ميناء:",
            style='text_body_md'
        )
        port_from_label.pack(anchor='e', pady=(0, 5))
        
        self.port_from_combo = create_enhanced_rtl_combobox(
            port_from_section,
            style='combobox_primary'
        )
        self.port_from_combo['values'] = ["جميع الموانئ"] + list(PORTS.keys())
        self.port_from_combo.set("جميع الموانئ")
        self.port_from_combo.pack(ipady=8)
        self.port_from_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # ميناء الوصول
        port_to_section = create_enhanced_rtl_frame(row2, style='container_primary')
        port_to_section.pack(side='right', padx=(0, 15))
        
        port_to_label = create_enhanced_rtl_label(
            port_to_section,
            text="🏁 إلى ميناء:",
            style='text_body_md'
        )
        port_to_label.pack(anchor='e', pady=(0, 5))
        
        self.port_to_combo = create_enhanced_rtl_combobox(
            port_to_section,
            style='combobox_primary'
        )
        self.port_to_combo['values'] = ["جميع الموانئ"] + list(PORTS.keys())
        self.port_to_combo.set("جميع الموانئ")
        self.port_to_combo.pack(ipady=8)
        self.port_to_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # أزرار التحكم
        controls_section = create_enhanced_rtl_frame(row2, style='container_primary')
        controls_section.pack(side='left', padx=(15, 0))
        
        # زر البحث
        search_btn = create_enhanced_rtl_button(
            controls_section,
            text="🔍 بحث",
            style='button_primary',
            command=self.apply_advanced_filters
        )
        search_btn.pack(side='top', pady=(20, 5))
        
        # زر المسح
        clear_btn = create_enhanced_rtl_button(
            controls_section,
            text="🗑️ مسح",
            style='button_outline',
            command=self.clear_all_filters
        )
        clear_btn.pack(side='top')
    
    def create_main_content_area(self):
        """إنشاء منطقة المحتوى الرئيسية"""
        content_frame = create_enhanced_rtl_frame(
            self.main_frame,
            style='container_primary'
        )
        content_frame.pack(fill='both', expand=True, pady=(0, 5))
        
        # إنشاء notebook للتبويبات المتقدمة
        self.create_advanced_tabs(content_frame)
    
    def create_advanced_tabs(self, parent):
        """إنشاء تبويبات متقدمة"""
        # إنشاء notebook مخصص مع تصميم RTL
        self.notebook_frame = create_enhanced_rtl_frame(parent, style='container_elevated')
        self.notebook_frame.pack(fill='both', expand=True)
        
        # شريط التبويبات
        tabs_header = create_enhanced_rtl_frame(
            self.notebook_frame,
            style='container_primary'
        )
        tabs_header.pack(fill='x', pady=(0, 5))
        tabs_header.configure(height=50)
        tabs_header.pack_propagate(False)
        
        # أزرار التبويبات
        self.tab_buttons = {}
        tabs = [
            ("📋 عرض الجدول", "table", self.show_table_view),
            ("📊 عرض البطاقات", "cards", self.show_cards_view),
            ("📈 الخط الزمني", "timeline", self.show_timeline_view),
            ("📊 الإحصائيات", "analytics", self.show_analytics_view),
            ("🗺️ الخريطة", "map", self.show_map_view)
        ]
        
        for text, tab_id, command in tabs:
            btn = create_enhanced_rtl_button(
                tabs_header,
                text=text,
                style='button_ghost',
                command=command
            )
            btn.pack(side='right', padx=5, pady=10)
            self.tab_buttons[tab_id] = btn
        
        # منطقة محتوى التبويبات
        self.tabs_content = create_enhanced_rtl_frame(
            self.notebook_frame,
            style='container_card'
        )
        self.tabs_content.pack(fill='both', expand=True)
        
        # إنشاء محتوى التبويبات
        self.create_table_view()
        self.create_cards_view()
        self.create_timeline_view()
        self.create_analytics_view()
        self.create_map_view()
        
        # عرض التبويب الافتراضي
        self.show_table_view()
    
    def create_table_view(self):
        """إنشاء عرض الجدول المتقدم"""
        self.table_frame = create_enhanced_rtl_frame(
            self.tabs_content,
            style='container_primary'
        )
        
        # إنشاء الجدول المتقدم مع ميزات إضافية
        self.create_enhanced_table()
    
    def create_enhanced_table(self):
        """إنشاء جدول محسن مع ميزات متقدمة"""
        # حاوية الجدول
        table_container = create_enhanced_rtl_frame(
            self.table_frame,
            style='container_card'
        )
        table_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط أدوات الجدول
        table_toolbar = create_enhanced_rtl_frame(
            table_container,
            style='container_elevated'
        )
        table_toolbar.pack(fill='x', pady=(0, 10))
        table_toolbar.configure(height=40)
        table_toolbar.pack_propagate(False)
        
        # أزرار أدوات الجدول
        table_tools = [
            ("📊 ترتيب", self.show_sort_options, "button_ghost"),
            ("🔍 فلترة", self.show_column_filters, "button_ghost"),
            ("📤 تصدير", self.export_table_data, "button_ghost"),
            ("🖨️ طباعة", self.print_table, "button_ghost"),
            ("⚙️ أعمدة", self.customize_columns, "button_ghost")
        ]
        
        for text, command, style in table_tools:
            btn = create_enhanced_rtl_button(
                table_toolbar,
                text=text,
                style=style,
                command=command
            )
            btn.pack(side='right', padx=5, pady=5)
        
        # الجدول المحسن
        self.create_ultimate_treeview(table_container)

    def create_ultimate_treeview(self, parent):
        """إنشاء جدول متقدم مع ميزات شاملة"""
        # إطار الجدول مع أشرطة التمرير
        tree_frame = create_enhanced_rtl_frame(parent, style='container_primary')
        tree_frame.pack(fill='both', expand=True)

        # تعريف الأعمدة المتقدمة
        columns = [
            ('priority', '⚡', 40),
            ('status_icon', '📊', 40),
            ('shipment_number', 'رقم الشحنة', 120),
            ('supplier_name', 'المورد', 150),
            ('status', 'الحالة', 100),
            ('shipment_date', 'تاريخ الشحن', 100),
            ('expected_arrival', 'الوصول المتوقع', 110),
            ('departure_port', 'ميناء المغادرة', 120),
            ('arrival_port', 'ميناء الوصول', 120),
            ('shipping_company', 'شركة الشحن', 120),
            ('container_number', 'رقم الحاوية', 110),
            ('container_type', 'نوع الحاوية', 100),
            ('total_value', 'القيمة الإجمالية', 120),
            ('currency', 'العملة', 60),
            ('payment_status', 'حالة الدفع', 100),
            ('progress', 'التقدم', 80),
            ('days_remaining', 'الأيام المتبقية', 100),
            ('created_by', 'المنشئ', 80),
            ('last_update', 'آخر تحديث', 100)
        ]

        # إنشاء Treeview محسن
        self.shipments_tree = create_enhanced_rtl_treeview(
            tree_frame,
            columns=[col[0] for col in columns],
            show='tree headings',
            height=20,
            style='treeview_primary'
        )

        # تكوين الأعمدة المتقدم
        for col_id, col_name, col_width in columns:
            self.shipments_tree.heading(col_id, text=col_name, anchor='e')
            self.shipments_tree.column(col_id, width=col_width, anchor='e', minwidth=50)

            # إضافة إمكانية الترتيب بالنقر على العنوان
            self.shipments_tree.heading(
                col_id,
                command=lambda c=col_id: self.sort_by_column(c)
            )

        # تكوين العمود الرئيسي (الشجرة)
        self.shipments_tree.column('#0', width=30, minwidth=30, anchor='e')
        self.shipments_tree.heading('#0', text='#', anchor='e')

        # أشرطة التمرير المحسنة
        v_scrollbar = ttk.Scrollbar(
            tree_frame,
            orient='vertical',
            command=self.shipments_tree.yview
        )
        self.shipments_tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(
            tree_frame,
            orient='horizontal',
            command=self.shipments_tree.xview
        )
        self.shipments_tree.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.shipments_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين الشبكة
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # ربط الأحداث المتقدمة
        self.bind_tree_events()

        # إعداد ألوان الصفوف المتقدمة
        self.setup_advanced_row_colors()

    def bind_tree_events(self):
        """ربط أحداث الجدول المتقدمة"""
        # الأحداث الأساسية
        self.shipments_tree.bind('<<TreeviewSelect>>', self.on_shipment_select)
        self.shipments_tree.bind('<Double-1>', self.on_shipment_double_click)
        self.shipments_tree.bind('<Button-3>', self.show_context_menu)

        # أحداث متقدمة
        self.shipments_tree.bind('<Control-c>', self.copy_selected_data)
        self.shipments_tree.bind('<Control-v>', self.paste_data)
        self.shipments_tree.bind('<F2>', lambda e: self.edit_shipment())
        self.shipments_tree.bind('<Delete>', lambda e: self.delete_shipment())

        # أحداث التمرير والتفاعل
        self.shipments_tree.bind('<Motion>', self.on_mouse_motion)
        self.shipments_tree.bind('<Leave>', self.on_mouse_leave)

    def setup_advanced_row_colors(self):
        """إعداد ألوان الصفوف المتقدمة"""
        # ألوان الحالات المتقدمة
        status_colors = advanced_style_manager.get_color_scheme('status_scheme')

        # تطبيق الألوان
        for status, color in status_colors.items():
            self.shipments_tree.tag_configure(f'status_{status}', background=color)

        # ألوان الأولوية
        priority_colors = advanced_style_manager.get_color_scheme('priority_scheme')

        for priority, color in priority_colors.items():
            self.shipments_tree.tag_configure(f'priority_{priority}', foreground=color)

        # ألوان التفاعل
        interaction_colors = advanced_style_manager.get_color_scheme('interaction_scheme')

        self.shipments_tree.tag_configure('hover', background=interaction_colors['hover'])
        self.shipments_tree.tag_configure('selected', background=interaction_colors['selected'])
        self.shipments_tree.tag_configure('odd_row', background='#F9FAFB')
        self.shipments_tree.tag_configure('even_row', background='#FFFFFF')

    def create_cards_view(self):
        """إنشاء عرض البطاقات المتقدم"""
        self.cards_frame = create_enhanced_rtl_frame(
            self.tabs_content,
            style='container_primary'
        )

        # إطار قابل للتمرير للبطاقات
        canvas = tk.Canvas(self.cards_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(self.cards_frame, orient="vertical", command=canvas.yview)
        self.cards_container = create_enhanced_rtl_frame(canvas, style='container_primary')

        self.cards_container.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.cards_container, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط عرض البطاقات
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def create_timeline_view(self):
        """إنشاء عرض الخط الزمني المتقدم"""
        self.timeline_frame = create_enhanced_rtl_frame(
            self.tabs_content,
            style='container_primary'
        )

        # شريط أدوات الخط الزمني
        timeline_toolbar = create_enhanced_rtl_frame(
            self.timeline_frame,
            style='container_elevated'
        )
        timeline_toolbar.pack(fill='x', pady=(0, 10))
        timeline_toolbar.configure(height=50)
        timeline_toolbar.pack_propagate(False)

        # أزرار التحكم في الخط الزمني
        timeline_controls = [
            ("📅 اليوم", self.show_today_timeline, "button_primary"),
            ("📆 هذا الأسبوع", self.show_week_timeline, "button_secondary"),
            ("🗓️ هذا الشهر", self.show_month_timeline, "button_secondary"),
            ("📊 مخصص", self.show_custom_timeline, "button_ghost")
        ]

        for text, command, style in timeline_controls:
            btn = create_enhanced_rtl_button(
                timeline_toolbar,
                text=text,
                style=style,
                command=command
            )
            btn.pack(side='right', padx=5, pady=10)

        # منطقة الخط الزمني
        self.timeline_content = create_enhanced_rtl_frame(
            self.timeline_frame,
            style='container_card'
        )
        self.timeline_content.pack(fill='both', expand=True, padx=10, pady=10)

    def create_analytics_view(self):
        """إنشاء عرض الإحصائيات والتحليلات"""
        self.analytics_frame = create_enhanced_rtl_frame(
            self.tabs_content,
            style='container_primary'
        )

        # شبكة الإحصائيات
        stats_grid = create_enhanced_rtl_frame(
            self.analytics_frame,
            style='container_primary'
        )
        stats_grid.pack(fill='x', padx=10, pady=10)

        # بطاقات الإحصائيات الرئيسية
        self.create_stats_cards(stats_grid)

        # الرسوم البيانية
        charts_frame = create_enhanced_rtl_frame(
            self.analytics_frame,
            style='container_card'
        )
        charts_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.create_charts_section(charts_frame)

    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # الصف الأول من البطاقات
        row1 = create_enhanced_rtl_frame(parent, style='container_primary')
        row1.pack(fill='x', pady=(0, 10))

        stats_data = [
            ("📦 إجمالي الشحنات", "0", COLORS['primary']),
            ("🚢 في الطريق", "0", COLORS['warning']),
            ("✅ تم التسليم", "0", COLORS['success']),
            ("⏰ متأخرة", "0", COLORS['danger'])
        ]

        for title, value, color in stats_data:
            card = self.create_stat_card(row1, title, value, color)
            card.pack(side='right', fill='x', expand=True, padx=5)

        # الصف الثاني من البطاقات
        row2 = create_enhanced_rtl_frame(parent, style='container_primary')
        row2.pack(fill='x')

        financial_stats = [
            ("💰 القيمة الإجمالية", "0 USD", COLORS['success']),
            ("💸 تكاليف الشحن", "0 USD", COLORS['warning']),
            ("📊 متوسط القيمة", "0 USD", COLORS['info']),
            ("📈 نمو هذا الشهر", "0%", COLORS['primary'])
        ]

        for title, value, color in financial_stats:
            card = self.create_stat_card(row2, title, value, color)
            card.pack(side='right', fill='x', expand=True, padx=5)

    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = create_enhanced_rtl_card(parent, style='container_elevated')
        card.configure(highlightbackground=color, highlightthickness=3)

        # أيقونة ملونة
        icon_frame = create_enhanced_rtl_frame(card, style='container_primary')
        icon_frame.pack(fill='x', pady=(10, 5))

        icon_bg = tk.Frame(icon_frame, bg=color, width=50, height=50)
        icon_bg.pack(anchor='center')
        icon_bg.pack_propagate(False)

        # العنوان
        title_label = create_enhanced_rtl_label(
            card,
            text=title,
            style='text_body_md'
        )
        title_label.pack(pady=(5, 0))

        # القيمة
        value_label = create_enhanced_rtl_label(
            card,
            text=value,
            style='text_heading_lg'
        )
        value_label.configure(fg=color)
        value_label.pack(pady=(0, 10))

        return card

    def create_charts_section(self, parent):
        """إنشاء قسم الرسوم البيانية"""
        # عنوان القسم
        charts_title = create_enhanced_rtl_label(
            parent,
            text="📊 الرسوم البيانية والتحليلات",
            style='text_heading_md'
        )
        charts_title.pack(pady=(15, 10))

        # محاكاة الرسوم البيانية
        charts_container = create_enhanced_rtl_frame(parent, style='container_primary')
        charts_container.pack(fill='both', expand=True, padx=15, pady=15)

        # رسم بياني للحالات
        status_chart = create_enhanced_rtl_frame(
            charts_container,
            style='container_card'
        )
        status_chart.pack(side='right', fill='both', expand=True, padx=(0, 10))

        status_title = create_enhanced_rtl_label(
            status_chart,
            text="📊 توزيع الحالات",
            style='text_heading_md'
        )
        status_title.pack(pady=10)

        # محاكاة رسم بياني دائري
        chart_canvas = tk.Canvas(
            status_chart,
            width=300,
            height=200,
            bg=COLORS['surface']
        )
        chart_canvas.pack(pady=10)

        # رسم بياني للاتجاهات
        trends_chart = create_enhanced_rtl_frame(
            charts_container,
            style='container_card'
        )
        trends_chart.pack(side='right', fill='both', expand=True)

        trends_title = create_enhanced_rtl_label(
            trends_chart,
            text="📈 اتجاهات الشحن",
            style='text_heading_md'
        )
        trends_title.pack(pady=10)

        # محاكاة رسم بياني خطي
        trends_canvas = tk.Canvas(
            trends_chart,
            width=300,
            height=200,
            bg=COLORS['surface']
        )
        trends_canvas.pack(pady=10)

    def create_map_view(self):
        """إنشاء عرض الخريطة"""
        self.map_frame = create_enhanced_rtl_frame(
            self.tabs_content,
            style='container_primary'
        )

        # شريط أدوات الخريطة
        map_toolbar = create_enhanced_rtl_frame(
            self.map_frame,
            style='container_elevated'
        )
        map_toolbar.pack(fill='x', pady=(0, 10))
        map_toolbar.configure(height=50)
        map_toolbar.pack_propagate(False)

        # أزرار التحكم في الخريطة
        map_controls = [
            ("🌍 عرض عالمي", self.show_world_map, "button_primary"),
            ("🏠 المنطقة المحلية", self.show_local_map, "button_secondary"),
            ("🚢 تتبع الشحنات", self.track_on_map, "button_success"),
            ("📍 الموانئ", self.show_ports_on_map, "button_ghost")
        ]

        for text, command, style in map_controls:
            btn = create_enhanced_rtl_button(
                map_toolbar,
                text=text,
                style=style,
                command=command
            )
            btn.pack(side='right', padx=5, pady=10)

        # منطقة الخريطة
        map_content = create_enhanced_rtl_frame(
            self.map_frame,
            style='container_card'
        )
        map_content.pack(fill='both', expand=True, padx=10, pady=10)

        # محاكاة الخريطة
        map_placeholder = create_enhanced_rtl_label(
            map_content,
            text="🗺️ خريطة تتبع الشحنات\n(سيتم تطويرها لاحقاً)",
            style='text_heading_lg'
        )
        map_placeholder.pack(expand=True)

    def create_advanced_status_bar(self):
        """إنشاء شريط الحالة المتقدم"""
        status_frame = create_enhanced_rtl_frame(
            self.main_frame,
            style='container_elevated'
        )
        status_frame.pack(fill='x', side='bottom')
        status_frame.configure(height=40)
        status_frame.pack_propagate(False)

        # الجانب الأيمن - معلومات الحالة
        status_info = create_enhanced_rtl_frame(status_frame, style='container_primary')
        status_info.pack(side='right', padx=15, pady=5)

        self.status_label = create_enhanced_rtl_label(
            status_info,
            text="جاهز",
            style='text_body_sm'
        )
        self.status_label.pack(side='right', padx=5)

        self.count_label = create_enhanced_rtl_label(
            status_info,
            text="إجمالي الشحنات: 0",
            style='text_body_sm'
        )
        self.count_label.pack(side='right', padx=10)

        # الوسط - شريط التقدم
        progress_frame = create_enhanced_rtl_frame(status_frame, style='container_primary')
        progress_frame.pack(expand=True, padx=20, pady=8)

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='indeterminate',
            length=200
        )

        # الجانب الأيسر - أزرار التنقل والإعدادات
        nav_frame = create_enhanced_rtl_frame(status_frame, style='container_primary')
        nav_frame.pack(side='left', padx=15, pady=5)

        self.prev_btn = create_enhanced_rtl_button(
            nav_frame,
            text="◀ السابق",
            style='button_ghost',
            command=self.prev_page
        )
        self.prev_btn.pack(side='left', padx=2)

        self.page_label = create_enhanced_rtl_label(
            nav_frame,
            text="صفحة 1 من 1",
            style='text_body_sm'
        )
        self.page_label.pack(side='left', padx=10)

        self.next_btn = create_enhanced_rtl_button(
            nav_frame,
            text="التالي ▶",
            style='button_ghost',
            command=self.next_page
        )
        self.next_btn.pack(side='left', padx=2)

    # وظائف التبويبات
    def show_table_view(self):
        """عرض تبويب الجدول"""
        self.hide_all_tabs()
        self.table_frame.pack(fill='both', expand=True)
        self.highlight_active_tab('table')
        self.view_mode.set('table')

    def show_cards_view(self):
        """عرض تبويب البطاقات"""
        self.hide_all_tabs()
        self.cards_frame.pack(fill='both', expand=True)
        self.highlight_active_tab('cards')
        self.view_mode.set('cards')
        self.update_cards_view()

    def show_timeline_view(self):
        """عرض تبويب الخط الزمني"""
        self.hide_all_tabs()
        self.timeline_frame.pack(fill='both', expand=True)
        self.highlight_active_tab('timeline')
        self.view_mode.set('timeline')
        self.update_timeline_view()

    def show_analytics_view(self):
        """عرض تبويب الإحصائيات"""
        self.hide_all_tabs()
        self.analytics_frame.pack(fill='both', expand=True)
        self.highlight_active_tab('analytics')
        self.view_mode.set('analytics')
        self.update_analytics_view()

    def show_map_view(self):
        """عرض تبويب الخريطة"""
        self.hide_all_tabs()
        self.map_frame.pack(fill='both', expand=True)
        self.highlight_active_tab('map')
        self.view_mode.set('map')
        self.update_map_view()

    def hide_all_tabs(self):
        """إخفاء جميع التبويبات"""
        for frame in [self.table_frame, self.cards_frame, self.timeline_frame,
                     self.analytics_frame, self.map_frame]:
            frame.pack_forget()

    def highlight_active_tab(self, active_tab):
        """تمييز التبويب النشط"""
        for tab_id, button in self.tab_buttons.items():
            if tab_id == active_tab:
                button.configure(style='button_primary')
            else:
                button.configure(style='button_ghost')

    # وظائف البيانات
    def load_shipments_data(self):
        """تحميل بيانات الشحنات"""
        try:
            self.show_loading()

            # تحميل الشحنات من قاعدة البيانات
            query = """
                SELECT s.*, sup.supplier_name
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                ORDER BY s.created_at DESC
            """

            self.shipments_data = self.db_manager.fetch_all(query)
            self.filtered_data = self.shipments_data.copy()

            # تحميل قائمة الموردين للتصفية
            self.load_suppliers_for_filter()

            # تحديث العرض
            self.update_current_view()

            # تحديث شريط الحالة
            self.update_status_bar()

            self.hide_loading()

        except Exception as e:
            self.hide_loading()
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers_for_filter(self):
        """تحميل قائمة الموردين للتصفية"""
        try:
            suppliers = self.db_manager.fetch_all(
                "SELECT id, supplier_name FROM suppliers WHERE is_active = 1"
            )
            supplier_names = ["جميع الموردين"] + [s['supplier_name'] for s in suppliers]
            self.supplier_combo['values'] = supplier_names
            self.supplier_combo.set("جميع الموردين")
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")

    def update_current_view(self):
        """تحديث العرض الحالي"""
        current_view = self.view_mode.get()

        if current_view == 'table':
            self.update_table_view()
        elif current_view == 'cards':
            self.update_cards_view()
        elif current_view == 'timeline':
            self.update_timeline_view()
        elif current_view == 'analytics':
            self.update_analytics_view()
        elif current_view == 'map':
            self.update_map_view()

    def update_table_view(self):
        """تحديث عرض الجدول"""
        # مسح البيانات الحالية
        for item in self.shipments_tree.get_children():
            self.shipments_tree.delete(item)

        # حساب البيانات للصفحة الحالية
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        page_data = self.filtered_data[start_index:end_index]

        # إضافة البيانات للجدول
        for i, shipment in enumerate(page_data):
            self.add_shipment_to_tree(shipment, i)

    def add_shipment_to_tree(self, shipment, index):
        """إضافة شحنة للجدول"""
        # تحديد الأولوية والحالة
        priority = self.calculate_priority(shipment)
        status = shipment.get('status', '')

        # تحديد الأيقونات
        priority_icon = self.get_priority_icon(priority)
        status_icon = self.get_status_icon(status)

        # حساب التقدم والأيام المتبقية
        progress = self.calculate_progress(shipment)
        days_remaining = self.calculate_days_remaining(shipment)

        # تنسيق البيانات
        values = [
            priority_icon,  # priority
            status_icon,    # status_icon
            shipment.get('shipment_number', ''),
            shipment.get('supplier_name', ''),
            SHIPMENT_STATUS.get(status, status),
            self.format_date(shipment.get('shipment_date')),
            self.format_date(shipment.get('expected_arrival_date')),
            PORTS.get(shipment.get('departure_port', ''), shipment.get('departure_port', '')),
            PORTS.get(shipment.get('arrival_port', ''), shipment.get('arrival_port', '')),
            SHIPPING_COMPANIES.get(shipment.get('shipping_company', ''), shipment.get('shipping_company', '')),
            shipment.get('container_number', ''),
            shipment.get('container_type', ''),
            self.format_currency(shipment.get('total_value', 0)),
            shipment.get('currency', 'USD'),
            shipment.get('payment_status', ''),
            f"{progress}%",
            days_remaining,
            shipment.get('created_by', ''),
            self.format_datetime(shipment.get('updated_at'))
        ]

        # تحديد العلامات (tags)
        tags = []
        tags.append(f'status_{status}')
        tags.append(f'priority_{priority}')
        if index % 2 == 0:
            tags.append('even_row')
        else:
            tags.append('odd_row')

        # إدراج العنصر
        item = self.shipments_tree.insert(
            '', 'end',
            text=str(index + 1),
            values=values,
            tags=tags
        )

        # حفظ معرف الشحنة مع العنصر
        self.shipments_tree.set(item, '#0', shipment.get('id', ''))

    def calculate_priority(self, shipment):
        """حساب أولوية الشحنة"""
        # خوارزمية حساب الأولوية بناءً على عوامل متعددة
        priority_score = 0

        # عامل القيمة
        value = float(shipment.get('total_value', 0))
        if value > 100000:
            priority_score += 3
        elif value > 50000:
            priority_score += 2
        elif value > 10000:
            priority_score += 1

        # عامل التأخير
        expected_date = shipment.get('expected_arrival_date')
        if expected_date:
            try:
                expected = datetime.strptime(expected_date, '%Y-%m-%d').date()
                today = date.today()
                if expected < today:
                    priority_score += 4  # متأخرة
                elif (expected - today).days <= 3:
                    priority_score += 2  # قريبة
            except:
                pass

        # عامل الحالة
        status = shipment.get('status', '')
        if status in ['متأخرة', 'في الجمارك']:
            priority_score += 2
        elif status in ['في الطريق', 'وصلت']:
            priority_score += 1

        # تحديد الأولوية النهائية
        if priority_score >= 6:
            return 'urgent'
        elif priority_score >= 4:
            return 'high'
        elif priority_score >= 2:
            return 'medium'
        elif priority_score >= 1:
            return 'low'
        else:
            return 'normal'

    def get_priority_icon(self, priority):
        """الحصول على أيقونة الأولوية"""
        icons = {
            'urgent': '🔴',
            'high': '🟠',
            'medium': '🟡',
            'low': '🟢',
            'normal': '⚪'
        }
        return icons.get(priority, '⚪')

    def get_status_icon(self, status):
        """الحصول على أيقونة الحالة"""
        icons = {
            'في الانتظار': '⏳',
            'مؤكدة': '✅',
            'تم الشحن': '🚢',
            'في الطريق': '🛣️',
            'وصلت': '🏁',
            'في الجمارك': '🏛️',
            'تم التسليم': '📦',
            'ملغية': '❌',
            'متأخرة': '⚠️'
        }
        return icons.get(status, '❓')

    def calculate_progress(self, shipment):
        """حساب نسبة التقدم"""
        status = shipment.get('status', '')

        progress_map = {
            'في الانتظار': 10,
            'مؤكدة': 25,
            'تم الشحن': 40,
            'في الطريق': 60,
            'وصلت': 80,
            'في الجمارك': 85,
            'تم التسليم': 100,
            'ملغية': 0,
            'متأخرة': 50
        }

        return progress_map.get(status, 0)

    def calculate_days_remaining(self, shipment):
        """حساب الأيام المتبقية"""
        expected_date = shipment.get('expected_arrival_date')
        if not expected_date:
            return "غير محدد"

        try:
            expected = datetime.strptime(expected_date, '%Y-%m-%d').date()
            today = date.today()
            diff = (expected - today).days

            if diff < 0:
                return f"متأخرة {abs(diff)} يوم"
            elif diff == 0:
                return "اليوم"
            elif diff == 1:
                return "غداً"
            else:
                return f"{diff} يوم"
        except:
            return "غير صحيح"

    # وظائف التنسيق
    def format_date(self, date_value):
        """تنسيق التاريخ"""
        if not date_value:
            return ""

        if isinstance(date_value, str):
            try:
                date_obj = datetime.strptime(date_value, '%Y-%m-%d')
                return date_obj.strftime('%Y/%m/%d')
            except:
                return date_value
        elif isinstance(date_value, (date, datetime)):
            return date_value.strftime('%Y/%m/%d')

        return str(date_value)

    def format_datetime(self, datetime_value):
        """تنسيق التاريخ والوقت"""
        if not datetime_value:
            return ""

        if isinstance(datetime_value, str):
            try:
                dt_obj = datetime.fromisoformat(datetime_value.replace('Z', '+00:00'))
                return dt_obj.strftime('%Y/%m/%d %H:%M')
            except:
                return datetime_value
        elif isinstance(datetime_value, datetime):
            return datetime_value.strftime('%Y/%m/%d %H:%M')

        return str(datetime_value)

    def format_currency(self, amount):
        """تنسيق العملة"""
        try:
            return f"{float(amount):,.2f}"
        except:
            return "0.00"

    # وظائف البحث والتصفية
    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        if hasattr(self, 'search_timer'):
            self.root.after_cancel(self.search_timer)

        self.search_timer = self.root.after(500, self.apply_advanced_filters)

    def on_filter_change(self, event=None):
        """عند تغيير التصفية"""
        self.apply_advanced_filters()

    def apply_advanced_filters(self):
        """تطبيق المرشحات المتقدمة"""
        try:
            self.show_loading()

            # جمع قيم المرشحات
            filters = {
                'general_search': self.general_search_entry.get().strip().lower(),
                'status': self.status_combo.get(),
                'supplier': self.supplier_combo.get(),
                'priority': self.priority_combo.get(),
                'date_from': self.date_from_entry.get().strip(),
                'date_to': self.date_to_entry.get().strip(),
                'port_from': self.port_from_combo.get(),
                'port_to': self.port_to_combo.get()
            }

            # تطبيق المرشحات
            self.filtered_data = []

            for shipment in self.shipments_data:
                if self.matches_filters(shipment, filters):
                    self.filtered_data.append(shipment)

            # إعادة تعيين الصفحة الحالية
            self.current_page = 1

            # تحديث العرض
            self.update_current_view()
            self.update_status_bar()

            self.hide_loading()

        except Exception as e:
            self.hide_loading()
            messagebox.showerror("خطأ", f"خطأ في تطبيق المرشحات: {str(e)}")

    def matches_filters(self, shipment, filters):
        """التحقق من تطابق الشحنة مع المرشحات"""
        # فلتر البحث العام
        if filters['general_search']:
            searchable_text = f"{shipment.get('shipment_number', '')} {shipment.get('supplier_name', '')} {shipment.get('container_number', '')}".lower()
            if filters['general_search'] not in searchable_text:
                return False

        # فلتر الحالة
        if filters['status'] and filters['status'] != "جميع الحالات":
            if shipment.get('status', '') != filters['status']:
                return False

        # فلتر المورد
        if filters['supplier'] and filters['supplier'] != "جميع الموردين":
            if shipment.get('supplier_name', '') != filters['supplier']:
                return False

        # فلتر الأولوية
        if filters['priority'] and filters['priority'] != "جميع الأولويات":
            shipment_priority = self.calculate_priority(shipment)
            priority_map = {
                'عاجل': 'urgent',
                'عالي': 'high',
                'متوسط': 'medium',
                'منخفض': 'low',
                'عادي': 'normal'
            }
            if priority_map.get(filters['priority']) != shipment_priority:
                return False

        # فلتر التاريخ
        if filters['date_from'] or filters['date_to']:
            shipment_date = shipment.get('shipment_date')
            if shipment_date:
                try:
                    if isinstance(shipment_date, str):
                        ship_date = datetime.strptime(shipment_date, '%Y-%m-%d').date()
                    else:
                        ship_date = shipment_date

                    if filters['date_from']:
                        from_date = datetime.strptime(filters['date_from'], '%Y-%m-%d').date()
                        if ship_date < from_date:
                            return False

                    if filters['date_to']:
                        to_date = datetime.strptime(filters['date_to'], '%Y-%m-%d').date()
                        if ship_date > to_date:
                            return False
                except:
                    return False

        # فلتر الموانئ
        if filters['port_from'] and filters['port_from'] != "جميع الموانئ":
            if shipment.get('departure_port', '') != filters['port_from']:
                return False

        if filters['port_to'] and filters['port_to'] != "جميع الموانئ":
            if shipment.get('arrival_port', '') != filters['port_to']:
                return False

        return True

    def clear_all_filters(self):
        """مسح جميع المرشحات"""
        self.general_search_entry.delete(0, tk.END)
        self.status_combo.set("جميع الحالات")
        self.supplier_combo.set("جميع الموردين")
        self.priority_combo.set("جميع الأولويات")
        self.date_from_entry.delete(0, tk.END)
        self.date_to_entry.delete(0, tk.END)
        self.port_from_combo.set("جميع الموانئ")
        self.port_to_combo.set("جميع الموانئ")

        # إعادة تحميل جميع البيانات
        self.filtered_data = self.shipments_data.copy()
        self.current_page = 1
        self.update_current_view()
        self.update_status_bar()

    # وظائف الأحداث
    def on_shipment_select(self, event):
        """عند اختيار شحنة"""
        selection = self.shipments_tree.selection()
        if selection:
            item = selection[0]
            shipment_id = self.shipments_tree.set(item, '#0')

            # البحث عن الشحنة في البيانات
            for shipment in self.shipments_data:
                if str(shipment.get('id', '')) == str(shipment_id):
                    self.selected_shipment = shipment
                    break

    def on_shipment_double_click(self, event):
        """عند النقر المزدوج على شحنة"""
        if self.selected_shipment:
            self.edit_shipment()

    def on_mouse_motion(self, event):
        """عند تحريك الماوس فوق الجدول"""
        item = self.shipments_tree.identify_row(event.y)
        if item:
            # إضافة تأثير التمرير
            self.shipments_tree.set(item, '#0', '►')

    def on_mouse_leave(self, event):
        """عند مغادرة الماوس للجدول"""
        # إزالة تأثيرات التمرير
        for item in self.shipments_tree.get_children():
            current_text = self.shipments_tree.item(item, 'text')
            if current_text and current_text.isdigit():
                continue  # الاحتفاظ بالأرقام
            self.shipments_tree.set(item, '#0', '')

    def show_context_menu(self, event):
        """إظهار قائمة السياق"""
        # إنشاء قائمة السياق المتقدمة
        context_menu = tk.Menu(self.root, tearoff=0)

        # إضافة العناصر مع الأيقونات
        context_menu.add_command(
            label="✏️ تعديل الشحنة",
            command=self.edit_shipment,
            accelerator="F2"
        )
        context_menu.add_command(
            label="👁️ عرض التفاصيل",
            command=self.view_shipment_details,
            accelerator="Enter"
        )
        context_menu.add_command(
            label="📋 نسخ الشحنة",
            command=self.duplicate_shipment,
            accelerator="Ctrl+D"
        )
        context_menu.add_separator()

        context_menu.add_command(
            label="📊 تتبع الشحنة",
            command=self.track_shipment,
            accelerator="Ctrl+T"
        )
        context_menu.add_command(
            label="📋 نسخ رقم الشحنة",
            command=self.copy_shipment_number,
            accelerator="Ctrl+C"
        )
        context_menu.add_command(
            label="📤 تصدير بيانات الشحنة",
            command=self.export_shipment_data
        )
        context_menu.add_separator()

        context_menu.add_command(
            label="🗑️ حذف الشحنة",
            command=self.delete_shipment,
            accelerator="Delete"
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    # وظائف الإجراءات الأساسية
    def new_shipment(self):
        """إضافة شحنة جديدة"""
        try:
            from src.ultimate_shipment_form import UltimateShipmentForm
            form = UltimateShipmentForm(self.root, mode='add')
            self.root.wait_window(form.root)

            # تحديث البيانات بعد الإضافة
            self.refresh_data()

        except ImportError:
            messagebox.showinfo("قريباً", "نموذج إضافة الشحنة سيتم تطويره قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة شحنة جديدة: {str(e)}")

    def edit_shipment(self):
        """تعديل الشحنة المحددة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتعديل")
            return

        try:
            from src.ultimate_shipment_form import UltimateShipmentForm
            form = UltimateShipmentForm(
                self.root,
                mode='edit',
                shipment_data=self.selected_shipment
            )
            self.root.wait_window(form.root)

            # تحديث البيانات بعد التعديل
            self.refresh_data()

        except ImportError:
            messagebox.showinfo("قريباً", "نموذج تعديل الشحنة سيتم تطويره قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الشحنة: {str(e)}")

    def duplicate_shipment(self):
        """نسخ الشحنة المحددة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للنسخ")
            return

        try:
            from src.ultimate_shipment_form import UltimateShipmentForm
            form = UltimateShipmentForm(
                self.root,
                mode='duplicate',
                shipment_data=self.selected_shipment
            )
            self.root.wait_window(form.root)

            # تحديث البيانات بعد النسخ
            self.refresh_data()

        except ImportError:
            messagebox.showinfo("قريباً", "نموذج نسخ الشحنة سيتم تطويره قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نسخ الشحنة: {str(e)}")

    def delete_shipment(self):
        """حذف الشحنة المحددة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الشحنة رقم {self.selected_shipment.get('shipment_number', '')}؟\n\n"
            "هذا الإجراء لا يمكن التراجع عنه.",
            icon='warning'
        )

        if result:
            try:
                # حذف الشحنة من قاعدة البيانات
                self.db_manager.execute_query(
                    "DELETE FROM shipments WHERE id = ?",
                    (self.selected_shipment['id'],)
                )

                messagebox.showinfo("نجح", "تم حذف الشحنة بنجاح")

                # تحديث البيانات
                self.refresh_data()

                # مسح التحديد
                self.selected_shipment = None

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الشحنة: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.configure(text="جاري التحديث...")
        self.show_loading()

        # تحديث في خيط منفصل لتجنب تجميد الواجهة
        def update_thread():
            try:
                time.sleep(0.5)  # محاكاة وقت التحميل
                self.root.after(0, self.load_shipments_data)
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في التحديث: {str(e)}"))

        threading.Thread(target=update_thread, daemon=True).start()

    # وظائف مساعدة
    def show_loading(self):
        """إظهار مؤشر التحميل"""
        self.progress_bar.pack(expand=True, padx=20, pady=8)
        self.progress_bar.start(10)

    def hide_loading(self):
        """إخفاء مؤشر التحميل"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("📅 %Y/%m/%d - ⏰ %H:%M:%S")
        self.time_label.configure(text=current_time)
        self.root.after(1000, self.update_time)

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        total_items = len(self.filtered_data)
        total_pages = max(1, (total_items + self.items_per_page - 1) // self.items_per_page)

        # تحديث عداد العناصر
        self.count_label.configure(text=f"إجمالي الشحنات: {total_items}")

        # تحديث معلومات الصفحة
        self.page_label.configure(text=f"صفحة {self.current_page} من {total_pages}")

        # تحديث حالة أزرار التنقل
        prev_state = 'normal' if self.current_page > 1 else 'disabled'
        next_state = 'normal' if self.current_page < total_pages else 'disabled'

        # تحديث حالة النظام
        if total_items == 0:
            self.status_label.configure(text="لا توجد شحنات")
        else:
            start_item = (self.current_page - 1) * self.items_per_page + 1
            end_item = min(self.current_page * self.items_per_page, total_items)
            self.status_label.configure(text=f"عرض {start_item}-{end_item} من {total_items}")

    def prev_page(self):
        """الصفحة السابقة"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_current_view()
            self.update_status_bar()

    def next_page(self):
        """الصفحة التالية"""
        total_pages = max(1, (len(self.filtered_data) + self.items_per_page - 1) // self.items_per_page)
        if self.current_page < total_pages:
            self.current_page += 1
            self.update_current_view()
            self.update_status_bar()

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def auto_refresh():
            # تحديث كل 5 دقائق
            self.root.after(300000, auto_refresh)
            if hasattr(self, 'auto_refresh_enabled') and self.auto_refresh_enabled:
                self.refresh_data()

        self.auto_refresh_enabled = True
        auto_refresh()

    # وظائف العرض المتقدمة
    def update_cards_view(self):
        """تحديث عرض البطاقات"""
        # مسح البطاقات الحالية
        for widget in self.cards_container.winfo_children():
            widget.destroy()

        # إنشاء بطاقات للشحنات
        for i, shipment in enumerate(self.filtered_data):
            if i >= self.current_page * self.items_per_page:
                break
            if i < (self.current_page - 1) * self.items_per_page:
                continue

            self.create_shipment_card(shipment)

    def create_shipment_card(self, shipment):
        """إنشاء بطاقة شحنة"""
        card = create_enhanced_rtl_card(
            self.cards_container,
            title=f"🚢 {shipment.get('shipment_number', '')}",
            style='container_elevated'
        )
        card.pack(fill='x', padx=10, pady=5)

        # محتوى البطاقة
        content = create_enhanced_rtl_frame(card, style='container_primary')
        content.pack(fill='x', padx=15, pady=10)

        # معلومات أساسية
        info_text = f"""
🏢 المورد: {shipment.get('supplier_name', 'غير محدد')}
📊 الحالة: {SHIPMENT_STATUS.get(shipment.get('status', ''), shipment.get('status', ''))}
📅 تاريخ الشحن: {self.format_date(shipment.get('shipment_date'))}
🏁 الوصول المتوقع: {self.format_date(shipment.get('expected_arrival_date'))}
💰 القيمة: {self.format_currency(shipment.get('total_value', 0))} {shipment.get('currency', 'USD')}
        """

        info_label = create_enhanced_rtl_label(
            content,
            text=info_text.strip(),
            style='text_body_md'
        )
        info_label.pack(anchor='e')

        # أزرار الإجراءات
        actions_frame = create_enhanced_rtl_frame(content, style='container_primary')
        actions_frame.pack(fill='x', pady=(10, 0))

        edit_btn = create_enhanced_rtl_button(
            actions_frame,
            text="✏️ تعديل",
            style='button_warning',
            command=lambda s=shipment: self.edit_shipment_from_card(s)
        )
        edit_btn.pack(side='left', padx=5)

        track_btn = create_enhanced_rtl_button(
            actions_frame,
            text="📊 تتبع",
            style='button_primary',
            command=lambda s=shipment: self.track_shipment_from_card(s)
        )
        track_btn.pack(side='left', padx=5)

    def update_timeline_view(self):
        """تحديث عرض الخط الزمني"""
        # مسح المحتوى الحالي
        for widget in self.timeline_content.winfo_children():
            widget.destroy()

        # إنشاء خط زمني للشحنات
        timeline_label = create_enhanced_rtl_label(
            self.timeline_content,
            text="📈 الخط الزمني للشحنات (قيد التطوير)",
            style='text_heading_lg'
        )
        timeline_label.pack(expand=True)

    def update_analytics_view(self):
        """تحديث عرض الإحصائيات"""
        # حساب الإحصائيات
        total_shipments = len(self.shipments_data)
        in_transit = len([s for s in self.shipments_data if s.get('status') == 'في الطريق'])
        delivered = len([s for s in self.shipments_data if s.get('status') == 'تم التسليم'])
        delayed = len([s for s in self.shipments_data if s.get('status') == 'متأخرة'])

        # تحديث بطاقات الإحصائيات
        # (سيتم تطوير هذا لاحقاً)

    def update_map_view(self):
        """تحديث عرض الخريطة"""
        # تحديث الخريطة
        # (سيتم تطوير هذا لاحقاً)
        pass

    # وظائف الأدوات المتقدمة
    def sort_by_column(self, column):
        """ترتيب حسب العمود"""
        # تبديل اتجاه الترتيب
        if self.sort_column.get() == column:
            direction = 'asc' if self.sort_direction.get() == 'desc' else 'desc'
        else:
            direction = 'desc'

        self.sort_column.set(column)
        self.sort_direction.set(direction)

        # تطبيق الترتيب
        reverse = (direction == 'desc')

        try:
            self.filtered_data.sort(
                key=lambda x: x.get(column, ''),
                reverse=reverse
            )

            # تحديث العرض
            self.update_current_view()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الترتيب: {str(e)}")

    def focus_search(self):
        """التركيز على حقل البحث"""
        self.general_search_entry.focus()

    def clear_selection(self):
        """مسح التحديد"""
        self.shipments_tree.selection_remove(self.shipments_tree.selection())
        self.selected_shipment = None

    def select_all(self):
        """تحديد الكل"""
        for item in self.shipments_tree.get_children():
            self.shipments_tree.selection_add(item)

    # وظائف التصدير والطباعة
    def export_to_excel(self):
        """تصدير إلى Excel"""
        messagebox.showinfo("قريباً", "تصدير Excel سيتم تطويره قريباً")

    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("قريباً", "الطباعة ستتم تطويرها قريباً")

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        messagebox.showinfo("قريباً", "التقرير الشامل سيتم تطويره قريباً")

    # وظائف أخرى
    def track_shipment(self):
        """تتبع الشحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتتبع")
            return

        messagebox.showinfo("تتبع الشحنة", f"تتبع الشحنة رقم {self.selected_shipment.get('shipment_number', '')} سيتم تطويره قريباً")

    def copy_shipment_number(self):
        """نسخ رقم الشحنة"""
        if self.selected_shipment:
            shipment_number = self.selected_shipment.get('shipment_number', '')
            self.root.clipboard_clear()
            self.root.clipboard_append(shipment_number)
            messagebox.showinfo("تم النسخ", f"تم نسخ رقم الشحنة: {shipment_number}")

    def show_help(self):
        """إظهار المساعدة"""
        help_text = """
🚢 نظام إدارة الشحنات المتقدم - المساعدة

📋 التبويبات المتاحة:
• عرض الجدول: جدول شامل مع 19 عمود
• عرض البطاقات: بطاقات تفاعلية للشحنات
• الخط الزمني: عرض زمني للأحداث
• الإحصائيات: تحليلات وإحصائيات متقدمة
• الخريطة: تتبع الشحنات على الخريطة

🔍 البحث والتصفية:
• البحث العام: ابحث في جميع الحقول
• تصفية الحالة: حسب حالة الشحنة
• تصفية المورد: حسب المورد
• تصفية الأولوية: حسب الأولوية المحسوبة
• تصفية التاريخ: حسب فترة زمنية
• تصفية الموانئ: حسب موانئ المغادرة والوصول

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+N: شحنة جديدة
• Ctrl+E: تعديل الشحنة
• Ctrl+D: نسخ الشحنة
• Ctrl+F: التركيز على البحث
• Ctrl+R: تحديث البيانات
• F1: هذه المساعدة
• F2: تعديل الشحنة المحددة
• F4: تبديل وضع العرض
• F5: تحديث
• Delete: حذف الشحنة المحددة

🎨 الألوان والرموز:
• 🔴 عاجل | 🟠 عالي | 🟡 متوسط | 🟢 منخفض | ⚪ عادي
• ⏳ في الانتظار | ✅ مؤكدة | 🚢 تم الشحن
• 🛣️ في الطريق | 🏁 وصلت | 🏛️ في الجمارك
• 📦 تم التسليم | ❌ ملغية | ⚠️ متأخرة

💡 نصائح:
• انقر على عناوين الأعمدة للترتيب
• انقر بالزر الأيمن لقائمة الخيارات
• استخدم البحث السريع للعثور على الشحنات
• تحديث تلقائي كل 5 دقائق
        """

        messagebox.showinfo("مساعدة - نظام إدارة الشحنات", help_text)

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.auto_refresh_enabled = False
        self.root.destroy()

# وظائف مساعدة إضافية (stubs للتطوير المستقبلي)
    def toggle_advanced_search(self): pass
    def toggle_cards_view(self): pass
    def toggle_timeline_view(self): pass
    def show_sort_options(self): pass
    def show_column_filters(self): pass
    def export_table_data(self): pass
    def print_table(self): pass
    def customize_columns(self): pass
    def show_today_timeline(self): pass
    def show_week_timeline(self): pass
    def show_month_timeline(self): pass
    def show_custom_timeline(self): pass
    def show_world_map(self): pass
    def show_local_map(self): pass
    def track_on_map(self): pass
    def show_ports_on_map(self): pass
    def view_shipment_details(self): pass
    def export_shipment_data(self): pass
    def copy_selected_data(self, event): pass
    def paste_data(self, event): pass
    def find_next(self): pass
    def toggle_view_mode(self): pass
    def toggle_fullscreen(self): pass
    def undo_action(self): pass
    def redo_action(self): pass
    def save_current_view(self): pass
    def open_shipment_file(self): pass
    def edit_shipment_from_card(self, shipment): pass
    def track_shipment_from_card(self, shipment): pass

def show_ultimate_shipments_manager(parent=None):
    """إظهار نظام إدارة الشحنات المتقدم"""
    manager = UltimateShipmentsManager(parent)
    return manager

if __name__ == "__main__":
    # اختبار النظام
    app = UltimateShipmentsManager()
    app.root.mainloop()
