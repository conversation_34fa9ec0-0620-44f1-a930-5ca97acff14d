#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الشحنات المتقدم
Advanced Shipments Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
from datetime import datetime, timedelta
import json
import os

# استيراد المكونات المتقدمة
from src.advanced_rtl_components import *
from src.advanced_arabic_styles import *
from src.auth_manager import auth_manager
from config.config import SHIPMENT_STATUS

class AdvancedShipmentsManager:
    """مدير الشحنات المتقدم"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.root = None
        self.selected_shipment = None
        self.shipments_data = []
        self.filtered_data = []
        self.current_filters = {}
        self.sort_column = None
        self.sort_reverse = False
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_shipments_data()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect('shipments.db')
            cursor = conn.cursor()
            
            # إنشاء جدول الشحنات المتقدم
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS advanced_shipments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shipment_number TEXT UNIQUE NOT NULL,
                    customer_name TEXT NOT NULL,
                    supplier_name TEXT NOT NULL,
                    origin_port TEXT,
                    destination_port TEXT,
                    shipment_date DATE,
                    expected_arrival DATE,
                    actual_arrival DATE,
                    status TEXT DEFAULT 'قيد التحضير',
                    priority TEXT DEFAULT 'عادي',
                    total_value REAL DEFAULT 0,
                    currency TEXT DEFAULT 'USD',
                    container_type TEXT,
                    container_number TEXT,
                    seal_number TEXT,
                    weight REAL,
                    volume REAL,
                    items_count INTEGER DEFAULT 0,
                    tracking_number TEXT,
                    shipping_line TEXT,
                    vessel_name TEXT,
                    voyage_number TEXT,
                    bill_of_lading TEXT,
                    customs_status TEXT DEFAULT 'لم يتم التخليص',
                    release_status TEXT DEFAULT 'بدون إفراج',
                    insurance_value REAL DEFAULT 0,
                    freight_cost REAL DEFAULT 0,
                    other_charges REAL DEFAULT 0,
                    notes TEXT,
                    documents_json TEXT,
                    created_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء فهارس للبحث السريع
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_shipment_number ON advanced_shipments(shipment_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_customer_name ON advanced_shipments(customer_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON advanced_shipments(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_shipment_date ON advanced_shipments(shipment_date)')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في إعداد قاعدة البيانات: {str(e)}")
    
    def create_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إنشاء النافذة الرئيسية
        self.root = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.root.title("🚢 نظام إدارة الشحنات المتقدم")
        self.root.geometry("1400x800")
        self.root.configure(bg=ADVANCED_COLORS['background'])
        self.root.state('zoomed')  # ملء الشاشة
        
        # إعداد النافذة
        self.root.resizable(True, True)
        self.root.minsize(1200, 700)
        
        # ربط أحداث لوحة المفاتيح
        self.root.bind('<Control-n>', lambda e: self.add_shipment())
        self.root.bind('<Control-f>', lambda e: self.focus_search())
        self.root.bind('<F5>', lambda e: self.refresh_data())
        self.root.bind('<Delete>', lambda e: self.delete_shipment())
        self.root.bind('<Control-e>', lambda e: self.edit_shipment())
        
        # إنشاء الواجهة
        self.create_header()
        self.create_toolbar()
        self.create_filters_panel()
        self.create_main_content()
        self.create_status_bar()
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = create_advanced_rtl_frame(self.root, style='header')
        header_frame.configure(bg=ADVANCED_COLORS['primary'], height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = create_advanced_rtl_label(
            header_frame,
            text="🚢 نظام إدارة الشحنات المتقدم",
            style='heading'
        )
        title_label.configure(
            bg=ADVANCED_COLORS['primary'],
            fg=ADVANCED_COLORS['text_white'],
            font=ADVANCED_FONTS['heading_large']
        )
        title_label.pack(side='right', padx=20, pady=20)
        
        # معلومات المستخدم والوقت
        info_frame = create_advanced_rtl_frame(header_frame)
        info_frame.configure(bg=ADVANCED_COLORS['primary'])
        info_frame.pack(side='left', padx=20, pady=20)
        
        # معلومات المستخدم
        current_user = auth_manager.get_current_user()
        username = current_user.get('username', 'غير محدد') if current_user else 'مستخدم'
        user_label = create_advanced_rtl_label(
            info_frame,
            text=f"👤 المستخدم: {username}",
            style='caption'
        )
        user_label.configure(
            bg=ADVANCED_COLORS['primary'],
            fg=ADVANCED_COLORS['text_white']
        )
        user_label.pack(anchor='w')
        
        # الوقت الحالي
        self.time_label = create_advanced_rtl_label(
            info_frame,
            text="",
            style='caption'
        )
        self.time_label.configure(
            bg=ADVANCED_COLORS['primary'],
            fg=ADVANCED_COLORS['text_white']
        )
        self.time_label.pack(anchor='w', pady=(5, 0))
        self.update_time()
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = create_advanced_rtl_frame(self.root)
        toolbar_frame.configure(bg=ADVANCED_COLORS['surface'], height=60)
        toolbar_frame.pack(fill='x', padx=10, pady=(10, 0))
        toolbar_frame.pack_propagate(False)
        
        # إضافة ظل للشريط
        toolbar_frame.add_shadow()
        
        # الأزرار الرئيسية
        buttons_frame = create_advanced_rtl_frame(toolbar_frame)
        buttons_frame.configure(bg=ADVANCED_COLORS['surface'])
        buttons_frame.pack(side='right', padx=20, pady=10)
        
        # زر شحنة جديدة
        new_btn = create_advanced_rtl_button(
            buttons_frame,
            text="➕ شحنة جديدة",
            style='primary',
            command=self.add_shipment
        )
        new_btn.pack(side='right', padx=(0, 10))
        
        # زر تعديل
        edit_btn = create_advanced_rtl_button(
            buttons_frame,
            text="✏️ تعديل",
            style='secondary',
            command=self.edit_shipment
        )
        edit_btn.pack(side='right', padx=(0, 10))
        
        # زر حذف
        delete_btn = create_advanced_rtl_button(
            buttons_frame,
            text="🗑️ حذف",
            style='danger',
            command=self.delete_shipment
        )
        delete_btn.pack(side='right', padx=(0, 10))
        
        # زر تحديث
        refresh_btn = create_advanced_rtl_button(
            buttons_frame,
            text="🔄 تحديث",
            style='outline',
            command=self.refresh_data
        )
        refresh_btn.pack(side='right', padx=(0, 10))
        
        # زر تصدير
        export_btn = create_advanced_rtl_button(
            buttons_frame,
            text="📊 تصدير",
            style='outline',
            command=self.export_data
        )
        export_btn.pack(side='right', padx=(0, 10))
        
        # شريط البحث السريع
        search_frame = create_advanced_rtl_frame(toolbar_frame)
        search_frame.configure(bg=ADVANCED_COLORS['surface'])
        search_frame.pack(side='left', padx=20, pady=10)
        
        search_label = create_advanced_rtl_label(
            search_frame,
            text="🔍 بحث سريع:",
            style='caption'
        )
        search_label.pack(side='right', padx=(0, 10))
        
        self.search_entry = create_advanced_rtl_entry(
            search_frame,
            placeholder="ابحث في الشحنات...",
            width=30
        )
        self.search_entry.pack(side='right')
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
    def update_time(self):
        """تحديث الوقت"""
        try:
            if self.root and self.root.winfo_exists():
                current_time = datetime.now().strftime("📅 %Y/%m/%d - ⏰ %H:%M:%S")
                self.time_label.configure(text=current_time)
                self.root.after(1000, self.update_time)
        except tk.TclError:
            pass
            
    def focus_search(self):
        """التركيز على حقل البحث"""
        self.search_entry.focus_set()
        
    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        search_term = self.search_entry.get_value().strip()
        self.apply_filters({'search': search_term})
        
    def add_shipment(self):
        """إضافة شحنة جديدة"""
        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm(self.root, mode='add')
            form.run()
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج الشحنة: {str(e)}")
            
    def edit_shipment(self):
        """تعديل شحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتعديل")
            return
            
        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm(self.root, mode='edit', shipment_data=self.selected_shipment)
            form.run()
            self.refresh_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج التعديل: {str(e)}")
            
    def delete_shipment(self):
        """حذف شحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للحذف")
            return
            
        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الشحنة رقم {self.selected_shipment.get('shipment_number', 'غير محدد')}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )
        
        if result:
            try:
                conn = sqlite3.connect('shipments.db')
                cursor = conn.cursor()
                cursor.execute('DELETE FROM advanced_shipments WHERE id = ?', (self.selected_shipment['id'],))
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", "تم حذف الشحنة بنجاح")
                self.refresh_data()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الشحنة: {str(e)}")
                
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("قريباً", "ميزة التصدير ستكون متاحة قريباً")
        
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_shipments_data()
        
    def load_shipments_data(self):
        """تحميل بيانات الشحنات"""
        try:
            conn = sqlite3.connect('shipments.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM advanced_shipments 
                ORDER BY created_at DESC
            ''')
            
            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            # تحويل البيانات إلى قواميس
            self.shipments_data = []
            for row in rows:
                shipment = dict(zip(columns, row))
                self.shipments_data.append(shipment)
                
            conn.close()
            
            # تطبيق المرشحات الحالية
            self.apply_filters(self.current_filters)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {str(e)}")
            
    def apply_filters(self, filters):
        """تطبيق المرشحات"""
        self.current_filters.update(filters)
        self.filtered_data = self.shipments_data.copy()

        # تطبيق البحث النصي
        search_term = self.current_filters.get('search', '').lower()
        if search_term:
            self.filtered_data = [
                shipment for shipment in self.filtered_data
                if any(
                    search_term in str(value).lower()
                    for value in shipment.values()
                    if value is not None
                )
            ]

        # تطبيق مرشح الحالة
        status_filter = self.current_filters.get('status')
        if status_filter:
            self.filtered_data = [
                shipment for shipment in self.filtered_data
                if shipment.get('status') == status_filter
            ]

        # تطبيق مرشح الأولوية
        priority_filter = self.current_filters.get('priority')
        if priority_filter:
            self.filtered_data = [
                shipment for shipment in self.filtered_data
                if shipment.get('priority') == priority_filter
            ]

        # تطبيق مرشح حالة الإفراج
        release_filter = self.current_filters.get('release_status')
        if release_filter:
            self.filtered_data = [
                shipment for shipment in self.filtered_data
                if shipment.get('release_status') == release_filter
            ]

        # تطبيق مرشح التاريخ
        date_range = self.current_filters.get('date_range')
        if date_range:
            start_date, end_date = date_range
            self.filtered_data = [
                shipment for shipment in self.filtered_data
                if self._is_date_in_range(shipment.get('shipment_date'), start_date, end_date)
            ]

        # تحديث الجدول
        self.update_table()

    def _is_date_in_range(self, date_str, start_date, end_date):
        """فحص ما إذا كان التاريخ ضمن النطاق المحدد"""
        if not date_str:
            return False

        try:
            # تحويل النص إلى تاريخ
            if isinstance(date_str, str):
                shipment_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                shipment_date = date_str

            return start_date <= shipment_date <= end_date
        except:
            return False
        
    def update_table(self):
        """تحديث الجدول"""
        # مسح البيانات الحالية
        for item in self.shipments_table.get_children():
            self.shipments_table.delete(item)

        # إضافة البيانات المفلترة
        for shipment in self.filtered_data:
            values = [
                shipment.get('shipment_number', ''),
                shipment.get('customer_name', ''),
                shipment.get('supplier_name', ''),
                shipment.get('origin_port', ''),
                shipment.get('destination_port', ''),
                shipment.get('shipment_date', ''),
                shipment.get('expected_arrival', ''),
                shipment.get('status', ''),
                shipment.get('release_status', 'بدون إفراج'),  # حالة الإفراج الجديدة
                shipment.get('priority', ''),
                f"{shipment.get('total_value', 0):.2f} {shipment.get('currency', 'USD')}"
            ]

            # تحديد لون الصف حسب الحالة والأولوية
            tags = []
            status = shipment.get('status', '')
            priority = shipment.get('priority', '')

            if status == 'ملغي':
                tags.append('cancelled')
            elif status == 'تم التسليم':
                tags.append('completed')
            elif priority == 'عاجل':
                tags.append('urgent')
            elif priority == 'حرج':
                tags.append('critical')

            item_id = self.shipments_table.insert_rtl_row(values, tags=tags)

        # تحديث عداد الشحنات
        total_count = len(self.shipments_data)
        filtered_count = len(self.filtered_data)

        if total_count == filtered_count:
            count_text = f"إجمالي الشحنات: {total_count}"
        else:
            count_text = f"عرض {filtered_count} من أصل {total_count} شحنة"

        self.count_label.configure(text=count_text)

        # تحديث حالة الأزرار
        self.update_button_states()

    def update_button_states(self):
        """تحديث حالة الأزرار"""
        # سيتم تنفيذ هذه الوظيفة حسب الحاجة
        pass

    def on_status_filter_change(self, event=None):
        """عند تغيير مرشح الحالة"""
        status = self.status_filter.get_selected_value()
        if status and status != 'الكل':
            self.apply_filters({'status': status})
        else:
            filters = self.current_filters.copy()
            filters.pop('status', None)
            self.current_filters = {}
            self.apply_filters(filters)

    def on_priority_filter_change(self, event=None):
        """عند تغيير مرشح الأولوية"""
        priority = self.priority_filter.get_selected_value()
        if priority and priority != 'الكل':
            self.apply_filters({'priority': priority})
        else:
            filters = self.current_filters.copy()
            filters.pop('priority', None)
            self.current_filters = {}
            self.apply_filters(filters)

    def on_release_filter_change(self, event=None):
        """عند تغيير مرشح حالة الإفراج"""
        release_status = self.release_filter.get_selected_value()
        if release_status and release_status != 'الكل':
            self.apply_filters({'release_status': release_status})
        else:
            filters = self.current_filters.copy()
            filters.pop('release_status', None)
            self.current_filters = {}
            self.apply_filters(filters)

    def on_date_filter_change(self, event=None):
        """عند تغيير مرشح التاريخ"""
        period = self.date_filter.get_selected_value()
        if period and period != 'الكل':
            # حساب نطاق التاريخ
            today = datetime.now().date()
            start_date = None

            if period == 'اليوم':
                start_date = today
            elif period == 'هذا الأسبوع':
                start_date = today - timedelta(days=today.weekday())
            elif period == 'هذا الشهر':
                start_date = today.replace(day=1)
            elif period == 'آخر 3 أشهر':
                start_date = today - timedelta(days=90)
            elif period == 'هذا العام':
                start_date = today.replace(month=1, day=1)

            if start_date:
                self.apply_filters({'date_range': (start_date, today)})
        else:
            filters = self.current_filters.copy()
            filters.pop('date_range', None)
            self.current_filters = {}
            self.apply_filters(filters)

    def on_table_select(self, event=None):
        """عند تحديد صف في الجدول"""
        selection = self.shipments_table.selection()
        if selection:
            item = selection[0]
            values = self.shipments_table.item(item, 'values')
            if values:
                # البحث عن الشحنة في البيانات
                shipment_number = values[0]
                self.selected_shipment = next(
                    (s for s in self.filtered_data if s.get('shipment_number') == shipment_number),
                    None
                )

                # تحديث شريط الحالة
                if self.selected_shipment:
                    status_text = f"محدد: {shipment_number} - {self.selected_shipment.get('customer_name', '')}"
                    self.status_label.configure(text=status_text)
        else:
            self.selected_shipment = None
            self.status_label.configure(text="جاهز")

    def on_table_double_click(self, event=None):
        """عند النقر المزدوج على الجدول"""
        self.edit_shipment()

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء القائمة السياقية
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.configure(
            bg=ADVANCED_COLORS['surface'],
            fg=ADVANCED_COLORS['text_primary'],
            font=ADVANCED_FONTS['body_small']
        )

        context_menu.add_command(label="✏️ تعديل", command=self.edit_shipment)
        context_menu.add_command(label="📋 نسخ رقم الشحنة", command=self.copy_shipment_number)
        context_menu.add_separator()
        context_menu.add_command(label="📊 عرض التفاصيل", command=self.show_shipment_details)
        context_menu.add_command(label="📄 طباعة", command=self.print_shipment)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_shipment)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def copy_shipment_number(self):
        """نسخ رقم الشحنة"""
        if self.selected_shipment:
            shipment_number = self.selected_shipment.get('shipment_number', '')
            self.root.clipboard_clear()
            self.root.clipboard_append(shipment_number)
            self.status_label.configure(text=f"تم نسخ رقم الشحنة: {shipment_number}")

    def show_shipment_details(self):
        """عرض تفاصيل الشحنة"""
        if self.selected_shipment:
            # إنشاء نافذة تفاصيل
            details_window = tk.Toplevel(self.root)
            details_window.title(f"تفاصيل الشحنة - {self.selected_shipment.get('shipment_number', '')}")
            details_window.geometry("600x400")
            details_window.configure(bg=ADVANCED_COLORS['background'])

            # محتوى التفاصيل
            details_text = create_advanced_rtl_text(
                details_window,
                width=70,
                height=20
            )
            details_text.pack(fill='both', expand=True, padx=20, pady=20)

            # إضافة التفاصيل
            details_content = self.format_shipment_details(self.selected_shipment)
            details_text.insert('1.0', details_content)
            details_text.configure(state='disabled')

    def format_shipment_details(self, shipment):
        """تنسيق تفاصيل الشحنة"""
        details = f"""
تفاصيل الشحنة رقم: {shipment.get('shipment_number', 'غير محدد')}

معلومات أساسية:
• العميل: {shipment.get('customer_name', 'غير محدد')}
• المورد: {shipment.get('supplier_name', 'غير محدد')}
• ميناء المنشأ: {shipment.get('origin_port', 'غير محدد')}
• ميناء الوصول: {shipment.get('destination_port', 'غير محدد')}

التواريخ:
• تاريخ الشحن: {shipment.get('shipment_date', 'غير محدد')}
• الوصول المتوقع: {shipment.get('expected_arrival', 'غير محدد')}
• الوصول الفعلي: {shipment.get('actual_arrival', 'لم يصل بعد')}

الحالة والأولوية:
• الحالة: {shipment.get('status', 'غير محدد')}
• الأولوية: {shipment.get('priority', 'غير محدد')}

المعلومات المالية:
• القيمة الإجمالية: {shipment.get('total_value', 0)} {shipment.get('currency', 'USD')}
• تكلفة الشحن: {shipment.get('freight_cost', 0)} {shipment.get('currency', 'USD')}
• قيمة التأمين: {shipment.get('insurance_value', 0)} {shipment.get('currency', 'USD')}

معلومات الحاوية:
• نوع الحاوية: {shipment.get('container_type', 'غير محدد')}
• رقم الحاوية: {shipment.get('container_number', 'غير محدد')}
• رقم الختم: {shipment.get('seal_number', 'غير محدد')}

الشحن والتتبع:
• خط الشحن: {shipment.get('shipping_line', 'غير محدد')}
• اسم السفينة: {shipment.get('vessel_name', 'غير محدد')}
• رقم الرحلة: {shipment.get('voyage_number', 'غير محدد')}
• رقم التتبع: {shipment.get('tracking_number', 'غير محدد')}

الملاحظات:
{shipment.get('notes', 'لا توجد ملاحظات')}

تاريخ الإنشاء: {shipment.get('created_at', 'غير محدد')}
آخر تحديث: {shipment.get('updated_at', 'غير محدد')}
        """
        return details.strip()

    def print_shipment(self):
        """طباعة الشحنة"""
        messagebox.showinfo("قريباً", "ميزة الطباعة ستكون متاحة قريباً")

    def sort_by_column(self, column):
        """ترتيب حسب العمود"""
        # تحديد اتجاه الترتيب
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False

        # تطبيق الترتيب
        column_map = {
            'رقم الشحنة': 'shipment_number',
            'العميل': 'customer_name',
            'المورد': 'supplier_name',
            'ميناء المنشأ': 'origin_port',
            'ميناء الوصول': 'destination_port',
            'تاريخ الشحن': 'shipment_date',
            'الوصول المتوقع': 'expected_arrival',
            'الحالة': 'status',
            'الأولوية': 'priority',
            'القيمة الإجمالية': 'total_value'
        }

        sort_key = column_map.get(column, 'shipment_number')

        try:
            self.filtered_data.sort(
                key=lambda x: x.get(sort_key, ''),
                reverse=self.sort_reverse
            )
            self.update_table()

            # تحديث رأس العمود لإظهار اتجاه الترتيب
            arrow = ' ↓' if self.sort_reverse else ' ↑'
            self.shipments_table.heading(column, text=column + arrow)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الترتيب: {str(e)}")
        
    def create_filters_panel(self):
        """إنشاء لوحة المرشحات"""
        filters_frame = create_advanced_rtl_frame(self.root)
        filters_frame.configure(bg=ADVANCED_COLORS['surface'], height=50)
        filters_frame.pack(fill='x', padx=10, pady=(5, 0))
        filters_frame.pack_propagate(False)

        # إضافة ظل
        filters_frame.add_shadow()

        # مرشحات سريعة
        quick_filters_frame = create_advanced_rtl_frame(filters_frame)
        quick_filters_frame.configure(bg=ADVANCED_COLORS['surface'])
        quick_filters_frame.pack(side='right', padx=20, pady=10)

        # مرشح الحالة
        status_label = create_advanced_rtl_label(
            quick_filters_frame,
            text="الحالة:",
            style='caption'
        )
        status_label.pack(side='right', padx=(0, 5))

        self.status_filter = create_advanced_rtl_combobox(
            quick_filters_frame,
            values=['الكل'] + list(SHIPMENT_STATUS.values()),
            placeholder="الكل",
            width=15
        )
        self.status_filter.pack(side='right', padx=(0, 20))
        self.status_filter.bind('<<ComboboxSelected>>', self.on_status_filter_change)

        # مرشح الأولوية
        priority_label = create_advanced_rtl_label(
            quick_filters_frame,
            text="الأولوية:",
            style='caption'
        )
        priority_label.pack(side='right', padx=(0, 5))

        self.priority_filter = create_advanced_rtl_combobox(
            quick_filters_frame,
            values=['الكل', 'عادي', 'مهم', 'عاجل', 'حرج'],
            placeholder="الكل",
            width=15
        )
        self.priority_filter.pack(side='right', padx=(0, 20))
        self.priority_filter.bind('<<ComboboxSelected>>', self.on_priority_filter_change)

        # مرشح حالة الإفراج
        release_label = create_advanced_rtl_label(
            quick_filters_frame,
            text="حالة الإفراج:",
            style='caption'
        )
        release_label.pack(side='right', padx=(0, 5))

        self.release_filter = create_advanced_rtl_combobox(
            quick_filters_frame,
            values=['الكل', 'بدون إفراج', 'مع الإفراج'],
            placeholder="الكل",
            width=15
        )
        self.release_filter.pack(side='right', padx=(0, 20))
        self.release_filter.bind('<<ComboboxSelected>>', self.on_release_filter_change)

        # مرشح التاريخ
        date_label = create_advanced_rtl_label(
            quick_filters_frame,
            text="الفترة:",
            style='caption'
        )
        date_label.pack(side='right', padx=(0, 5))

        self.date_filter = create_advanced_rtl_combobox(
            quick_filters_frame,
            values=['الكل', 'اليوم', 'هذا الأسبوع', 'هذا الشهر', 'آخر 3 أشهر', 'هذا العام'],
            placeholder="الكل",
            width=15
        )
        self.date_filter.pack(side='right')
        self.date_filter.bind('<<ComboboxSelected>>', self.on_date_filter_change)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # الإطار الرئيسي
        main_frame = create_advanced_rtl_frame(self.root)
        main_frame.configure(bg=ADVANCED_COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # إنشاء الجدول
        self.create_shipments_table(main_frame)

    def create_shipments_table(self, parent):
        """إنشاء جدول الشحنات"""
        # إطار الجدول
        table_frame = create_advanced_rtl_frame(parent)
        table_frame.configure(bg=ADVANCED_COLORS['surface'])
        table_frame.pack(fill='both', expand=True)
        table_frame.add_shadow()

        # أعمدة الجدول
        columns = [
            'رقم الشحنة', 'العميل', 'المورد', 'ميناء المنشأ', 'ميناء الوصول',
            'تاريخ الشحن', 'الوصول المتوقع', 'الحالة', 'حالة الإفراج', 'الأولوية', 'القيمة الإجمالية'
        ]

        # إنشاء الجدول
        self.shipments_table = create_advanced_rtl_treeview(
            table_frame,
            columns=columns,
            height=20
        )

        # إعداد أنماط الألوان للصفوف
        self.shipments_table.tag_configure('cancelled', background='#ffebee', foreground='#c62828')
        self.shipments_table.tag_configure('completed', background='#e8f5e8', foreground='#2e7d32')
        self.shipments_table.tag_configure('urgent', background='#fff3e0', foreground='#ef6c00')
        self.shipments_table.tag_configure('critical', background='#fce4ec', foreground='#ad1457')

        # إعداد عرض الأعمدة
        column_widths = [120, 150, 150, 120, 120, 100, 100, 100, 80, 120]
        for i, width in enumerate(column_widths):
            col_id = f'#{i+1}'
            self.shipments_table.column(col_id, width=width, anchor='e')

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.shipments_table.yview)
        self.shipments_table.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.shipments_table.xview)
        self.shipments_table.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول وأشرطة التمرير
        self.shipments_table.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # إعداد التمدد
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط الأحداث
        self.shipments_table.bind('<ButtonRelease-1>', self.on_table_select)
        self.shipments_table.bind('<Double-1>', self.on_table_double_click)
        self.shipments_table.bind('<Button-3>', self.show_context_menu)

        # ربط أحداث الترتيب
        for col in columns:
            self.shipments_table.heading(col, command=lambda c=col: self.sort_by_column(c))

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = create_advanced_rtl_frame(self.root)
        status_frame.configure(bg=ADVANCED_COLORS['surface'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # معلومات الحالة
        self.status_label = create_advanced_rtl_label(
            status_frame,
            text="جاهز",
            style='caption'
        )
        self.status_label.pack(side='right', padx=20, pady=5)

        # عداد الشحنات
        self.count_label = create_advanced_rtl_label(
            status_frame,
            text="إجمالي الشحنات: 0",
            style='caption'
        )
        self.count_label.pack(side='left', padx=20, pady=5)
        
    def run(self):
        """تشغيل النافذة"""
        if self.root:
            self.root.mainloop()
