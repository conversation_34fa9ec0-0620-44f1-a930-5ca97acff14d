#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json

def test_container_functionality():
    """اختبار وظائف الحاويات الجديدة"""
    
    print("🧪 اختبار وظائف الحاويات المتعددة...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('shipments.db')
        cursor = conn.cursor()
        
        # اختبار 1: التحقق من وجود الحقول الجديدة
        print("\n1️⃣ التحقق من هيكل قاعدة البيانات:")
        cursor.execute("PRAGMA table_info(advanced_shipments)")
        columns = cursor.fetchall()
        
        container_count_exists = False
        container_numbers_json_exists = False
        
        for col in columns:
            if col[1] == 'container_count':
                container_count_exists = True
                print(f"   ✅ حقل container_count موجود - النوع: {col[2]}")
            elif col[1] == 'container_numbers_json':
                container_numbers_json_exists = True
                print(f"   ✅ حقل container_numbers_json موجود - النوع: {col[2]}")
        
        if not container_count_exists:
            print("   ❌ حقل container_count غير موجود")
        if not container_numbers_json_exists:
            print("   ❌ حقل container_numbers_json غير موجود")
        
        # اختبار 2: إدراج بيانات تجريبية
        print("\n2️⃣ اختبار إدراج بيانات تجريبية:")
        
        test_data = {
            'shipment_number': 'TEST-001',
            'customer_name': 'عميل تجريبي',
            'container_count': 3,
            'container_numbers': ['CONT-001', 'CONT-002', 'CONT-003']
        }
        
        cursor.execute('''
            INSERT INTO advanced_shipments (
                shipment_number, customer_name, supplier_name, container_count,
                container_numbers_json, container_number, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_data['shipment_number'],
            test_data['customer_name'],
            'مورد تجريبي',  # إضافة اسم مورد
            test_data['container_count'],
            json.dumps(test_data['container_numbers']),
            test_data['container_numbers'][0],  # الحاوية الأولى للتوافق
            '2024-01-01 00:00:00',  # تاريخ الإنشاء
            '2024-01-01 00:00:00'   # تاريخ التحديث
        ))
        
        print("   ✅ تم إدراج البيانات التجريبية بنجاح")
        
        # اختبار 3: استرجاع البيانات
        print("\n3️⃣ اختبار استرجاع البيانات:")
        
        cursor.execute('''
            SELECT shipment_number, customer_name, container_count, 
                   container_numbers_json, container_number
            FROM advanced_shipments 
            WHERE shipment_number = ?
        ''', (test_data['shipment_number'],))
        
        result = cursor.fetchone()
        if result:
            shipment_num, customer, count, numbers_json, first_container = result
            container_numbers = json.loads(numbers_json) if numbers_json else []
            
            print(f"   📦 رقم الشحنة: {shipment_num}")
            print(f"   👤 العميل: {customer}")
            print(f"   🔢 عدد الحاويات: {count}")
            print(f"   📋 أرقام الحاويات: {container_numbers}")
            print(f"   🏷️ الحاوية الأولى: {first_container}")
            
            # التحقق من صحة البيانات
            if count == len(container_numbers):
                print("   ✅ عدد الحاويات يطابق قائمة الأرقام")
            else:
                print("   ❌ عدد الحاويات لا يطابق قائمة الأرقام")
        else:
            print("   ❌ لم يتم العثور على البيانات التجريبية")
        
        # اختبار 4: تحديث البيانات
        print("\n4️⃣ اختبار تحديث البيانات:")
        
        updated_containers = ['CONT-001-UPD', 'CONT-002-UPD']
        cursor.execute('''
            UPDATE advanced_shipments 
            SET container_count = ?, container_numbers_json = ?, container_number = ?
            WHERE shipment_number = ?
        ''', (
            len(updated_containers),
            json.dumps(updated_containers),
            updated_containers[0],
            test_data['shipment_number']
        ))
        
        print("   ✅ تم تحديث البيانات بنجاح")
        
        # التحقق من التحديث
        cursor.execute('''
            SELECT container_count, container_numbers_json 
            FROM advanced_shipments 
            WHERE shipment_number = ?
        ''', (test_data['shipment_number'],))
        
        result = cursor.fetchone()
        if result:
            count, numbers_json = result
            container_numbers = json.loads(numbers_json) if numbers_json else []
            print(f"   📊 البيانات المحدثة - العدد: {count}, الأرقام: {container_numbers}")
        
        # تنظيف البيانات التجريبية
        print("\n5️⃣ تنظيف البيانات التجريبية:")
        cursor.execute('DELETE FROM advanced_shipments WHERE shipment_number = ?', 
                      (test_data['shipment_number'],))
        print("   ✅ تم حذف البيانات التجريبية")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 جميع الاختبارات نجحت! وظائف الحاويات المتعددة تعمل بشكل صحيح.")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    test_container_functionality()
