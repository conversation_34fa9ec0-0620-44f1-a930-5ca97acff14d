#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_tables():
    """فحص هيكل الجداول"""
    
    try:
        conn = sqlite3.connect('shipments.db')
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        print("الجداول الموجودة:")
        for table in tables:
            print(f"  - {table}")
        
        # فحص جدول shipments إذا كان موجوداً
        if 'shipments' in tables:
            print("\nهيكل جدول shipments:")
            cursor.execute("PRAGMA table_info(shipments)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} - {col[2]}")
        
        # فحص جدول advanced_shipments
        if 'advanced_shipments' in tables:
            print("\nهيكل جدول advanced_shipments:")
            cursor.execute("PRAGMA table_info(advanced_shipments)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} - {col[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_tables()
