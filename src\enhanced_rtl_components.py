#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات RTL محسنة مع أنماط CSS متقدمة
Enhanced RTL Components with Advanced CSS Styles
"""

import tkinter as tk
from tkinter import ttk
import os
import sys

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG
from src.advanced_rtl_styles import advanced_style_manager

class EnhancedRTLFrame(tk.Frame):
    """إطار RTL محسن مع أنماط متقدمة"""
    
    def __init__(self, parent, style='container_primary', **kwargs):
        # تطبيق النمط المحدد
        style_config = advanced_style_manager.get_style(style)
        kwargs.update(style_config)
        
        super().__init__(parent, **kwargs)
        
        # تطبيق خصائص RTL
        self.configure_rtl()
        
    def configure_rtl(self):
        """تكوين خصائص RTL"""
        # تعيين اتجاه التخطيط
        self.pack_configure(side='right')

class EnhancedRTLLabel(tk.Label):
    """تسمية RTL محسنة مع أنماط متقدمة"""
    
    def __init__(self, parent, text="", style='text_body_md', **kwargs):
        # تطبيق النمط المحدد
        style_config = advanced_style_manager.get_style(style)
        kwargs.update(style_config)
        
        # تعيين النص
        kwargs['text'] = text
        
        super().__init__(parent, **kwargs)
        
        # تطبيق خصائص RTL
        self.configure_rtl()
        
    def configure_rtl(self):
        """تكوين خصائص RTL"""
        # تعيين المحاذاة والاتجاه
        self.configure(anchor='e', justify='right')

class EnhancedRTLButton(tk.Button):
    """زر RTL محسن مع أنماط متقدمة وتأثيرات"""
    
    def __init__(self, parent, text="", style='button_primary', command=None, **kwargs):
        # تطبيق النمط المحدد
        style_config = advanced_style_manager.get_style(style)
        kwargs.update(style_config)
        
        # تعيين النص والأمر
        kwargs['text'] = text
        if command:
            kwargs['command'] = command
        
        super().__init__(parent, **kwargs)
        
        # تطبيق خصائص RTL والتأثيرات
        self.configure_rtl()
        self.add_hover_effects()
        
    def configure_rtl(self):
        """تكوين خصائص RTL"""
        # تعيين اتجاه النص
        self.configure(compound='right')
        
    def add_hover_effects(self):
        """إضافة تأثيرات التمرير"""
        original_bg = self.cget('bg')
        original_fg = self.cget('fg')
        
        def on_enter(event):
            # تأثير التمرير
            hover_bg = self.lighten_color(original_bg, 0.1)
            self.configure(bg=hover_bg)
            
        def on_leave(event):
            # العودة للون الأصلي
            self.configure(bg=original_bg, fg=original_fg)
            
        def on_press(event):
            # تأثير الضغط
            press_bg = self.darken_color(original_bg, 0.1)
            self.configure(bg=press_bg)
            
        def on_release(event):
            # العودة بعد الضغط
            self.configure(bg=original_bg)
        
        self.bind("<Enter>", on_enter)
        self.bind("<Leave>", on_leave)
        self.bind("<Button-1>", on_press)
        self.bind("<ButtonRelease-1>", on_release)
    
    def lighten_color(self, color, factor):
        """تفتيح اللون"""
        try:
            # تحويل اللون إلى RGB
            rgb = self.winfo_rgb(color)
            # تفتيح كل مكون
            r = min(65535, int(rgb[0] * (1 + factor)))
            g = min(65535, int(rgb[1] * (1 + factor)))
            b = min(65535, int(rgb[2] * (1 + factor)))
            # تحويل إلى hex
            return f"#{r//256:02x}{g//256:02x}{b//256:02x}"
        except:
            return color
    
    def darken_color(self, color, factor):
        """تغميق اللون"""
        try:
            # تحويل اللون إلى RGB
            rgb = self.winfo_rgb(color)
            # تغميق كل مكون
            r = max(0, int(rgb[0] * (1 - factor)))
            g = max(0, int(rgb[1] * (1 - factor)))
            b = max(0, int(rgb[2] * (1 - factor)))
            # تحويل إلى hex
            return f"#{r//256:02x}{g//256:02x}{b//256:02x}"
        except:
            return color

class EnhancedRTLEntry(tk.Entry):
    """حقل إدخال RTL محسن مع أنماط متقدمة"""
    
    def __init__(self, parent, style='input_primary', placeholder="", **kwargs):
        # تطبيق النمط المحدد
        style_config = advanced_style_manager.get_style(style)
        kwargs.update(style_config)
        
        super().__init__(parent, **kwargs)
        
        # تطبيق خصائص RTL
        self.configure_rtl()
        
        # إضافة placeholder
        self.placeholder = placeholder
        if placeholder:
            self.add_placeholder()
        
        # إضافة تأثيرات التركيز
        self.add_focus_effects()
        
    def configure_rtl(self):
        """تكوين خصائص RTL"""
        # تعيين اتجاه النص
        self.configure(justify='right')
        
    def add_placeholder(self):
        """إضافة نص placeholder"""
        self.placeholder_active = True
        self.insert(0, self.placeholder)
        self.configure(fg=COLORS['text_muted'])
        
        def on_focus_in(event):
            if self.placeholder_active:
                self.delete(0, tk.END)
                self.configure(fg=COLORS['text_primary'])
                self.placeholder_active = False
                
        def on_focus_out(event):
            if not self.get():
                self.insert(0, self.placeholder)
                self.configure(fg=COLORS['text_muted'])
                self.placeholder_active = True
        
        self.bind("<FocusIn>", on_focus_in)
        self.bind("<FocusOut>", on_focus_out)
    
    def add_focus_effects(self):
        """إضافة تأثيرات التركيز"""
        original_bg = self.cget('bg')
        original_highlightcolor = self.cget('highlightcolor')
        
        def on_focus_in(event):
            self.configure(
                highlightcolor=COLORS['primary'],
                highlightthickness=2
            )
            
        def on_focus_out(event):
            self.configure(
                highlightcolor=original_highlightcolor,
                highlightthickness=1
            )
        
        self.bind("<FocusIn>", on_focus_in)
        self.bind("<FocusOut>", on_focus_out)

class EnhancedRTLCombobox(ttk.Combobox):
    """قائمة منسدلة RTL محسنة"""

    def __init__(self, parent, style_name='combobox_primary', **kwargs):
        # إزالة style من kwargs إذا كان موجوداً
        kwargs.pop('style', None)

        super().__init__(parent, **kwargs)

        # تطبيق النمط المحدد
        self.configure_style(style_name)

        # تطبيق خصائص RTL
        self.configure_rtl()

    def configure_style(self, style_name):
        """تكوين النمط"""
        try:
            style_config = advanced_style_manager.get_style(style_name)

            # إنشاء نمط مخصص للـ Combobox
            style_obj = ttk.Style()
            ttk_style_name = f"RTL.{style_name}.TCombobox"

            # تطبيق الخصائص المتوافقة فقط
            compatible_config = {}
            for key, value in style_config.items():
                if key in ['font', 'background', 'foreground', 'fieldbackground', 'borderwidth', 'relief']:
                    compatible_config[key] = value

            if compatible_config:
                style_obj.configure(ttk_style_name, **compatible_config)
                self.configure(style=ttk_style_name)
        except Exception as e:
            print(f"خطأ في تكوين نمط Combobox: {e}")

    def configure_rtl(self):
        """تكوين خصائص RTL"""
        try:
            # تعيين اتجاه النص
            self.configure(justify='right')
        except Exception as e:
            print(f"خطأ في تكوين RTL للـ Combobox: {e}")

class EnhancedRTLText(tk.Text):
    """منطقة نص RTL محسنة"""
    
    def __init__(self, parent, style='input_primary', **kwargs):
        # تطبيق النمط المحدد
        style_config = advanced_style_manager.get_style(style)
        
        # إزالة الخصائص غير المتوافقة مع Text
        text_config = {k: v for k, v in style_config.items() 
                      if k not in ['justify', 'anchor']}
        kwargs.update(text_config)
        
        super().__init__(parent, **kwargs)
        
        # تطبيق خصائص RTL
        self.configure_rtl()
        
        # إضافة شريط تمرير RTL
        self.add_rtl_scrollbar()
        
    def configure_rtl(self):
        """تكوين خصائص RTL"""
        # تعيين اتجاه النص
        self.tag_configure("rtl", justify='right')
        self.tag_add("rtl", "1.0", "end")
        
    def add_rtl_scrollbar(self):
        """إضافة شريط تمرير RTL"""
        # إنشاء شريط تمرير على اليسار
        scrollbar = ttk.Scrollbar(self.master, orient='vertical', command=self.yview)
        self.configure(yscrollcommand=scrollbar.set)
        
        # وضع شريط التمرير على اليسار
        scrollbar.pack(side='left', fill='y')

class EnhancedRTLTreeview(ttk.Treeview):
    """جدول RTL محسن مع أنماط متقدمة"""

    def __init__(self, parent, style_name='treeview_primary', **kwargs):
        # إزالة style من kwargs إذا كان موجوداً
        kwargs.pop('style', None)

        super().__init__(parent, **kwargs)

        # تطبيق النمط المحدد
        self.configure_style(style_name)

        # تطبيق خصائص RTL
        self.configure_rtl()

        # إضافة تأثيرات التفاعل
        self.add_interaction_effects()

    def configure_style(self, style_name):
        """تكوين النمط"""
        try:
            style_config = advanced_style_manager.get_style(style_name)
            heading_config = advanced_style_manager.get_style('treeview_heading')

            # إنشاء أنماط مخصصة
            style_obj = ttk.Style()

            # نمط الجدول - تطبيق الخصائص المتوافقة فقط
            treeview_style = f"RTL.{style_name}.Treeview"
            compatible_config = {}
            for key, value in style_config.items():
                if key in ['background', 'foreground', 'fieldbackground', 'borderwidth', 'relief', 'font', 'rowheight']:
                    compatible_config[key] = value

            if compatible_config:
                style_obj.configure(treeview_style, **compatible_config)
                self.configure(style=treeview_style)

            # نمط الرؤوس
            heading_style = f"RTL.{style_name}.Treeview.Heading"
            heading_compatible = {}
            for key, value in heading_config.items():
                if key in ['background', 'foreground', 'font', 'relief', 'borderwidth']:
                    heading_compatible[key] = value

            if heading_compatible:
                style_obj.configure(heading_style, **heading_compatible)

        except Exception as e:
            print(f"خطأ في تكوين نمط Treeview: {e}")
        
    def configure_rtl(self):
        """تكوين خصائص RTL"""
        # تعيين محاذاة الأعمدة لليمين
        for col in self['columns']:
            self.column(col, anchor='e')
            self.heading(col, anchor='e')
    
    def add_interaction_effects(self):
        """إضافة تأثيرات التفاعل"""
        def on_select(event):
            # تأثير التحديد
            selection = self.selection()
            if selection:
                item = selection[0]
                self.set(item, '#0', '►')  # إضافة مؤشر
        
        self.bind('<<TreeviewSelect>>', on_select)

class EnhancedRTLCard(EnhancedRTLFrame):
    """بطاقة RTL محسنة مع عنوان وتأثيرات"""
    
    def __init__(self, parent, title="", style='container_card', **kwargs):
        super().__init__(parent, style=style, **kwargs)
        
        # إنشاء العنوان
        if title:
            self.create_title(title)
        
        # إضافة تأثير الظل
        self.add_shadow_effect()
        
    def create_title(self, title):
        """إنشاء عنوان البطاقة"""
        title_frame = EnhancedRTLFrame(self, style='container_primary')
        title_frame.pack(fill='x', pady=(0, 10))
        
        title_label = EnhancedRTLLabel(
            title_frame,
            text=title,
            style='text_heading_md'
        )
        title_label.pack(anchor='e', padx=10, pady=5)
        
        # خط فاصل
        separator = tk.Frame(
            title_frame,
            height=2,
            bg=COLORS['primary'],
            relief='flat'
        )
        separator.pack(fill='x', padx=10)
        
    def add_shadow_effect(self):
        """إضافة تأثير الظل"""
        # محاكاة الظل باستخدام إطار إضافي
        self.configure(highlightthickness=1, highlightcolor=COLORS['border'])

class EnhancedRTLToolbar(EnhancedRTLFrame):
    """شريط أدوات RTL محسن"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, style='container_elevated', **kwargs)
        
        # تكوين الشريط
        self.configure(height=60)
        self.pack_propagate(False)
        
        # إنشاء حاوية الأزرار
        self.buttons_frame = EnhancedRTLFrame(self, style='container_primary')
        self.buttons_frame.pack(side='right', padx=15, pady=10)
        
    def add_button(self, text, command=None, style='button_primary', icon=None):
        """إضافة زر للشريط"""
        if icon:
            text = f"{icon} {text}"
        
        button = EnhancedRTLButton(
            self.buttons_frame,
            text=text,
            style=style,
            command=command
        )
        button.pack(side='right', padx=5)
        
        return button

class EnhancedRTLSearchPanel(EnhancedRTLFrame):
    """لوحة بحث RTL محسنة"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, style='container_card', **kwargs)
        
        # إنشاء محتوى اللوحة
        self.create_search_content()
        
    def create_search_content(self):
        """إنشاء محتوى البحث"""
        # عنوان اللوحة
        title_label = EnhancedRTLLabel(
            self,
            text="🔍 البحث والتصفية المتقدمة",
            style='text_heading_md'
        )
        title_label.pack(anchor='e', padx=20, pady=(15, 10))
        
        # خط فاصل
        separator = tk.Frame(
            self,
            height=2,
            bg=COLORS['primary'],
            relief='flat'
        )
        separator.pack(fill='x', padx=20, pady=(0, 15))
        
        # محتوى البحث
        content_frame = EnhancedRTLFrame(self, style='container_primary')
        content_frame.pack(fill='x', padx=20, pady=(0, 15))
        
        return content_frame

# وظائف مساعدة لإنشاء المكونات
def create_enhanced_rtl_frame(parent, style='container_primary', **kwargs):
    """إنشاء إطار RTL محسن"""
    return EnhancedRTLFrame(parent, style=style, **kwargs)

def create_enhanced_rtl_label(parent, text="", style='text_body_md', **kwargs):
    """إنشاء تسمية RTL محسنة"""
    return EnhancedRTLLabel(parent, text=text, style=style, **kwargs)

def create_enhanced_rtl_button(parent, text="", style='button_primary', command=None, **kwargs):
    """إنشاء زر RTL محسن"""
    return EnhancedRTLButton(parent, text=text, style=style, command=command, **kwargs)

def create_enhanced_rtl_entry(parent, style='input_primary', placeholder="", **kwargs):
    """إنشاء حقل إدخال RTL محسن"""
    return EnhancedRTLEntry(parent, style=style, placeholder=placeholder, **kwargs)

def create_enhanced_rtl_combobox(parent, style='combobox_primary', **kwargs):
    """إنشاء قائمة منسدلة RTL محسنة"""
    return EnhancedRTLCombobox(parent, style_name=style, **kwargs)

def create_enhanced_rtl_text(parent, style='input_primary', **kwargs):
    """إنشاء منطقة نص RTL محسنة"""
    return EnhancedRTLText(parent, style=style, **kwargs)

def create_enhanced_rtl_treeview(parent, style='treeview_primary', **kwargs):
    """إنشاء جدول RTL محسن"""
    return EnhancedRTLTreeview(parent, style_name=style, **kwargs)

def create_enhanced_rtl_card(parent, title="", style='container_card', **kwargs):
    """إنشاء بطاقة RTL محسنة"""
    return EnhancedRTLCard(parent, title=title, style=style, **kwargs)

def create_enhanced_rtl_toolbar(parent, **kwargs):
    """إنشاء شريط أدوات RTL محسن"""
    return EnhancedRTLToolbar(parent, **kwargs)

def create_enhanced_rtl_search_panel(parent, **kwargs):
    """إنشاء لوحة بحث RTL محسنة"""
    return EnhancedRTLSearchPanel(parent, **kwargs)
