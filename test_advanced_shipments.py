#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الشحنات المتقدم
Test Advanced Shipments Management System
"""

import tkinter as tk
import sys
import os

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_advanced_shipments():
    """اختبار نظام إدارة الشحنات المتقدم"""
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # استيراد وتشغيل نظام إدارة الشحنات
        from src.advanced_shipments_manager import AdvancedShipmentsManager
        
        # إنشاء مدير الشحنات
        shipments_manager = AdvancedShipmentsManager()
        
        # تشغيل النظام
        shipments_manager.run()
        
    except Exception as e:
        print(f"خطأ في تشغيل النظام: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_advanced_shipments()
