# 🔍 وظيفة البحث F9 في قسم الأصناف

## 📋 نظرة عامة

تم تفعيل **وظيفة البحث المتقدم** في قسم الأصناف باستخدام زر **F9**، مما يوفر طريقة سريعة وفعالة للبحث في جميع الأصناف والمنتجات المضافة للشحنة. هذه الوظيفة تدعم البحث العام والبحث المتقدم مع واجهة RTL متكاملة.

## ✨ الميزات الرئيسية

### 🔍 **البحث العام السريع**

#### **حقل البحث الموحد:**
- **بحث شامل**: في كود الصنف، اسم الصنف، والوصف معاً
- **بحث فوري**: النتائج تظهر أثناء الكتابة
- **بحث ذكي**: غير حساس لحالة الأحرف
- **نص توضيحي**: "ابحث في كود الصنف، اسم الصنف، أو الوصف..."

#### **مثال على البحث العام:**
```
إدخال: "منتج"
النتائج: جميع الأصناف التي تحتوي على كلمة "منتج" في:
- كود الصنف
- اسم الصنف  
- الوصف
```

### 🎯 **البحث المتقدم المخصص**

#### **3 حقول بحث متخصصة:**

**1. 🔢 كود الصنف:**
- بحث دقيق في أكواد الأصناف
- مفيد للبحث عن صنف محدد بالكود
- بحث جزئي مدعوم

**2. 📦 اسم الصنف:**
- بحث في أسماء المنتجات
- بحث جزئي وكامل
- غير حساس لحالة الأحرف

**3. 🌍 بلد المنشأ:**
- قائمة منسدلة بالبلدان المدعومة
- بحث دقيق حسب البلد
- 12 بلد مدعوم

#### **البلدان المدعومة:**
- **دول الخليج**: السعودية، الإمارات، الكويت، قطر، البحرين، عمان
- **دول أخرى**: الصين، الهند، تركيا، ألمانيا، أمريكا، اليابان

### 📊 **جدول النتائج المتقدم**

#### **7 أعمدة شاملة:**
1. **🔢 كود الصنف**: رقم التعريف
2. **📦 اسم الصنف**: الاسم التجاري
3. **📝 الوصف**: وصف المنتج
4. **📊 الكمية**: العدد المشحون
5. **📏 الوحدة**: وحدة القياس
6. **💰 سعر الوحدة**: السعر لكل وحدة
7. **💯 الإجمالي**: القيمة الإجمالية

#### **مميزات الجدول:**
- **أشرطة تمرير**: عمودية وأفقية
- **صفوف متناوبة**: ألوان متناوبة للوضوح
- **تحديد مفرد**: اختيار صنف واحد
- **نقر مزدوج**: للاختيار السريع

### 📈 **عداد النتائج الذكي**

#### **عرض ديناميكي:**
- **عند عدم التصفية**: "📊 إجمالي الأصناف: X"
- **عند التصفية**: "📊 النتائج: Y من X"
- **تحديث فوري**: مع كل تغيير في البحث

#### **مثال:**
```
قبل البحث: "📊 إجمالي الأصناف: 25"
بعد البحث: "📊 النتائج: 8 من 25"
```

### ⌨️ **اختصارات لوحة المفاتيح**

#### **اختصارات البحث:**
- **F9**: فتح نافذة البحث
- **Enter**: اختيار الصنف المحدد
- **Esc**: إغلاق نافذة البحث
- **Double-Click**: اختيار سريع

#### **اختصارات التنقل:**
- **Tab**: التنقل بين حقول البحث
- **↑/↓**: التنقل في نتائج الجدول
- **Ctrl+A**: تحديد الكل (في حقول النص)

## 🛠️ **التقنيات المستخدمة**

### 🎨 **نافذة حوار متقدمة**

#### **ItemSearchDialog Class:**
```python
class ItemSearchDialog:
    """نافذة حوار البحث في الأصناف"""
    - حجم: 800x600 بكسل
    - نافذة modal (تركيز حصري)
    - تصميم RTL كامل
    - ربط أحداث شامل
```

#### **مكونات الواجهة:**
- **منطقة البحث**: حقول البحث العام والمتقدم
- **شريط أدوات**: أزرار البحث والمسح
- **جدول النتائج**: عرض النتائج مع تمرير
- **شريط التحكم**: أزرار الاختيار والإغلاق
- **شريط المساعدة**: نصائح الاستخدام

### 🔄 **البحث الفوري**

#### **تقنية التأخير الذكي:**
```python
def on_search_change(self, *args):
    """عند تغيير نص البحث"""
    if hasattr(self, '_search_timer'):
        self.root.after_cancel(self._search_timer)
    self._search_timer = self.root.after(300, self.perform_search)
```

#### **مميزات البحث الفوري:**
- **تأخير 300ms**: لتجنب البحث المفرط
- **إلغاء تلقائي**: للبحث السابق
- **أداء محسن**: لا يؤثر على سرعة النظام

### 🎯 **خوارزمية البحث المتقدمة**

#### **البحث متعدد المعايير:**
```python
def perform_search(self):
    """تنفيذ البحث"""
    # 1. جمع معايير البحث
    # 2. تصفية حسب البحث العام
    # 3. تصفية حسب المعايير المتقدمة
    # 4. تحديث النتائج
    # 5. تحديث العداد
```

#### **منطق التصفية:**
```
للصنف ليكون في النتائج:
1. يجب أن يطابق البحث العام (إذا كان موجوداً)
2. AND يجب أن يطابق كود الصنف (إذا كان موجوداً)
3. AND يجب أن يطابق اسم الصنف (إذا كان موجوداً)
4. AND يجب أن يطابق بلد المنشأ (إذا كان موجوداً)
```

### 🔗 **التكامل مع النظام**

#### **ربط مع قسم الأصناف:**
```python
def show_item_search(self, event=None):
    """إظهار نافذة البحث عند F9"""
    # 1. التحقق من القسم الحالي
    # 2. إنشاء نافذة البحث
    # 3. انتظار النتيجة
    # 4. تمييز الصنف المختار
```

#### **تمييز النتيجة:**
```python
def highlight_item_in_tree(self, item_data):
    """تمييز صنف في الجدول"""
    # 1. البحث عن الصنف في الجدول
    # 2. تحديد الصنف
    # 3. التركيز عليه
    # 4. التمرير إليه
```

## 🚀 **كيفية الاستخدام**

### **1. تفعيل البحث:**
```
طرق تفعيل البحث:
✅ اضغط F9 في قسم الأصناف
✅ انقر على تلميح "💡 اضغط F9 للبحث في الأصناف"
❌ F9 لا يعمل في الأقسام الأخرى (سيظهر تنبيه)
```

### **2. البحث العام:**
```
1. اكتب في حقل "🔍 البحث العام"
2. النتائج تظهر فورياً أثناء الكتابة
3. البحث يشمل: كود الصنف + اسم الصنف + الوصف
```

### **3. البحث المتقدم:**
```
1. استخدم حقول البحث المتخصصة:
   - 🔢 كود الصنف: للبحث بالكود
   - 📦 اسم الصنف: للبحث بالاسم
   - 🌍 بلد المنشأ: للبحث بالبلد

2. يمكن الجمع بين عدة معايير
3. البحث تراكمي (AND logic)
```

### **4. اختيار النتيجة:**
```
طرق الاختيار:
✅ انقر على الصنف ثم "✅ اختيار الصنف"
✅ انقر مرتين على الصنف
✅ انقر على الصنف ثم اضغط Enter
```

### **5. مسح البحث:**
```
1. انقر "🗑️ مسح" لمسح جميع حقول البحث
2. أو امسح الحقول يدوياً
3. سيتم عرض جميع الأصناف مرة أخرى
```

## 🎯 **حالات الاستخدام**

### **🔍 سيناريوهات البحث الشائعة:**

#### **1. البحث بالكود:**
```
المشكلة: تريد العثور على صنف بكود "ITM001"
الحل: اكتب "ITM001" في حقل كود الصنف
النتيجة: عرض الصنف المطابق فقط
```

#### **2. البحث بالاسم:**
```
المشكلة: تريد العثور على جميع المنتجات التي تحتوي على "قهوة"
الحل: اكتب "قهوة" في حقل اسم الصنف أو البحث العام
النتيجة: عرض جميع منتجات القهوة
```

#### **3. البحث بالبلد:**
```
المشكلة: تريد رؤية جميع المنتجات من الصين
الحل: اختر "الصين" من قائمة بلد المنشأ
النتيجة: عرض جميع المنتجات الصينية
```

#### **4. البحث المركب:**
```
المشكلة: تريد منتجات قهوة من البرازيل
الحل: اكتب "قهوة" في اسم الصنف + اختر "البرازيل" من بلد المنشأ
النتيجة: عرض منتجات القهوة البرازيلية فقط
```

### **⚡ نصائح للاستخدام الأمثل:**

#### **1. البحث السريع:**
- استخدم البحث العام للبحث السريع
- اكتب أي جزء من كود أو اسم الصنف
- النتائج تظهر فورياً

#### **2. البحث الدقيق:**
- استخدم حقول البحث المتخصصة
- اجمع بين عدة معايير للدقة
- استخدم قائمة بلد المنشأ للتصفية

#### **3. التنقل السريع:**
- استخدم F9 للوصول السريع
- استخدم Enter للاختيار السريع
- استخدم Esc للإغلاق السريع

## 🎉 **النتيجة النهائية**

**✅ تم تفعيل البحث F9 في قسم الأصناف بنجاح!**

### **🌟 الإنجازات المحققة:**
- 🔍 **بحث متقدم** مع واجهة RTL متكاملة
- ⚡ **بحث فوري** مع تأخير ذكي 300ms
- 🎯 **بحث متعدد المعايير** مع منطق AND
- 📊 **جدول نتائج متقدم** مع 7 أعمدة
- 📈 **عداد نتائج ذكي** مع عرض ديناميكي
- ⌨️ **اختصارات شاملة** للاستخدام السريع
- 🔗 **تكامل كامل** مع قسم الأصناف
- 💡 **تلميحات واضحة** للمستخدم

### **📊 الإحصائيات:**
- **حقول البحث**: 4 حقول (عام + 3 متخصصة)
- **أعمدة النتائج**: 7 أعمدة شاملة
- **البلدان المدعومة**: 12 بلد
- **الاختصارات**: 6 اختصارات رئيسية
- **وقت الاستجابة**: 300ms للبحث الفوري

**وظيفة البحث F9 جاهزة للاستخدام الإنتاجي مع تجربة مستخدم متقدمة!** 🔍✨📊🎯📦
