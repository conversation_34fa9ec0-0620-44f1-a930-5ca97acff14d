#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإعدادات العامة
General Settings Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date
from tkcalendar import DateEntry
from PIL import Image, ImageTk

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, CURRENCIES

class SettingsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.company_logo = None
        self.setup_window()
        self.create_widgets()
        self.load_company_settings()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("الإعدادات العامة - نظام متابعة الشحنات")
        self.window.geometry("700x800")
        self.window.configure(bg=COLORS['background'])
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة modal
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['white'], relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="الإعدادات العامة للشركة",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(20, 30))
        
        # إنشاء دفتر الملاحظات للتبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, padx=20)
        
        # تبويب معلومات الشركة
        self.create_company_info_tab()
        
        # تبويب السنة المالية
        self.create_fiscal_year_tab()
        
        # تبويب إعدادات النظام
        self.create_system_settings_tab()
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_frame, bg=COLORS['white'])
        buttons_frame.pack(fill='x', pady=20, padx=20)
        
        self.save_button = tk.Button(
            buttons_frame,
            text="حفظ الإعدادات",
            font=(FONTS['button']['family'], FONTS['button']['size'], 'bold'),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.save_settings,
            width=20
        )
        self.save_button.pack(side='right', padx=5)
        
        cancel_button = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.window.destroy,
            width=15
        )
        cancel_button.pack(side='right', padx=5)
    
    def create_company_info_tab(self):
        """إنشاء تبويب معلومات الشركة"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="معلومات الشركة")
        
        # إطار المحتوى مع شريط تمرير
        canvas = tk.Canvas(frame, bg=COLORS['white'])
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['white'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # اسم الشركة
        self.create_field(scrollable_frame, "اسم الشركة:", "company_name_entry", required=True)
        
        # شعار الشركة
        logo_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        logo_frame.pack(fill='x', pady=10)
        
        logo_label = tk.Label(
            logo_frame,
            text="شعار الشركة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        logo_label.pack(anchor='e')
        
        logo_buttons_frame = tk.Frame(logo_frame, bg=COLORS['white'])
        logo_buttons_frame.pack(fill='x', pady=5)
        
        self.logo_button = tk.Button(
            logo_buttons_frame,
            text="اختيار شعار",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.select_logo
        )
        self.logo_button.pack(side='right', padx=5)
        
        self.logo_preview = tk.Label(
            logo_buttons_frame,
            text="لم يتم اختيار شعار",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['light'],
            relief='solid',
            bd=1,
            width=40,
            height=5
        )
        self.logo_preview.pack(side='right', padx=5)
        
        # العنوان
        self.create_field(scrollable_frame, "العنوان:", "address_entry", multiline=True)
        
        # الهاتف
        self.create_field(scrollable_frame, "الهاتف:", "phone_entry")
        
        # البريد الإلكتروني
        self.create_field(scrollable_frame, "البريد الإلكتروني:", "email_entry")
        
        # الرقم الضريبي
        self.create_field(scrollable_frame, "الرقم الضريبي:", "tax_number_entry")
        
        # العملة الافتراضية
        currency_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        currency_frame.pack(fill='x', pady=10)
        
        currency_label = tk.Label(
            currency_frame,
            text="العملة الافتراضية:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        currency_label.pack(anchor='e')
        
        currency_values = [f"{curr['code']} - {curr['name']}" for curr in CURRENCIES]
        self.currency_combo = ttk.Combobox(
            currency_frame,
            values=currency_values,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            state='readonly'
        )
        self.currency_combo.pack(fill='x', pady=5)
        
        # اللغة
        language_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        language_frame.pack(fill='x', pady=10)
        
        language_label = tk.Label(
            language_frame,
            text="اللغة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        language_label.pack(anchor='e')
        
        self.language_combo = ttk.Combobox(
            language_frame,
            values=['العربية', 'English'],
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            state='readonly'
        )
        self.language_combo.set('العربية')
        self.language_combo.pack(fill='x', pady=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_fiscal_year_tab(self):
        """إنشاء تبويب السنة المالية"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="السنة المالية")
        
        content_frame = tk.Frame(frame, bg=COLORS['white'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # بداية السنة المالية
        start_frame = tk.Frame(content_frame, bg=COLORS['white'])
        start_frame.pack(fill='x', pady=15)
        
        start_label = tk.Label(
            start_frame,
            text="بداية السنة المالية:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        start_label.pack(anchor='e')
        
        self.fiscal_year_start = DateEntry(
            start_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size'])
        )
        self.fiscal_year_start.pack(anchor='e', pady=5)
        
        # نهاية السنة المالية
        end_frame = tk.Frame(content_frame, bg=COLORS['white'])
        end_frame.pack(fill='x', pady=15)
        
        end_label = tk.Label(
            end_frame,
            text="نهاية السنة المالية:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        end_label.pack(anchor='e')
        
        self.fiscal_year_end = DateEntry(
            end_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size'])
        )
        self.fiscal_year_end.pack(anchor='e', pady=5)
        
        # ملاحظة
        note_label = tk.Label(
            content_frame,
            text="ملاحظة: تغيير السنة المالية قد يؤثر على التقارير والإحصائيات",
            font=(FONTS['arabic']['family'], 9),
            bg=COLORS['white'],
            fg=COLORS['warning'],
            wraplength=400
        )
        note_label.pack(pady=20)
    
    def create_system_settings_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        frame = tk.Frame(self.notebook, bg=COLORS['white'])
        self.notebook.add(frame, text="إعدادات النظام")
        
        content_frame = tk.Frame(frame, bg=COLORS['white'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # النسخ الاحتياطي التلقائي
        backup_frame = tk.LabelFrame(content_frame, text="النسخ الاحتياطي", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white'])
        backup_frame.pack(fill='x', pady=10)
        
        self.auto_backup_var = tk.BooleanVar(value=True)
        auto_backup_check = tk.Checkbutton(
            backup_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_var,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white']
        )
        auto_backup_check.pack(anchor='e', pady=10)
        
        # مسار النسخ الاحتياطي
        backup_path_frame = tk.Frame(backup_frame, bg=COLORS['white'])
        backup_path_frame.pack(fill='x', pady=5)
        
        backup_path_label = tk.Label(
            backup_path_frame,
            text="مسار النسخ الاحتياطي:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white']
        )
        backup_path_label.pack(anchor='e')
        
        path_entry_frame = tk.Frame(backup_path_frame, bg=COLORS['white'])
        path_entry_frame.pack(fill='x', pady=5)
        
        self.backup_path_entry = tk.Entry(
            path_entry_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            relief='solid',
            bd=1
        )
        self.backup_path_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        
        browse_button = tk.Button(
            path_entry_frame,
            text="استعراض",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.browse_backup_path
        )
        browse_button.pack(side='right')
        
        # إعدادات الأمان
        security_frame = tk.LabelFrame(content_frame, text="الأمان", font=(FONTS['arabic']['family'], FONTS['arabic']['size']), bg=COLORS['white'])
        security_frame.pack(fill='x', pady=10)
        
        # مدة انتهاء الجلسة
        session_frame = tk.Frame(security_frame, bg=COLORS['white'])
        session_frame.pack(fill='x', pady=10)
        
        session_label = tk.Label(
            session_frame,
            text="مدة انتهاء الجلسة (بالدقائق):",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white']
        )
        session_label.pack(anchor='e')
        
        self.session_timeout_entry = tk.Entry(
            session_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            relief='solid',
            bd=1,
            width=10
        )
        self.session_timeout_entry.pack(anchor='e', pady=5)
        self.session_timeout_entry.insert(0, "60")
        
        # عدد محاولات تسجيل الدخول
        attempts_frame = tk.Frame(security_frame, bg=COLORS['white'])
        attempts_frame.pack(fill='x', pady=10)
        
        attempts_label = tk.Label(
            attempts_frame,
            text="عدد محاولات تسجيل الدخول المسموحة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white']
        )
        attempts_label.pack(anchor='e')
        
        self.max_attempts_entry = tk.Entry(
            attempts_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            relief='solid',
            bd=1,
            width=10
        )
        self.max_attempts_entry.pack(anchor='e', pady=5)
        self.max_attempts_entry.insert(0, "3")
    
    def create_field(self, parent, label_text, entry_name, required=False, multiline=False):
        """إنشاء حقل إدخال"""
        field_frame = tk.Frame(parent, bg=COLORS['white'])
        field_frame.pack(fill='x', pady=10)
        
        label_text_with_star = label_text + " *" if required else label_text
        label = tk.Label(
            field_frame,
            text=label_text_with_star,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['danger'] if required else COLORS['dark']
        )
        label.pack(anchor='e')
        
        if multiline:
            entry = tk.Text(
                field_frame,
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                height=3,
                relief='solid',
                bd=1
            )
        else:
            entry = tk.Entry(
                field_frame,
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                relief='solid',
                bd=1
            )
        
        entry.pack(fill='x', pady=5)
        setattr(self, entry_name, entry)
    
    def select_logo(self):
        """اختيار شعار الشركة"""
        file_path = filedialog.askopenfilename(
            title="اختيار شعار الشركة",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                # قراءة الصورة وتحويلها لعرضها
                image = Image.open(file_path)
                image = image.resize((100, 60), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)
                
                self.logo_preview.config(image=photo, text="")
                self.logo_preview.image = photo  # الاحتفاظ بمرجع للصورة
                
                # قراءة الملف كبيانات ثنائية
                with open(file_path, 'rb') as f:
                    self.company_logo = f.read()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في قراءة الصورة: {str(e)}")
    
    def browse_backup_path(self):
        """استعراض مسار النسخ الاحتياطي"""
        folder_path = filedialog.askdirectory(title="اختيار مجلد النسخ الاحتياطي")
        if folder_path:
            self.backup_path_entry.delete(0, tk.END)
            self.backup_path_entry.insert(0, folder_path)
    
    def load_company_settings(self):
        """تحميل إعدادات الشركة الحالية"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM company_settings ORDER BY id DESC LIMIT 1')
            settings = cursor.fetchone()
            
            if settings:
                # تحميل معلومات الشركة
                if settings['company_name']:
                    self.company_name_entry.insert(0, settings['company_name'])
                
                if settings['address']:
                    self.address_entry.insert('1.0', settings['address'])
                
                if settings['phone']:
                    self.phone_entry.insert(0, settings['phone'])
                
                if settings['email']:
                    self.email_entry.insert(0, settings['email'])
                
                if settings['tax_number']:
                    self.tax_number_entry.insert(0, settings['tax_number'])
                
                if settings['currency']:
                    # البحث عن العملة في القائمة
                    for i, curr in enumerate(CURRENCIES):
                        if curr['code'] == settings['currency']:
                            self.currency_combo.current(i)
                            break
                
                if settings['language']:
                    lang_map = {'ar': 'العربية', 'en': 'English'}
                    self.language_combo.set(lang_map.get(settings['language'], 'العربية'))
                
                # تحميل السنة المالية
                if settings['fiscal_year_start']:
                    self.fiscal_year_start.set_date(datetime.strptime(settings['fiscal_year_start'], '%Y-%m-%d').date())
                
                if settings['fiscal_year_end']:
                    self.fiscal_year_end.set_date(datetime.strptime(settings['fiscal_year_end'], '%Y-%m-%d').date())
                
                # تحميل الشعار
                if settings['company_logo']:
                    try:
                        from io import BytesIO
                        image = Image.open(BytesIO(settings['company_logo']))
                        image = image.resize((100, 60), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(image)
                        
                        self.logo_preview.config(image=photo, text="")
                        self.logo_preview.image = photo
                        self.company_logo = settings['company_logo']
                    except:
                        pass
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الإعدادات: {str(e)}")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.company_name_entry.get().strip():
            messagebox.showerror("خطأ", "اسم الشركة مطلوب")
            self.company_name_entry.focus()
            return False
        
        # التحقق من البريد الإلكتروني
        email = self.email_entry.get().strip()
        if email and '@' not in email:
            messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
            self.email_entry.focus()
            return False
        
        # التحقق من السنة المالية
        start_date = self.fiscal_year_start.get_date()
        end_date = self.fiscal_year_end.get_date()
        
        if start_date >= end_date:
            messagebox.showerror("خطأ", "تاريخ بداية السنة المالية يجب أن يكون قبل تاريخ النهاية")
            return False
        
        return True
    
    def save_settings(self):
        """حفظ الإعدادات"""
        if not auth_manager.has_permission('manage_settings'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل الإعدادات")
            return
        
        if not self.validate_data():
            return
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # جمع البيانات
            company_name = self.company_name_entry.get().strip()
            address = self.address_entry.get('1.0', tk.END).strip() or None
            phone = self.phone_entry.get().strip() or None
            email = self.email_entry.get().strip() or None
            tax_number = self.tax_number_entry.get().strip() or None
            
            # العملة
            currency_text = self.currency_combo.get()
            currency = currency_text.split(' - ')[0] if currency_text else 'SAR'
            
            # اللغة
            language_map = {'العربية': 'ar', 'English': 'en'}
            language = language_map.get(self.language_combo.get(), 'ar')
            
            # السنة المالية
            fiscal_year_start = self.fiscal_year_start.get_date().strftime('%Y-%m-%d')
            fiscal_year_end = self.fiscal_year_end.get_date().strftime('%Y-%m-%d')
            
            # التحقق من وجود إعدادات سابقة
            cursor.execute('SELECT id FROM company_settings LIMIT 1')
            existing = cursor.fetchone()
            
            if existing:
                # تحديث الإعدادات الموجودة
                cursor.execute('''
                    UPDATE company_settings SET
                        company_name = ?, company_logo = ?, address = ?, phone = ?,
                        email = ?, tax_number = ?, fiscal_year_start = ?, fiscal_year_end = ?,
                        currency = ?, language = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (company_name, self.company_logo, address, phone, email, tax_number,
                      fiscal_year_start, fiscal_year_end, currency, language, existing['id']))
            else:
                # إنشاء إعدادات جديدة
                cursor.execute('''
                    INSERT INTO company_settings (
                        company_name, company_logo, address, phone, email, tax_number,
                        fiscal_year_start, fiscal_year_end, currency, language
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (company_name, self.company_logo, address, phone, email, tax_number,
                      fiscal_year_start, fiscal_year_end, currency, language))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الإعدادات: {str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة الإعدادات للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    settings_window = SettingsWindow()
    settings_window.window.mainloop()
