#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار البحث في الموردين مع دعم RTL كامل
Supplier Search Dialog with Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS
from src.simple_rtl_components import *
from database.database_manager import DatabaseManager

class SupplierSearchDialog:
    """نافذة حوار البحث في الموردين"""
    
    def __init__(self, parent, shipment_form):
        self.parent = parent
        self.shipment_form = shipment_form
        self.db_manager = DatabaseManager()
        self.suppliers_data = []
        self.filtered_suppliers = []
        self.selected_supplier = None
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات البحث
        self.search_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_suppliers_from_database()
        
        # توسيط النافذة
        self.center_window()
        
        # تركيز على حقل البحث
        self.focus_search_field()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🔍 البحث في الموردين - F9")
        self.root.geometry("800x600")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        
        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.close_dialog)
        
        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Return>', lambda e: self.select_supplier())
        self.root.bind('<Escape>', lambda e: self.close_dialog())
        self.root.bind('<F9>', lambda e: self.close_dialog())
        self.root.bind('<Double-Button-1>', lambda e: self.select_supplier())
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = create_simple_rtl_label(
            main_frame,
            text="🔍 البحث في الموردين المحفوظين",
            font=('Segoe UI', 18, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e', pady=(0, 20))
        
        # وصف
        desc_label = create_simple_rtl_label(
            main_frame,
            text="ابحث في الموردين المحفوظين مسبقاً واختر المورد المطلوب",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        desc_label.pack(anchor='e', pady=(0, 20))
        
        # منطقة البحث
        search_frame = create_simple_rtl_frame(main_frame)
        search_frame.pack(fill='x', pady=(0, 20))
        
        # البحث العام
        search_label = create_simple_rtl_label(
            search_frame,
            text="🔍 البحث العام:",
            font=('Segoe UI', 14, 'bold')
        )
        search_label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars['general'] = tk.StringVar()
        self.general_search_entry = create_simple_rtl_entry(
            search_frame,
            textvariable=self.search_vars['general'],
            placeholder="ابحث في اسم المورد أو معلومات الاتصال..."
        )
        self.general_search_entry.configure(font=('Segoe UI', 12, 'normal'))
        self.general_search_entry.pack(fill='x', ipady=10)
        
        # ربط البحث الفوري
        self.search_vars['general'].trace('w', self.on_search_change)
        
        # صف البحث المتقدم
        advanced_row = create_simple_rtl_frame(search_frame)
        advanced_row.pack(fill='x', pady=(15, 0))
        
        # البحث في الاسم
        name_frame = create_simple_rtl_frame(advanced_row)
        name_frame.pack(side='right', fill='x', expand=True, padx=(0, 10))
        
        name_label = create_simple_rtl_label(
            name_frame,
            text="🏢 اسم المورد:",
            font=('Segoe UI', 12, 'bold')
        )
        name_label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars['name'] = tk.StringVar()
        name_entry = create_simple_rtl_entry(
            name_frame,
            textvariable=self.search_vars['name'],
            placeholder="اسم المورد..."
        )
        name_entry.pack(fill='x', ipady=8)
        self.search_vars['name'].trace('w', self.on_search_change)
        
        # البحث في معلومات الاتصال
        contact_frame = create_simple_rtl_frame(advanced_row)
        contact_frame.pack(side='right', fill='x', expand=True, padx=(0, 10))
        
        contact_label = create_simple_rtl_label(
            contact_frame,
            text="📞 معلومات الاتصال:",
            font=('Segoe UI', 12, 'bold')
        )
        contact_label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars['contact'] = tk.StringVar()
        contact_entry = create_simple_rtl_entry(
            contact_frame,
            textvariable=self.search_vars['contact'],
            placeholder="هاتف، إيميل، عنوان..."
        )
        contact_entry.pack(fill='x', ipady=8)
        self.search_vars['contact'].trace('w', self.on_search_change)
        
        # البحث في البلد
        country_frame = create_simple_rtl_frame(advanced_row)
        country_frame.pack(side='right', fill='x', expand=True)
        
        country_label = create_simple_rtl_label(
            country_frame,
            text="🌍 البلد:",
            font=('Segoe UI', 12, 'bold')
        )
        country_label.pack(anchor='e', pady=(0, 5))
        
        self.search_vars['country'] = tk.StringVar()
        country_entry = create_simple_rtl_entry(
            country_frame,
            textvariable=self.search_vars['country'],
            placeholder="بلد المورد..."
        )
        country_entry.pack(fill='x', ipady=8)
        self.search_vars['country'].trace('w', self.on_search_change)
        
        # أزرار البحث
        buttons_row = create_simple_rtl_frame(search_frame)
        buttons_row.pack(fill='x', pady=(15, 0))
        
        # زر مسح
        clear_btn = create_simple_rtl_button(
            buttons_row,
            text="🗑️ مسح البحث",
            button_type="secondary",
            command=self.clear_search
        )
        clear_btn.pack(side='right', padx=5)
        
        # عداد النتائج
        self.results_label = create_simple_rtl_label(
            buttons_row,
            text="",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        self.results_label.pack(side='left', padx=10)
        
        # جدول النتائج
        results_frame = create_simple_rtl_frame(main_frame)
        results_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # عنوان النتائج
        results_title = create_simple_rtl_label(
            results_frame,
            text="📊 الموردين المتاحين:",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        results_title.pack(anchor='e', pady=(0, 10))
        
        # إنشاء جدول النتائج
        table_frame = create_simple_rtl_frame(results_frame)
        table_frame.pack(fill='both', expand=True)
        
        # تعريف أعمدة الجدول
        columns = ['name', 'contact_info', 'country', 'rating']
        column_names = ['اسم المورد', 'معلومات الاتصال', 'البلد', 'التقييم']
        
        # إنشاء Treeview
        self.results_tree = create_simple_rtl_treeview(
            table_frame,
            columns=columns,
            show='tree headings',
            height=15
        )
        
        # تكوين الأعمدة
        widths = [200, 250, 120, 100]
        for i, (col_id, col_name, width) in enumerate(zip(columns, column_names, widths)):
            self.results_tree.heading(col_id, text=col_name, anchor='e')
            self.results_tree.column(col_id, width=width, anchor='e', minwidth=50)
        
        # العمود الرئيسي
        self.results_tree.column('#0', width=30, minwidth=30, anchor='e')
        self.results_tree.heading('#0', text='#', anchor='e')
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.on_result_double_click)
        
        # إعداد ألوان الجدول
        self.setup_tree_colors()
        
        # أزرار التحكم
        control_frame = create_simple_rtl_frame(main_frame)
        control_frame.pack(fill='x', pady=(20, 0))
        
        # زر الاختيار
        select_btn = create_simple_rtl_button(
            control_frame,
            text="✅ اختيار المورد",
            button_type="success",
            command=self.select_supplier
        )
        select_btn.pack(side='right', padx=5)
        
        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            control_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_dialog
        )
        close_btn.pack(side='right', padx=5)
        
        # معلومات المساعدة
        help_label = create_simple_rtl_label(
            control_frame,
            text="💡 نصيحة: انقر مرتين على المورد لاختياره مباشرة",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        help_label.pack(side='left', padx=10)
    
    def setup_tree_colors(self):
        """إعداد ألوان الجدول"""
        try:
            self.results_tree.tag_configure('odd_row', background='#F9FAFB')
            self.results_tree.tag_configure('even_row', background='#FFFFFF')
            self.results_tree.tag_configure('selected', background='#DBEAFE')
        except Exception as e:
            print(f"خطأ في إعداد ألوان الجدول: {e}")
    
    def load_suppliers_from_database(self):
        """تحميل الموردين من قاعدة البيانات"""
        try:
            # استعلام للحصول على الموردين الفريدين
            query = """
                SELECT DISTINCT supplier_id as name,
                       '' as contact_info,
                       '' as country,
                       '⭐⭐⭐⭐⭐' as rating
                FROM shipments
                WHERE supplier_id IS NOT NULL AND supplier_id != ''
                ORDER BY supplier_id
            """

            suppliers = self.db_manager.fetch_all(query)
            self.suppliers_data = []

            if suppliers:
                for supplier in suppliers:
                    try:
                        # التأكد من أن جميع القيم نصوص
                        name = supplier.get('name', '') if supplier.get('name') is not None else ''
                        contact_info = supplier.get('contact_info', '') if supplier.get('contact_info') is not None else ''
                        country = supplier.get('country', '') if supplier.get('country') is not None else ''
                        rating = supplier.get('rating', '') if supplier.get('rating') is not None else ''

                        supplier_data = {
                            'name': str(name),
                            'contact_info': str(contact_info) if contact_info else 'غير محدد',
                            'country': str(country) if country else 'غير محدد',
                            'rating': str(rating) if rating else '⭐⭐⭐⭐⭐'
                        }
                        self.suppliers_data.append(supplier_data)
                    except Exception as supplier_error:
                        print(f"خطأ في معالجة مورد: {supplier_error}")
                        continue
            
            # إضافة موردين افتراضيين إذا لم توجد بيانات
            if not self.suppliers_data:
                default_suppliers = [
                    {
                        'name': 'شركة التجارة العالمية',
                        'contact_info': '+966501234567 - <EMAIL>',
                        'country': 'السعودية',
                        'rating': '⭐⭐⭐⭐⭐'
                    },
                    {
                        'name': 'مؤسسة الشحن السريع',
                        'contact_info': '+971501234567 - <EMAIL>',
                        'country': 'الإمارات',
                        'rating': '⭐⭐⭐⭐'
                    },
                    {
                        'name': 'شركة اللوجستيات المتقدمة',
                        'contact_info': '+20101234567 - <EMAIL>',
                        'country': 'مصر',
                        'rating': '⭐⭐⭐⭐⭐'
                    }
                ]
                self.suppliers_data = default_suppliers
            
            # تحديث القائمة المفلترة
            self.filtered_suppliers = self.suppliers_data.copy()
            self.update_results_tree()
            self.update_results_count()
            
        except Exception as e:
            print(f"خطأ في تحميل الموردين من قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)
        self._search_timer = self.root.after(300, self.perform_search)
    
    def perform_search(self):
        """تنفيذ البحث"""
        try:
            # الحصول على نصوص البحث
            general_text = self.search_vars['general'].get().strip().lower()
            name_text = self.search_vars['name'].get().strip().lower()
            contact_text = self.search_vars['contact'].get().strip().lower()
            country_text = self.search_vars['country'].get().strip().lower()

            # إذا كانت جميع الحقول فارغة، عرض جميع الموردين
            if not any([general_text, name_text, contact_text, country_text]):
                self.filtered_suppliers = self.suppliers_data.copy()
            else:
                self.filtered_suppliers = []
                for supplier in self.suppliers_data:
                    match = True

                    # البحث العام
                    if general_text:
                        supplier_name = str(supplier.get('name', '')).lower()
                        supplier_contact = str(supplier.get('contact_info', '')).lower()
                        supplier_text = f"{supplier_name} {supplier_contact}"
                        if general_text not in supplier_text:
                            match = False

                    # البحث في الاسم
                    if match and name_text:
                        supplier_name = str(supplier.get('name', '')).lower()
                        if name_text not in supplier_name:
                            match = False

                    # البحث في معلومات الاتصال
                    if match and contact_text:
                        supplier_contact = str(supplier.get('contact_info', '')).lower()
                        if contact_text not in supplier_contact:
                            match = False

                    # البحث في البلد
                    if match and country_text:
                        supplier_country = str(supplier.get('country', '')).lower()
                        if country_text not in supplier_country:
                            match = False

                    if match:
                        self.filtered_suppliers.append(supplier)

            self.update_results_tree()
            self.update_results_count()

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
    
    def clear_search(self):
        """مسح البحث"""
        for var in self.search_vars.values():
            var.set('')
        self.perform_search()
        self.focus_search_field()
    
    def update_results_tree(self):
        """تحديث جدول النتائج"""
        # مسح البيانات الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # إضافة النتائج
        for i, supplier_data in enumerate(self.filtered_suppliers):
            self.add_result_to_tree(supplier_data, i + 1)
    
    def add_result_to_tree(self, supplier_data, index):
        """إضافة نتيجة للجدول"""
        try:
            values = [
                str(supplier_data.get('name', '')),
                str(supplier_data.get('contact_info', '')),
                str(supplier_data.get('country', '')),
                str(supplier_data.get('rating', ''))
            ]

            # تحديد لون الصف
            tags = ['odd_row' if index % 2 == 1 else 'even_row']

            # إدراج العنصر
            item = self.results_tree.insert(
                '', 'end',
                text=str(index),
                values=values,
                tags=tags
            )

        except Exception as e:
            print(f"خطأ في إضافة نتيجة للجدول: {e}")
    
    def update_results_count(self):
        """تحديث عداد النتائج"""
        total_suppliers = len(self.suppliers_data)
        filtered_suppliers = len(self.filtered_suppliers)
        
        if filtered_suppliers == total_suppliers:
            self.results_label.configure(text=f"📊 إجمالي الموردين: {total_suppliers}")
        else:
            self.results_label.configure(text=f"📊 النتائج: {filtered_suppliers} من {total_suppliers}")
    
    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            values = self.results_tree.item(item, 'values')

            if values:
                # البحث عن بيانات المورد الكاملة
                supplier_name = str(values[0])
                for supplier_data in self.filtered_suppliers:
                    if str(supplier_data.get('name', '')) == supplier_name:
                        self.selected_supplier = supplier_data
                        break
    
    def on_result_double_click(self, event):
        """عند النقر المزدوج على نتيجة"""
        self.select_supplier()
    
    def select_supplier(self):
        """اختيار المورد"""
        if not self.selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد من النتائج")
            return
        
        self.close_dialog()
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.root.destroy()
    
    def focus_search_field(self):
        """تركيز على حقل البحث"""
        self.general_search_entry.focus_set()
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    
    dialog = SupplierSearchDialog(root, None)
    root.mainloop()
