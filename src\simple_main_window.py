#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية البسيطة للنظام
Simple Main Application Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from config.config import APP_CONFIG, UI_CONFIG, COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG

class SimpleMainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_main_layout()
        self.create_status_bar()
        self.update_user_info()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(APP_CONFIG['window_title'])
        
        # تعيين النافذة في وضع ملء الشاشة
        self.root.state('zoomed')  # ملء الشاشة في Windows
        
        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(UI_CONFIG['min_width'], UI_CONFIG['min_height'])
        self.root.configure(bg=COLORS.get('background', '#F5F5F5'))
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # ربط مفاتيح الاختصار
        self.setup_keyboard_shortcuts()
        
        # متغير لتتبع حالة ملء الشاشة
        self.is_fullscreen = True

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # F11 - تبديل ملء الشاشة
        self.root.bind('<F11>', self.toggle_fullscreen)
        
        # F5 - تحديث لوحة المعلومات
        self.root.bind('<F5>', self.refresh_dashboard)
        
        # Ctrl+N - شحنة جديدة
        self.root.bind('<Control-n>', lambda e: self.new_shipment())

    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.root, bg=COLORS.get('background', '#F5F5F5'))
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # الشريط العلوي
        self.header_frame = tk.Frame(
            self.main_frame,
            bg=COLORS.get('primary', '#2E86AB'),
            height=80,
            relief='raised',
            bd=2
        )
        self.header_frame.pack(fill='x', pady=(0, 10))
        self.header_frame.pack_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(
            self.header_frame,
            text=f"🚢 {APP_CONFIG['name']}",
            font=('Tahoma', 18, 'bold'),
            bg=COLORS.get('primary', '#2E86AB'),
            fg='white'
        )
        title_label.pack(side='right', padx=20, pady=20)
        
        # معلومات المستخدم
        self.user_info_label = tk.Label(
            self.header_frame,
            text="",
            font=('Tahoma', 12),
            bg=COLORS.get('primary', '#2E86AB'),
            fg='white'
        )
        self.user_info_label.pack(side='left', padx=20, pady=20)
        
        # الإطار الأوسط
        self.content_frame = tk.Frame(self.main_frame, bg=COLORS.get('background', '#F5F5F5'))
        self.content_frame.pack(fill='both', expand=True)
        
        # الشريط الجانبي
        self.sidebar_frame = tk.Frame(
            self.content_frame,
            bg=COLORS.get('sidebar', '#34495E'),
            width=250,
            relief='raised',
            bd=2
        )
        self.sidebar_frame.pack(side='right', fill='y', padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)
        
        self.create_sidebar(self.sidebar_frame)
        
        # المنطقة الرئيسية
        self.main_content_frame = tk.Frame(
            self.content_frame,
            bg='white',
            relief='raised',
            bd=2
        )
        self.main_content_frame.pack(side='left', fill='both', expand=True)
        
        self.create_dashboard()

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(
            parent,
            text="📋 القوائم الرئيسية",
            font=('Tahoma', 14, 'bold'),
            bg=COLORS.get('sidebar', '#34495E'),
            fg='white'
        )
        sidebar_title.pack(pady=20)
        
        # أزرار القوائم
        buttons = [
            ("🚢 إدارة الشحنات", self.open_shipments_window),
            ("🏢 إدارة الموردين", self.open_suppliers_window),
            ("📦 إدارة الأصناف", self.open_items_window),
            ("🏪 إدارة المخازن", self.open_warehouses_window),
            ("📊 التقارير", self.open_reports_window),
            ("⚙️ الإعدادات", self.open_settings_window)
        ]
        
        for text, command in buttons:
            btn = tk.Button(
                parent,
                text=text,
                font=('Tahoma', 11),
                bg=COLORS.get('primary', '#2E86AB'),
                fg='white',
                relief='flat',
                cursor='hand2',
                command=command,
                pady=10,
                anchor='e'
            )
            btn.pack(fill='x', padx=15, pady=5)

    def create_dashboard(self):
        """إنشاء لوحة المعلومات الرئيسية"""
        # مسح المحتوى السابق
        for widget in self.main_content_frame.winfo_children():
            widget.destroy()
        
        # عنوان لوحة المعلومات
        dashboard_title = tk.Label(
            self.main_content_frame,
            text="📊 لوحة المعلومات الرئيسية",
            font=('Tahoma', 16, 'bold'),
            bg='white',
            fg=COLORS.get('primary', '#2E86AB')
        )
        dashboard_title.pack(pady=20)
        
        # رسالة ترحيب
        welcome_label = tk.Label(
            self.main_content_frame,
            text=f"مرحباً بك في نظام متابعة الشحنات\n{datetime.now().strftime('%Y-%m-%d')}",
            font=('Tahoma', 12),
            bg='white',
            fg=COLORS.get('text_primary', '#000000'),
            justify='center'
        )
        welcome_label.pack(pady=10)
        
        # إطار الإجراءات السريعة
        actions_frame = tk.LabelFrame(
            self.main_content_frame,
            text="الإجراءات السريعة",
            font=('Tahoma', 12, 'bold'),
            bg='white',
            fg=COLORS.get('primary', '#2E86AB')
        )
        actions_frame.pack(fill='x', padx=50, pady=20)
        
        # أزرار الإجراءات السريعة
        quick_actions = [
            ("🚢 شحنة جديدة", self.new_shipment),
            ("🏢 مورد جديد", self.new_supplier),
            ("📦 صنف جديد", self.new_item),
            ("📊 عرض التقارير", self.open_reports_window)
        ]
        
        for i, (text, command) in enumerate(quick_actions):
            btn = tk.Button(
                actions_frame,
                text=text,
                font=('Tahoma', 11),
                bg=COLORS.get('success', '#28A745'),
                fg='white',
                relief='flat',
                cursor='hand2',
                command=command,
                pady=10,
                width=20
            )
            btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky='ew')
        
        # تكوين الأعمدة
        actions_frame.grid_columnconfigure(0, weight=1)
        actions_frame.grid_columnconfigure(1, weight=1)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg=COLORS.get('light', '#E9ECEF'), relief='sunken', bd=1)
        self.status_frame.pack(side='bottom', fill='x')
        
        # معلومات الحالة
        self.status_label = tk.Label(
            self.status_frame,
            text="🟢 النظام جاهز",
            font=('Tahoma', 10),
            bg=COLORS.get('light', '#E9ECEF'),
            fg=COLORS.get('text_primary', '#000000'),
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=2)
        
        # الوقت والتاريخ
        self.datetime_label = tk.Label(
            self.status_frame,
            text="",
            font=('Tahoma', 10),
            bg=COLORS.get('light', '#E9ECEF'),
            fg=COLORS.get('text_primary', '#000000'),
            anchor='e'
        )
        self.datetime_label.pack(side='right', padx=10, pady=2)
        
        # تحديث الوقت
        self.update_datetime()

    def update_user_info(self):
        """تحديث معلومات المستخدم"""
        if auth_manager.is_logged_in():
            user = auth_manager.get_current_user()
            self.user_info_label.config(text=f"👤 {user['username']}")

    def update_datetime(self):
        """تحديث الوقت والتاريخ"""
        now = datetime.now()
        self.datetime_label.config(text=now.strftime("%Y-%m-%d %H:%M:%S"))
        self.root.after(1000, self.update_datetime)

    def toggle_fullscreen(self, event=None):
        """تبديل ملء الشاشة"""
        self.is_fullscreen = not self.is_fullscreen
        if self.is_fullscreen:
            self.root.state('zoomed')
        else:
            self.root.state('normal')

    def refresh_dashboard(self, event=None):
        """تحديث لوحة المعلومات"""
        self.create_dashboard()
        self.status_label.config(text="🔄 تم تحديث لوحة المعلومات")
        self.root.after(2000, lambda: self.status_label.config(text="🟢 النظام جاهز"))

    # دوال النوافذ والإجراءات
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        try:
            from src.fullscreen_shipment_form import FullscreenShipmentForm
            form = FullscreenShipmentForm()
            form.run()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج الشحنة: {str(e)}")

    def new_supplier(self):
        """إضافة مورد جديد"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def new_item(self):
        """إضافة صنف جديد"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def open_shipments_window(self):
        """فتح نافذة إدارة الشحنات"""
        try:
            from src.simple_shipments_window import SimpleShipmentsWindow
            shipments_window = SimpleShipmentsWindow(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة الشحنات: {str(e)}")

    def open_suppliers_window(self):
        """فتح نافذة إدارة الموردين"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def open_items_window(self):
        """فتح نافذة إدارة الأصناف"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def open_warehouses_window(self):
        """فتح نافذة إدارة المخازن"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def open_reports_window(self):
        """فتح نافذة التقارير"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def on_closing(self):
        """عند إغلاق النافذة"""
        if messagebox.askokcancel("إغلاق", "هل تريد إغلاق التطبيق؟"):
            # تسجيل الخروج
            auth_manager.logout()
            self.root.destroy()

    def run(self):
        """تشغيل النافذة الرئيسية"""
        self.root.mainloop()

def show_simple_main_window():
    """إظهار النافذة الرئيسية البسيطة"""
    main_window = SimpleMainWindow()
    main_window.run()

if __name__ == "__main__":
    show_simple_main_window()
