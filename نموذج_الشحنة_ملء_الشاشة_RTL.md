# 🚢 نموذج الشحنة بملء الشاشة مع دعم RTL كامل

## 📋 نظرة عامة

تم تطوير **نموذج الشحنة بملء الشاشة** ليكون أحدث وأشمل نموذج لإدارة بيانات الشحنات مع تجربة مستخدم استثنائية. النموذج يستغل كامل مساحة الشاشة مع تصميم متقدم ودعم كامل للغة العربية واتجاه RTL.

## ✨ الميزات الرئيسية

### 🖥️ **تصميم ملء الشاشة**
- **استغلال كامل للشاشة**: النموذج يملأ الشاشة بالكامل لأقصى استفادة
- **تخطيط متقدم**: تقسيم ذكي للمساحة مع 3 مناطق رئيسية
- **تصميم تفاعلي**: واجهة حديثة مع تأثيرات بصرية متقدمة
- **تنقل سلس**: انتقال سهل بين الأقسام مع مؤثرات انتقالية

### 📊 **6 أقسام شاملة ومنظمة**

#### **1️⃣ المعلومات الأساسية**
- 🔢 **رقم الشحنة**: إنشاء تلقائي بتنسيق SH-XXXXXX
- 🏢 **المورد**: قائمة محملة من قاعدة البيانات
- 📅 **تاريخ الشحن**: مع التحقق من صحة التاريخ
- 📊 **حالة الشحنة**: 9 حالات مختلفة مع ألوان
- 🏁 **تاريخ الوصول المتوقع**: حساب تلقائي للأيام المتبقية
- ⚡ **الأولوية**: حساب تلقائي حسب القيمة والتأخير

#### **2️⃣ معلومات الشحن والنقل**
- 🚢 **ميناء المغادرة**: قائمة شاملة للموانئ العالمية
- 🏁 **ميناء الوصول**: موانئ محلية ودولية
- 🏢 **شركة الشحن**: أشهر شركات الشحن العالمية
- 📅 **تواريخ المغادرة والوصول الفعلية**
- 📋 **رقم بوليصة الشحن**: BOL-XXXXXX
- 🔍 **رقم التتبع**: TRK-XXXXXX

#### **3️⃣ معلومات الحاوية والبضائع**
- 📦 **رقم الحاوية**: تنسيق دولي معياري
- 📏 **نوع الحاوية**: 20ft, 40ft, 40ft HC, إلخ
- 🔒 **رقم الختم**: SEAL-XXXXXX
- ⚖️ **الأوزان**: إجمالي وصافي بالكيلوجرام
- 📐 **الحجم**: بالمتر المكعب
- 📊 **عدد القطع**: عدد صحيح
- 📝 **وصف البضائع**: منطقة نص كبيرة

#### **4️⃣ المعلومات المالية والتكاليف**
- 💱 **العملة**: 5 عملات رئيسية (SAR, USD, EUR, AED, KWD)
- 💰 **القيمة الإجمالية**: قيمة البضائع
- 🚢 **تكلفة الشحن**: رسوم النقل البحري
- 🛡️ **رسوم التأمين**: تأمين البضائع
- 🏛️ **رسوم الجمارك**: رسوم حكومية
- 🔧 **رسوم المناولة**: رسوم الموانئ
- 💯 **إجمالي التكلفة**: حساب تلقائي لجميع التكاليف
- 💳 **طريقة الدفع**: نقد، تحويل، اعتماد، إلخ
- 💰 **حالة الدفع**: لم يتم، جزئي، كامل، مسترد

#### **5️⃣ التتبع والحالة المتقدمة**
- 📍 **الموقع الحالي**: موقع الشحنة الحالي
- 🕐 **آخر تحديث**: تاريخ ووقت آخر تحديث
- 📊 **شريط التقدم**: نسبة مئوية مع ألوان ذكية
- ⏰ **الأيام المتبقية**: حساب تلقائي مع تحذيرات
- ⚠️ **نسبة التأخير**: مؤشر الأداء
- ⭐ **تقييم الأداء**: تقييم شامل للشحنة

#### **6️⃣ الملاحظات والمرفقات**
- 📝 **ملاحظات إضافية**: منطقة نص كبيرة
- 📎 **إضافة مرفقات**: دعم جميع أنواع الملفات
- 👁️ **عرض المرفقات**: قائمة تفاعلية
- 🗑️ **حذف مرفقات**: إدارة سهلة للملفات

### 🎨 **تصميم RTL متقدم**

#### **شريط العنوان العلوي**
- 🎯 **3 مناطق منظمة**: عنوان، تنقل، معلومات
- 👤 **معلومات المستخدم**: اسم المستخدم الحالي
- 🕐 **الوقت المباشر**: تحديث كل ثانية
- 🔄 **أزرار التنقل السريع**: 6 أزرار للأقسام

#### **المحتوى الرئيسي**
- 📱 **تخطيط متجاوب**: يتكيف مع حجم الشاشة
- 🎨 **ألوان متناسقة**: نظام ألوان احترافي
- 📝 **حقول ذكية**: تحقق فوري من البيانات
- 🔄 **حسابات تلقائية**: تحديث فوري للقيم

#### **شريط الحالة السفلي**
- 💾 **أزرار الحفظ**: حفظ، حفظ وإغلاق
- ◀▶ **أزرار التنقل**: سابق، تالي مع تعطيل ذكي
- 📊 **مؤشر القسم**: "القسم X من 6"
- ✅ **مؤشر الحالة**: حالة الحفظ والتعديل

### ⌨️ **اختصارات لوحة المفاتيح المتقدمة**

#### **الحفظ والإدارة**
- `Ctrl+S`: حفظ الشحنة
- `Ctrl+W`: إغلاق النموذج
- `Esc`: إغلاق/إلغاء

#### **التنقل بين الأقسام**
- `Ctrl+→` / `Page Down`: القسم التالي
- `Ctrl+←` / `Page Up`: القسم السابق
- `Ctrl+1`: المعلومات الأساسية
- `Ctrl+2`: معلومات الشحن
- `Ctrl+3`: معلومات الحاوية
- `Ctrl+4`: المعلومات المالية
- `Ctrl+5`: التتبع والحالة
- `Ctrl+6`: الملاحظات والمرفقات

#### **المساعدة والعرض**
- `F1`: إظهار المساعدة الشاملة
- `F11`: تبديل وضع ملء الشاشة

### 🧮 **الحسابات التلقائية الذكية**

#### **الحسابات المالية**
```
إجمالي التكلفة = القيمة الإجمالية + تكلفة الشحن + رسوم التأمين + 
                  رسوم إضافية + رسوم الجمارك + رسوم المناولة
```

#### **حساب الأولوية**
```python
نقاط الأولوية = نقاط القيمة + نقاط التأخير + نقاط الحالة

القيمة > 100,000: +3 نقاط
القيمة > 50,000:  +2 نقطة  
القيمة > 10,000:  +1 نقطة

متأخرة: +4 نقاط
قريبة (≤3 أيام): +2 نقطة

حالة حرجة: +2 نقطة
حالة عادية: +1 نقطة

النتيجة النهائية:
≥6 نقاط: 🔴 عاجل
≥4 نقاط: 🟠 عالي  
≥2 نقطة: 🟡 متوسط
≥1 نقطة: 🟢 منخفض
0 نقطة: ⚪ عادي
```

#### **حساب التقدم**
```python
نسبة التقدم حسب الحالة:
في الانتظار: 10%
مؤكدة: 25%
تم الشحن: 40%
في الطريق: 60%
وصلت: 80%
في الجمارك: 85%
تم التسليم: 100%
ملغية: 0%
متأخرة: 50%
```

## 🛠️ **التقنيات المستخدمة**

### 🐍 **Python & Tkinter**
- **Python 3.8+**: لغة البرمجة الأساسية
- **Tkinter**: مكتبة الواجهات الرسومية
- **ttk**: مكونات محسنة ومتقدمة
- **sqlite3**: قاعدة البيانات المدمجة

### 🎨 **مكونات RTL مخصصة**
- **SimpleRTLFrame**: إطارات محاذاة يمين
- **SimpleRTLLabel**: تسميات عربية محسنة  
- **SimpleRTLEntry**: حقول إدخال مع placeholder
- **SimpleRTLCombobox**: قوائم منسدلة آمنة
- **SimpleRTLText**: مناطق نص RTL متقدمة
- **SimpleRTLButton**: أزرار بـ 7 أنماط

### 🗄️ **قاعدة البيانات المحسنة**
```sql
-- جدول الشحنات الشامل (35 حقل)
CREATE TABLE shipments (
    -- المعلومات الأساسية
    id TEXT PRIMARY KEY,
    shipment_number TEXT UNIQUE NOT NULL,
    supplier_id INTEGER NOT NULL,
    status TEXT DEFAULT 'في الانتظار',
    
    -- التواريخ
    shipment_date DATE,
    expected_arrival_date DATE,
    actual_departure_date DATE,
    actual_arrival_date DATE,
    
    -- معلومات الشحن
    departure_port TEXT,
    arrival_port TEXT,
    shipping_company TEXT,
    tracking_number TEXT,
    current_location TEXT,
    
    -- معلومات الحاوية
    container_number TEXT,
    bill_of_lading TEXT,
    container_type TEXT,
    seal_number TEXT,
    weight REAL,
    volume REAL,
    pieces_count INTEGER,
    net_weight REAL,
    goods_description TEXT,
    
    -- المعلومات المالية
    currency TEXT DEFAULT 'USD',
    total_value REAL DEFAULT 0,
    shipping_cost REAL DEFAULT 0,
    additional_fees REAL DEFAULT 0,
    insurance_cost REAL DEFAULT 0,
    customs_fees REAL DEFAULT 0,
    handling_fees REAL DEFAULT 0,
    paid_amount REAL DEFAULT 0,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'لم يتم الدفع',
    
    -- معلومات إضافية
    notes TEXT,
    attachments TEXT,
    created_by TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 **كيفية الاستخدام**

### **1. فتح النموذج**
```python
# من نظام إدارة الشحنات
انقر على "➕ شحنة جديدة"
# أو
انقر مرتين على شحنة موجودة للتعديل
```

### **2. التنقل بين الأقسام**
- استخدم **الأزرار العلوية** للانتقال المباشر
- استخدم **أزرار التنقل السفلية** للتنقل التسلسلي
- استخدم **اختصارات لوحة المفاتيح** للسرعة

### **3. إدخال البيانات**
- **الحقول المطلوبة** مميزة باللون الأحمر
- **التحقق الفوري** من صحة البيانات
- **الحسابات التلقائية** تحدث فورياً
- **الحفظ التلقائي** عند التنقل بين الأقسام

### **4. إدارة المرفقات**
```
📎 إضافة ملف → اختيار الملف → تأكيد الإرفاق
👁️ عرض الملفات → قائمة بجميع المرفقات
🗑️ حذف ملف → اختيار من القائمة → تأكيد الحذف
```

### **5. الحفظ والإغلاق**
- **💾 حفظ**: حفظ البيانات والبقاء في النموذج
- **💾 حفظ وإغلاق**: حفظ البيانات وإغلاق النموذج
- **❌ إغلاق**: إغلاق مع تحذير للبيانات غير المحفوظة

## 🎯 **المميزات المتقدمة**

### **🔄 التحديث المباشر**
- تحديث الوقت كل ثانية
- حساب الأولوية فورياً عند تغيير البيانات
- تحديث شريط التقدم حسب الحالة
- حساب الأيام المتبقية تلقائياً

### **🎨 الألوان الذكية**
- ألوان الأولوية: 🔴🟠🟡🟢⚪
- ألوان التقدم: أحمر < 50%، برتقالي < 80%، أخضر ≥ 80%
- ألوان الحالة: مختلفة لكل حالة شحنة
- ألوان التحذير: للتواريخ المتأخرة والقيم الخاطئة

### **📊 التحليلات المدمجة**
- نسبة التقدم المئوية
- تقييم الأداء التلقائي
- حساب نسبة التأخير
- مؤشرات الكفاءة

## 🏆 **النتيجة النهائية**

**✅ تم تطوير نموذج الشحنة بملء الشاشة بنجاح!**

### **🌟 الإنجازات المحققة:**
- 🖥️ **تصميم ملء الشاشة** مع استغلال أمثل للمساحة
- 📊 **6 أقسام شاملة** مع 35+ حقل بيانات
- 🎨 **تصميم RTL متقدم** مع ألوان ذكية
- ⌨️ **اختصارات شاملة** لسهولة الاستخدام
- 🧮 **حسابات تلقائية** للقيم والأولوية والتقدم
- 📎 **إدارة مرفقات** متكاملة
- 🔄 **تحديث مباشر** للبيانات والحسابات
- 🛡️ **معالجة أخطاء متقدمة** مع رسائل واضحة

**النموذج جاهز للاستخدام الإنتاجي مع تجربة مستخدم استثنائية!** 🚢✨📊🎨
