#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التقارير
Reports Management System
"""

import os
import sys
from datetime import datetime, date
import sqlite3
from tkinter import messagebox, filedialog
import logging

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database_manager import DatabaseManager
from config.config import get_reports_path, REPORTS_CONFIG

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logging.warning("pandas غير متوفر - بعض وظائف التقارير قد لا تعمل")

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logging.warning("reportlab غير متوفر - تصدير PDF غير متاح")

class ReportsManager:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.reports_path = get_reports_path()
        
    def generate_shipments_report(self, start_date=None, end_date=None, supplier_id=None, status=None, format='excel'):
        """إنشاء تقرير الشحنات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # بناء الاستعلام
            query = '''
                SELECT 
                    s.shipment_number as "رقم الشحنة",
                    sup.supplier_name as "المورد",
                    s.shipment_date as "تاريخ الشحن",
                    s.expected_arrival_date as "الوصول المتوقع",
                    s.actual_arrival_date as "الوصول الفعلي",
                    s.departure_port as "ميناء المغادرة",
                    s.arrival_port as "ميناء الوصول",
                    s.shipping_company as "شركة الشحن",
                    s.container_number as "رقم الحاوية",
                    s.bill_of_lading as "بوليصة الشحن",
                    s.invoice_number as "رقم الفاتورة",
                    s.invoice_amount as "قيمة الفاتورة",
                    s.currency as "العملة",
                    s.status as "الحالة",
                    s.tracking_number as "رقم التتبع",
                    s.notes as "الملاحظات",
                    s.created_at as "تاريخ الإنشاء"
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                WHERE 1=1
            '''
            
            params = []
            
            if start_date:
                query += ' AND s.shipment_date >= ?'
                params.append(start_date)
            
            if end_date:
                query += ' AND s.shipment_date <= ?'
                params.append(end_date)
            
            if supplier_id:
                query += ' AND s.supplier_id = ?'
                params.append(supplier_id)
            
            if status:
                query += ' AND s.status = ?'
                params.append(status)
            
            query += ' ORDER BY s.shipment_date DESC'
            
            cursor.execute(query, params)
            data = cursor.fetchall()
            conn.close()
            
            if not data:
                messagebox.showinfo("معلومات", "لا توجد بيانات للتقرير")
                return None
            
            # تحويل البيانات إلى قائمة من القواميس
            columns = [description[0] for description in cursor.description]
            report_data = []
            for row in data:
                report_data.append(dict(zip(columns, row)))
            
            # إنشاء التقرير حسب التنسيق المطلوب
            if format.lower() == 'excel':
                return self._export_to_excel(report_data, "تقرير الشحنات")
            elif format.lower() == 'pdf':
                return self._export_to_pdf(report_data, "تقرير الشحنات")
            else:
                return report_data
                
        except Exception as e:
            logging.error(f"خطأ في إنشاء تقرير الشحنات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")
            return None
    
    def generate_suppliers_report(self, active_only=True, format='excel'):
        """إنشاء تقرير الموردين"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            query = '''
                SELECT 
                    supplier_code as "كود المورد",
                    supplier_name as "اسم المورد",
                    contact_person as "الشخص المسؤول",
                    email as "البريد الإلكتروني",
                    phone as "الهاتف",
                    mobile as "الجوال",
                    address as "العنوان",
                    city as "المدينة",
                    country as "البلد",
                    tax_number as "الرقم الضريبي",
                    payment_terms as "شروط الدفع",
                    credit_limit as "الحد الائتماني",
                    CASE WHEN is_active = 1 THEN 'نشط' ELSE 'غير نشط' END as "الحالة",
                    created_at as "تاريخ الإنشاء"
                FROM suppliers
            '''
            
            if active_only:
                query += ' WHERE is_active = 1'
            
            query += ' ORDER BY supplier_name'
            
            cursor.execute(query)
            data = cursor.fetchall()
            conn.close()
            
            if not data:
                messagebox.showinfo("معلومات", "لا توجد بيانات للتقرير")
                return None
            
            # تحويل البيانات
            columns = [description[0] for description in cursor.description]
            report_data = []
            for row in data:
                report_data.append(dict(zip(columns, row)))
            
            # إنشاء التقرير
            if format.lower() == 'excel':
                return self._export_to_excel(report_data, "تقرير الموردين")
            elif format.lower() == 'pdf':
                return self._export_to_pdf(report_data, "تقرير الموردين")
            else:
                return report_data
                
        except Exception as e:
            logging.error(f"خطأ في إنشاء تقرير الموردين: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")
            return None
    
    def generate_inventory_report(self, low_stock_only=False, format='excel'):
        """إنشاء تقرير المخزون"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            query = '''
                SELECT 
                    i.item_code as "كود الصنف",
                    i.item_name as "اسم الصنف",
                    c.category_name as "الفئة",
                    i.unit_of_measure as "وحدة القياس",
                    i.cost_price as "سعر التكلفة",
                    i.selling_price as "سعر البيع",
                    i.current_stock as "المخزون الحالي",
                    i.min_stock_level as "الحد الأدنى",
                    i.max_stock_level as "الحد الأقصى",
                    i.reorder_point as "نقطة إعادة الطلب",
                    CASE WHEN i.is_active = 1 THEN 'نشط' ELSE 'غير نشط' END as "الحالة",
                    CASE 
                        WHEN i.current_stock <= i.min_stock_level THEN 'نقص في المخزون'
                        WHEN i.current_stock >= i.max_stock_level THEN 'مخزون زائد'
                        ELSE 'طبيعي'
                    END as "حالة المخزون"
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
            '''
            
            if low_stock_only:
                query += ' WHERE i.current_stock <= i.min_stock_level'
            
            query += ' ORDER BY i.item_name'
            
            cursor.execute(query)
            data = cursor.fetchall()
            conn.close()
            
            if not data:
                messagebox.showinfo("معلومات", "لا توجد بيانات للتقرير")
                return None
            
            # تحويل البيانات
            columns = [description[0] for description in cursor.description]
            report_data = []
            for row in data:
                report_data.append(dict(zip(columns, row)))
            
            # إنشاء التقرير
            if format.lower() == 'excel':
                return self._export_to_excel(report_data, "تقرير المخزون")
            elif format.lower() == 'pdf':
                return self._export_to_pdf(report_data, "تقرير المخزون")
            else:
                return report_data
                
        except Exception as e:
            logging.error(f"خطأ في إنشاء تقرير المخزون: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}")
            return None
    
    def _export_to_excel(self, data, report_title):
        """تصدير البيانات إلى Excel"""
        if not PANDAS_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة pandas غير متوفرة لتصدير Excel")
            return None
        
        try:
            # إنشاء DataFrame
            df = pd.DataFrame(data)
            
            # تحديد اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_title}_{timestamp}.xlsx"
            filepath = os.path.join(self.reports_path, filename)
            
            # تصدير إلى Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=report_title, index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets[report_title]
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح:\n{filepath}")
            return filepath
            
        except Exception as e:
            logging.error(f"خطأ في تصدير Excel: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تصدير Excel: {str(e)}")
            return None
    
    def _export_to_pdf(self, data, report_title):
        """تصدير البيانات إلى PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة لتصدير PDF")
            return None
        
        try:
            # تحديد اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_title}_{timestamp}.pdf"
            filepath = os.path.join(self.reports_path, filename)
            
            # إنشاء المستند
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            
            # إضافة العنوان
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # توسيط
            )
            
            title = Paragraph(report_title, title_style)
            story.append(title)
            story.append(Spacer(1, 12))
            
            # إضافة تاريخ التقرير
            date_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            date_para = Paragraph(date_text, styles['Normal'])
            story.append(date_para)
            story.append(Spacer(1, 12))
            
            if data:
                # إنشاء الجدول
                table_data = []
                
                # إضافة العناوين
                headers = list(data[0].keys())
                table_data.append(headers)
                
                # إضافة البيانات
                for row in data:
                    table_data.append([str(row.get(col, '')) for col in headers])
                
                # إنشاء الجدول
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            
            # بناء المستند
            doc.build(story)
            
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح:\n{filepath}")
            return filepath
            
        except Exception as e:
            logging.error(f"خطأ في تصدير PDF: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")
            return None
    
    def get_shipment_statistics(self):
        """الحصول على إحصائيات الشحنات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            stats = {}
            
            # إجمالي الشحنات
            cursor.execute('SELECT COUNT(*) as total FROM shipments')
            stats['total_shipments'] = cursor.fetchone()['total']
            
            # الشحنات النشطة
            cursor.execute("SELECT COUNT(*) as active FROM shipments WHERE status NOT IN ('delivered', 'cancelled')")
            stats['active_shipments'] = cursor.fetchone()['active']
            
            # الشحنات المتأخرة
            cursor.execute('''
                SELECT COUNT(*) as delayed 
                FROM shipments 
                WHERE expected_arrival_date < date('now') 
                AND actual_arrival_date IS NULL
                AND status NOT IN ('delivered', 'cancelled')
            ''')
            stats['delayed_shipments'] = cursor.fetchone()['delayed']
            
            # إحصائيات حسب الحالة
            cursor.execute('''
                SELECT status, COUNT(*) as count 
                FROM shipments 
                GROUP BY status
            ''')
            stats['by_status'] = {row['status']: row['count'] for row in cursor.fetchall()}
            
            conn.close()
            return stats
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على إحصائيات الشحنات: {e}")
            return {}

# إنشاء مثيل من مدير التقارير
reports_manager = ReportsManager()
