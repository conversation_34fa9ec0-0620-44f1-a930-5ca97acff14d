#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط CSS متقدمة للواجهة العربية
Advanced Arabic CSS Styles with RTL Support
"""

import tkinter as tk
from tkinter import ttk

# الألوان المتقدمة للواجهة العربية
ADVANCED_COLORS = {
    # الألوان الأساسية
    'primary': '#1E3A8A',           # أزرق داكن أنيق
    'primary_light': '#3B82F6',     # أزرق فاتح
    'primary_dark': '#1E40AF',      # أزرق أغمق
    'secondary': '#059669',         # أخضر زمردي
    'secondary_light': '#10B981',   # أخضر فاتح
    'accent': '#DC2626',            # أحمر للتنبيهات
    'accent_light': '#EF4444',      # أحمر فاتح
    
    # ألوان الخلفية
    'background': '#F8FAFC',        # خلفية رئيسية فاتحة
    'background_dark': '#E2E8F0',   # خلفية داكنة قليلاً
    'surface': '#FFFFFF',           # سطح أبيض
    'surface_hover': '#F1F5F9',     # سطح عند التمرير
    'surface_selected': '#E0F2FE',  # سطح محدد
    
    # ألوان النصوص
    'text_primary': '#1F2937',      # نص أساسي داكن
    'text_secondary': '#6B7280',    # نص ثانوي رمادي
    'text_muted': '#9CA3AF',        # نص خافت
    'text_white': '#FFFFFF',        # نص أبيض
    'text_success': '#059669',      # نص نجاح أخضر
    'text_warning': '#D97706',      # نص تحذير برتقالي
    'text_error': '#DC2626',        # نص خطأ أحمر
    
    # ألوان الحدود
    'border': '#D1D5DB',            # حدود عادية
    'border_light': '#E5E7EB',      # حدود فاتحة
    'border_dark': '#9CA3AF',       # حدود داكنة
    'border_focus': '#3B82F6',      # حدود عند التركيز
    
    # ألوان الحالة
    'success': '#10B981',           # نجاح
    'warning': '#F59E0B',           # تحذير
    'error': '#EF4444',             # خطأ
    'info': '#3B82F6',              # معلومات
    
    # تدرجات
    'gradient_primary': 'linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%)',
    'gradient_secondary': 'linear-gradient(135deg, #059669 0%, #10B981 100%)',
    'gradient_surface': 'linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%)',
}

# الخطوط العربية المتقدمة
ADVANCED_FONTS = {
    # الخطوط الأساسية
    'primary': ('Segoe UI', 12, 'normal'),
    'primary_bold': ('Segoe UI', 12, 'bold'),
    'secondary': ('Tahoma', 11, 'normal'),
    'secondary_bold': ('Tahoma', 11, 'bold'),
    
    # خطوط العناوين
    'heading_large': ('Segoe UI', 18, 'bold'),
    'heading_medium': ('Segoe UI', 16, 'bold'),
    'heading_small': ('Segoe UI', 14, 'bold'),
    
    # خطوط النصوص
    'body_large': ('Segoe UI', 13, 'normal'),
    'body_medium': ('Segoe UI', 12, 'normal'),
    'body_small': ('Segoe UI', 11, 'normal'),
    
    # خطوط خاصة
    'caption': ('Segoe UI', 10, 'normal'),
    'button': ('Segoe UI', 11, 'bold'),
    'input': ('Segoe UI', 11, 'normal'),
    'table_header': ('Segoe UI', 11, 'bold'),
    'table_cell': ('Segoe UI', 10, 'normal'),
    
    # خطوط الأرقام
    'numbers': ('Consolas', 11, 'normal'),
    'numbers_bold': ('Consolas', 11, 'bold'),
}

# أبعاد ومسافات متقدمة
ADVANCED_DIMENSIONS = {
    # المسافات
    'padding_xs': 4,
    'padding_sm': 8,
    'padding_md': 12,
    'padding_lg': 16,
    'padding_xl': 20,
    'padding_2xl': 24,
    
    # الهوامش
    'margin_xs': 4,
    'margin_sm': 8,
    'margin_md': 12,
    'margin_lg': 16,
    'margin_xl': 20,
    'margin_2xl': 24,
    
    # نصف القطر للحواف
    'radius_sm': 4,
    'radius_md': 6,
    'radius_lg': 8,
    'radius_xl': 12,
    'radius_2xl': 16,
    
    # الظلال
    'shadow_sm': '0 1px 2px rgba(0, 0, 0, 0.05)',
    'shadow_md': '0 4px 6px rgba(0, 0, 0, 0.1)',
    'shadow_lg': '0 10px 15px rgba(0, 0, 0, 0.1)',
    'shadow_xl': '0 20px 25px rgba(0, 0, 0, 0.15)',
    
    # الارتفاعات
    'height_sm': 32,
    'height_md': 40,
    'height_lg': 48,
    'height_xl': 56,
    
    # العروض
    'width_sm': 120,
    'width_md': 200,
    'width_lg': 300,
    'width_xl': 400,
}

# أنماط الأزرار المتقدمة
ADVANCED_BUTTON_STYLES = {
    'primary': {
        'bg': ADVANCED_COLORS['primary'],
        'fg': ADVANCED_COLORS['text_white'],
        'activebackground': ADVANCED_COLORS['primary_dark'],
        'activeforeground': ADVANCED_COLORS['text_white'],
        'font': ADVANCED_FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'bd': 0,
    },
    'secondary': {
        'bg': ADVANCED_COLORS['secondary'],
        'fg': ADVANCED_COLORS['text_white'],
        'activebackground': ADVANCED_COLORS['secondary_light'],
        'activeforeground': ADVANCED_COLORS['text_white'],
        'font': ADVANCED_FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'bd': 0,
    },
    'outline': {
        'bg': ADVANCED_COLORS['surface'],
        'fg': ADVANCED_COLORS['primary'],
        'activebackground': ADVANCED_COLORS['surface_hover'],
        'activeforeground': ADVANCED_COLORS['primary_dark'],
        'font': ADVANCED_FONTS['button'],
        'relief': 'solid',
        'cursor': 'hand2',
        'bd': 1,
    },
    'ghost': {
        'bg': ADVANCED_COLORS['surface'],
        'fg': ADVANCED_COLORS['text_primary'],
        'activebackground': ADVANCED_COLORS['surface_hover'],
        'activeforeground': ADVANCED_COLORS['text_primary'],
        'font': ADVANCED_FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'bd': 0,
    },
    'danger': {
        'bg': ADVANCED_COLORS['error'],
        'fg': ADVANCED_COLORS['text_white'],
        'activebackground': ADVANCED_COLORS['accent'],
        'activeforeground': ADVANCED_COLORS['text_white'],
        'font': ADVANCED_FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
        'bd': 0,
    },
    'success': {
        'bg': ADVANCED_COLORS['success'],
        'fg': ADVANCED_COLORS['text_white'],
        'active_bg': ADVANCED_COLORS['secondary'],
        'active_fg': ADVANCED_COLORS['text_white'],
        'border': ADVANCED_COLORS['success'],
        'font': ADVANCED_FONTS['button'],
        'relief': 'flat',
        'cursor': 'hand2',
    }
}

# أنماط حقول الإدخال المتقدمة
ADVANCED_INPUT_STYLES = {
    'default': {
        'bg': ADVANCED_COLORS['surface'],
        'fg': ADVANCED_COLORS['text_primary'],
        'insertbackground': ADVANCED_COLORS['primary'],
        'selectbackground': ADVANCED_COLORS['surface_selected'],
        'selectforeground': ADVANCED_COLORS['text_primary'],
        'font': ADVANCED_FONTS['input'],
        'relief': 'solid',
        'bd': 1,
        'justify': 'right',
    },
    'focus': {
        'bd': 2,
    },
    'error': {
        'bd': 2,
    },
    'success': {
        'bd': 2,
    }
}

# أنماط الجداول المتقدمة
ADVANCED_TABLE_STYLES = {
    'header': {
        'bg': ADVANCED_COLORS['primary'],
        'fg': ADVANCED_COLORS['text_white'],
        'font': ADVANCED_FONTS['table_header'],
        'relief': 'flat',
    },
    'row_even': {
        'bg': ADVANCED_COLORS['surface'],
        'fg': ADVANCED_COLORS['text_primary'],
        'font': ADVANCED_FONTS['table_cell'],
    },
    'row_odd': {
        'bg': ADVANCED_COLORS['background'],
        'fg': ADVANCED_COLORS['text_primary'],
        'font': ADVANCED_FONTS['table_cell'],
    },
    'row_selected': {
        'bg': ADVANCED_COLORS['surface_selected'],
        'fg': ADVANCED_COLORS['text_primary'],
        'font': ADVANCED_FONTS['table_cell'],
    },
    'row_hover': {
        'bg': ADVANCED_COLORS['surface_hover'],
        'fg': ADVANCED_COLORS['text_primary'],
        'font': ADVANCED_FONTS['table_cell'],
    }
}

# إعدادات RTL المتقدمة
RTL_SETTINGS = {
    'text_direction': 'rtl',
    'justify': 'right',
    'anchor': 'e',
    'side': 'right',
    'pack_side': 'right',
    'grid_sticky': 'e',
    'text_align': 'right',
}

def apply_rtl_style(widget, **kwargs):
    """تطبيق نمط RTL على عنصر واجهة"""
    rtl_options = {
        'justify': 'right',
        'anchor': 'e',
    }
    
    # دمج الخيارات المخصصة
    rtl_options.update(kwargs)
    
    # تطبيق الخيارات على العنصر
    try:
        widget.configure(**rtl_options)
    except tk.TclError:
        # تجاهل الخيارات غير المدعومة
        pass

def get_rtl_pack_options(**kwargs):
    """الحصول على خيارات pack للـ RTL"""
    options = {
        'side': 'right',
        'anchor': 'e',
        'padx': (0, ADVANCED_DIMENSIONS['padding_md']),
    }
    options.update(kwargs)
    return options

def get_rtl_grid_options(**kwargs):
    """الحصول على خيارات grid للـ RTL"""
    options = {
        'sticky': 'e',
        'padx': (0, ADVANCED_DIMENSIONS['padding_md']),
    }
    options.update(kwargs)
    return options
